#!/bin/bash

# 医学数据分析平台后端启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Go环境
check_go() {
    if ! command -v go &> /dev/null; then
        print_error "Go is not installed. Please install Go 1.19 or later."
        exit 1
    fi
    
    GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
    print_info "Go version: $GO_VERSION"
}

# 检查PostgreSQL连接
check_database() {
    print_info "Checking database connection..."
    
    # 从配置文件或环境变量获取数据库URL
    if [ -z "$DATABASE_URL" ]; then
        if [ -f "config.yaml" ]; then
            print_info "Using database configuration from config.yaml"
        else
            print_warning "DATABASE_URL not set and config.yaml not found"
        fi
    else
        print_info "Using DATABASE_URL from environment"
    fi
}

# 创建必要的目录
create_directories() {
    print_info "Creating necessary directories..."
    
    mkdir -p logs
    mkdir -p exports
    mkdir -p tmp
    
    print_success "Directories created"
}

# 安装依赖
install_dependencies() {
    print_info "Installing dependencies..."
    go mod tidy
    print_success "Dependencies installed"
}

# 运行数据库迁移
run_migrations() {
    print_info "Running database migrations..."
    
    # 这里可以添加迁移逻辑
    # migrate -path migrations -database $DATABASE_URL up
    
    print_success "Database migrations completed"
}

# 构建应用
build_app() {
    print_info "Building application..."
    
    # 设置构建信息
    BUILD_TIME=$(date -u '+%Y-%m-%d_%H:%M:%S')
    GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
    VERSION=$(git describe --tags --always 2>/dev/null || echo "dev")
    
    # 构建
    go build -ldflags "-X main.Version=$VERSION -X main.BuildTime=$BUILD_TIME -X main.GitCommit=$GIT_COMMIT" -o bin/server cmd/server/main.go
    
    print_success "Application built successfully"
}

# 启动应用
start_app() {
    print_info "Starting Medical Data Platform Server..."
    
    # 设置默认环境变量
    export LOG_LEVEL=${LOG_LEVEL:-info}
    export LOG_DIR=${LOG_DIR:-logs}
    export GIN_MODE=${GIN_MODE:-debug}
    export SERVER_PORT=${SERVER_PORT:-8088}
    
    print_info "Configuration:"
    print_info "  - Log Level: $LOG_LEVEL"
    print_info "  - Log Directory: $LOG_DIR"
    print_info "  - Gin Mode: $GIN_MODE"
    print_info "  - Server Port: $SERVER_PORT"
    
    # 启动应用
    if [ -f "bin/server" ]; then
        ./bin/server
    else
        go run cmd/server/main.go
    fi
}

# 主函数
main() {
    print_info "Medical Data Platform Server Startup Script"
    print_info "==========================================="
    
    # 检查环境
    check_go
    check_database
    
    # 准备环境
    create_directories
    install_dependencies
    
    # 构建和启动
    if [ "$1" = "--build" ] || [ "$1" = "-b" ]; then
        build_app
        start_app
    elif [ "$1" = "--dev" ] || [ "$1" = "-d" ]; then
        print_info "Starting in development mode..."
        start_app
    else
        print_info "Usage: $0 [--build|-b] [--dev|-d]"
        print_info "  --build, -b: Build and start"
        print_info "  --dev, -d:   Start in development mode"
        print_info "  (no args):   Show usage"
        exit 1
    fi
}

# 信号处理
trap 'print_info "Shutting down..."; exit 0' SIGINT SIGTERM

# 运行主函数
main "$@" 