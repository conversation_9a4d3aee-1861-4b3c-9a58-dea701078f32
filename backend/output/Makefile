# Medical Data Platform Server Makefile

# 变量定义
APP_NAME = medical-data-platform-server
VERSION ?= $(shell git describe --tags --always --dirty 2>/dev/null || echo "dev")
BUILD_TIME = $(shell date -u '+%Y-%m-%d_%H:%M:%S')
GIT_COMMIT = $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")
LDFLAGS = -ldflags "-X main.Version=$(VERSION) -X main.BuildTime=$(BUILD_TIME) -X main.GitCommit=$(GIT_COMMIT)"

# Go相关变量
GOCMD = go
GOBUILD = $(GOCMD) build
GOCLEAN = $(GOCMD) clean
GOTEST = $(GOCMD) test
GOGET = $(GOCMD) get
GOMOD = $(GOCMD) mod
GOFMT = $(GOCMD) fmt

# 目录定义
BIN_DIR = bin
LOG_DIR = logs
EXPORT_DIR = exports
MIGRATION_DIR = migrations

# 默认目标
.PHONY: all
all: clean deps fmt lint test build

# 帮助信息
.PHONY: help
help:
	@echo "Medical Data Platform Server - Makefile Commands"
	@echo "================================================"
	@echo ""
	@echo "Development Commands:"
	@echo "  make dev          - Start development server"
	@echo "  make build        - Build the application"
	@echo "  make run          - Build and run the application"
	@echo "  make clean        - Clean build artifacts"
	@echo ""
	@echo "Testing Commands:"
	@echo "  make test         - Run all tests"
	@echo "  make test-unit    - Run unit tests"
	@echo "  make test-cover   - Run tests with coverage"
	@echo "  make benchmark    - Run benchmark tests"
	@echo ""
	@echo "Code Quality Commands:"
	@echo "  make fmt          - Format Go code"
	@echo "  make lint         - Run linter"
	@echo "  make vet          - Run go vet"
	@echo ""
	@echo "Dependency Commands:"
	@echo "  make deps         - Download dependencies"
	@echo "  make deps-update  - Update dependencies"
	@echo "  make deps-verify  - Verify dependencies"
	@echo ""
	@echo "Database Commands:"
	@echo "  make db-migrate   - Run database migrations"
	@echo "  make db-rollback  - Rollback database migrations"
	@echo "  make db-reset     - Reset database"
	@echo ""
	@echo "Docker Commands:"
	@echo "  make docker-build - Build Docker image"
	@echo "  make docker-run   - Run Docker container"
	@echo "  make docker-stop  - Stop Docker container"
	@echo ""
	@echo "Utility Commands:"
	@echo "  make setup        - Setup development environment"
	@echo "  make logs         - View application logs"
	@echo "  make health       - Check application health"

# 开发相关命令
.PHONY: dev
dev: setup
	@echo "Starting development server..."
	@./scripts/start.sh --dev

.PHONY: build
build: setup
	@echo "Building $(APP_NAME)..."
	@mkdir -p $(BIN_DIR)
	$(GOBUILD) $(LDFLAGS) -o $(BIN_DIR)/server cmd/server/main.go
	@echo "Build completed: $(BIN_DIR)/server"

.PHONY: run
run: build
	@echo "Running $(APP_NAME)..."
	@./$(BIN_DIR)/server

.PHONY: clean
clean:
	@echo "Cleaning build artifacts..."
	$(GOCLEAN)
	@rm -rf $(BIN_DIR)
	@rm -rf $(LOG_DIR)/*.log
	@rm -rf $(EXPORT_DIR)/*
	@echo "Clean completed"

# 测试相关命令
.PHONY: test
test:
	@echo "Running tests..."
	$(GOTEST) -v ./...

.PHONY: test-unit
test-unit:
	@echo "Running unit tests..."
	$(GOTEST) -v -short ./...

.PHONY: test-cover
test-cover:
	@echo "Running tests with coverage..."
	$(GOTEST) -v -coverprofile=coverage.out ./...
	$(GOCMD) tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

.PHONY: benchmark
benchmark:
	@echo "Running benchmark tests..."
	$(GOTEST) -bench=. -benchmem ./...

# 代码质量命令
.PHONY: fmt
fmt:
	@echo "Formatting Go code..."
	$(GOFMT) ./...

.PHONY: lint
lint:
	@echo "Running linter..."
	@if command -v golangci-lint >/dev/null 2>&1; then \
		golangci-lint run; \
	else \
		echo "golangci-lint not installed. Installing..."; \
		go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest; \
		golangci-lint run; \
	fi

.PHONY: vet
vet:
	@echo "Running go vet..."
	$(GOCMD) vet ./...

# 依赖管理命令
.PHONY: deps
deps:
	@echo "Downloading dependencies..."
	$(GOMOD) download
	$(GOMOD) tidy

.PHONY: deps-update
deps-update:
	@echo "Updating dependencies..."
	$(GOGET) -u ./...
	$(GOMOD) tidy

.PHONY: deps-verify
deps-verify:
	@echo "Verifying dependencies..."
	$(GOMOD) verify

# 数据库相关命令
.PHONY: db-migrate
db-migrate:
	@echo "Running database migrations..."
	@if command -v migrate >/dev/null 2>&1; then \
		migrate -path $(MIGRATION_DIR) -database "$(DATABASE_URL)" up; \
	else \
		echo "migrate tool not installed. Please install golang-migrate"; \
		echo "https://github.com/golang-migrate/migrate"; \
	fi

.PHONY: db-rollback
db-rollback:
	@echo "Rolling back database migrations..."
	@if command -v migrate >/dev/null 2>&1; then \
		migrate -path $(MIGRATION_DIR) -database "$(DATABASE_URL)" down 1; \
	else \
		echo "migrate tool not installed. Please install golang-migrate"; \
	fi

.PHONY: db-reset
db-reset:
	@echo "Resetting database..."
	@if command -v migrate >/dev/null 2>&1; then \
		migrate -path $(MIGRATION_DIR) -database "$(DATABASE_URL)" drop -f; \
		migrate -path $(MIGRATION_DIR) -database "$(DATABASE_URL)" up; \
	else \
		echo "migrate tool not installed. Please install golang-migrate"; \
	fi

# Docker相关命令
.PHONY: docker-build
docker-build:
	@echo "Building Docker image..."
	docker build -t $(APP_NAME):$(VERSION) .
	docker tag $(APP_NAME):$(VERSION) $(APP_NAME):latest

.PHONY: docker-run
docker-run:
	@echo "Running Docker container..."
	docker run -d --name $(APP_NAME) -p 8080:8080 $(APP_NAME):latest

.PHONY: docker-stop
docker-stop:
	@echo "Stopping Docker container..."
	docker stop $(APP_NAME) || true
	docker rm $(APP_NAME) || true

# 工具命令
.PHONY: setup
setup:
	@echo "Setting up development environment..."
	@mkdir -p $(BIN_DIR)
	@mkdir -p $(LOG_DIR)
	@mkdir -p $(EXPORT_DIR)
	@mkdir -p tmp

.PHONY: logs
logs:
	@echo "Viewing application logs..."
	@if [ -f "$(LOG_DIR)/app.log" ]; then \
		tail -f $(LOG_DIR)/app.log; \
	else \
		echo "No application logs found"; \
	fi

.PHONY: gin-logs
gin-logs:
	@echo "Viewing Gin logs..."
	@if [ -f "$(LOG_DIR)/gin.log" ]; then \
		tail -f $(LOG_DIR)/gin.log; \
	else \
		echo "No Gin logs found"; \
	fi

.PHONY: health
health:
	@echo "Checking application health..."
	@curl -s http://localhost:8080/health | jq . || echo "Application not responding"

# 安装工具
.PHONY: install-tools
install-tools:
	@echo "Installing development tools..."
	go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	go install github.com/golang-migrate/migrate/v4/cmd/migrate@latest
	@echo "Tools installed successfully"

# 版本信息
.PHONY: version
version:
	@echo "Version: $(VERSION)"
	@echo "Build Time: $(BUILD_TIME)"
	@echo "Git Commit: $(GIT_COMMIT)" 