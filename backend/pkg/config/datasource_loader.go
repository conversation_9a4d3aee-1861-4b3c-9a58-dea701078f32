package config

import (
	"fmt"
	"os"

	"github.com/chungzy/medical-data-platform/pkg/database"
	"github.com/chungzy/medical-data-platform/pkg/logger"
	"gopkg.in/yaml.v2"
)

// DataSourcesConfig 数据源配置结构
type DataSourcesConfig struct {
	DataSources map[string]database.DataSourceConfig `yaml:"datasources"`
}

// LoadDataSourcesFromConfig 从配置文件加载数据源配置
func LoadDataSourcesFromConfig(configPath string) (map[string]database.DataSourceConfig, error) {
	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	// 解析YAML配置
	var config DataSourcesConfig
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse config file: %w", err)
	}

	// 应用环境变量覆盖
	applyEnvironmentOverrides(config.DataSources)

	logger.Info("Loaded data source configurations", map[string]interface{}{
		"datasource_count": len(config.DataSources),
	})

	return config.DataSources, nil
}

// applyEnvironmentOverrides 应用环境变量覆盖配置
func applyEnvironmentOverrides(dataSources map[string]database.DataSourceConfig) {
	for id, config := range dataSources {
		// 检查环境变量覆盖
		if host := os.Getenv(fmt.Sprintf("DATASOURCE_%s_HOST", id)); host != "" {
			config.Host = host
		}
		if port := os.Getenv(fmt.Sprintf("DATASOURCE_%s_PORT", id)); port != "" {
			// 这里简化处理，实际应该转换为int
			logger.Info("Environment override for port detected", map[string]interface{}{
				"datasource_id": id,
				"port":          port,
			})
		}
		if user := os.Getenv(fmt.Sprintf("DATASOURCE_%s_USER", id)); user != "" {
			config.User = user
		}
		if password := os.Getenv(fmt.Sprintf("DATASOURCE_%s_PASSWORD", id)); password != "" {
			config.Password = password
		}
		if dbname := os.Getenv(fmt.Sprintf("DATASOURCE_%s_DBNAME", id)); dbname != "" {
			config.DBName = dbname
		}
		if enabled := os.Getenv(fmt.Sprintf("DATASOURCE_%s_ENABLED", id)); enabled != "" {
			config.Enabled = enabled == "true"
		}

		// 更新配置
		dataSources[id] = config
	}
}

// InitializeDataSourceManager 初始化数据源管理器
func InitializeDataSourceManager(configPath string) (*database.DataSourceManager, error) {
	// 加载配置
	configs, err := LoadDataSourcesFromConfig(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to load data source configs: %w", err)
	}

	// 创建数据源管理器
	dsm := database.NewDataSourceManager()

	// 添加所有数据源
	for id, config := range configs {
		if err := dsm.AddDataSource(id, config); err != nil {
			logger.Error("Failed to add data source", err, map[string]interface{}{
				"datasource_id": id,
			})
			// 继续添加其他数据源，不因为一个失败而停止
			continue
		}
	}

	// 启动健康检查例程
	dsm.StartHealthCheckRoutine(30 * 1000) // 30秒检查一次

	logger.Info("Data source manager initialized successfully", map[string]interface{}{
		"total_datasources": len(configs),
		"healthy_datasources": len(dsm.GetHealthyDataSources()),
	})

	return dsm, nil
}

// GetDataSourceStatus 获取数据源状态信息
func GetDataSourceStatus(dsm *database.DataSourceManager) map[string]interface{} {
	statuses := dsm.GetAllStatuses()
	result := make(map[string]interface{})

	var healthyCount, totalCount int
	for id, status := range statuses {
		totalCount++
		if status.IsHealthy {
			healthyCount++
		}
		result[id] = map[string]interface{}{
			"name":        status.Name,
			"isHealthy":   status.IsHealthy,
			"lastCheck":   status.LastCheck,
			"description": status.Description,
			"errorMsg":    status.ErrorMsg,
		}
	}

	result["summary"] = map[string]interface{}{
		"total":   totalCount,
		"healthy": healthyCount,
		"ratio":   float64(healthyCount) / float64(totalCount),
	}

	return result
}
