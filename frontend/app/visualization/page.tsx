"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { BarChart3, <PERSON><PERSON>hart, <PERSON><PERSON>hart, Download, Settings, TrendingUp } from "lucide-react"
import { DashboardLayout } from "@/components/dashboard-layout"

// Mock chart components (in real implementation, use recharts or similar)
function MockBarChart({ title, data }: { title: string; data: any[] }) {
  return (
    <div className="h-64 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg flex items-center justify-center">
      <div className="text-center">
        <BarChart3 className="h-12 w-12 mx-auto mb-2 text-blue-600" />
        <p className="text-sm font-medium">{title}</p>
        <p className="text-xs text-gray-500">{data.length} 数据点</p>
      </div>
    </div>
  )
}

function Mock<PERSON>ine<PERSON>hart({ title, data }: { title: string; data: any[] }) {
  return (
    <div className="h-64 bg-gradient-to-br from-green-50 to-green-100 rounded-lg flex items-center justify-center">
      <div className="text-center">
        <LineChart className="h-12 w-12 mx-auto mb-2 text-green-600" />
        <p className="text-sm font-medium">{title}</p>
        <p className="text-xs text-gray-500">{data.length} 数据点</p>
      </div>
    </div>
  )
}

function MockPieChart({ title, data }: { title: string; data: any[] }) {
  return (
    <div className="h-64 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg flex items-center justify-center">
      <div className="text-center">
        <PieChart className="h-12 w-12 mx-auto mb-2 text-purple-600" />
        <p className="text-sm font-medium">{title}</p>
        <p className="text-xs text-gray-500">{data.length} 分类</p>
      </div>
    </div>
  )
}

export default function VisualizationPage() {
  const [selectedQuery, setSelectedQuery] = useState("query1")
  const [chartType, setChartType] = useState("bar")

  const queries = [
    { value: "query1", label: "ICU患者年龄分布", type: "demographics" },
    { value: "query2", label: "心率变化趋势", type: "vitals" },
    { value: "query3", label: "诊断分类统计", type: "diagnosis" },
    { value: "query4", label: "药物使用频率", type: "medications" },
  ]

  const chartTypes = [
    { value: "bar", label: "柱状图", icon: BarChart3 },
    { value: "line", label: "折线图", icon: LineChart },
    { value: "pie", label: "饼图", icon: PieChart },
  ]

  // Mock data
  const mockData = [
    { name: "18-30岁", value: 120 },
    { name: "31-45岁", value: 280 },
    { name: "46-60岁", value: 350 },
    { name: "61-75岁", value: 420 },
    { name: "75岁以上", value: 180 },
  ]

  const renderChart = () => {
    const selectedQueryData = queries.find((q) => q.value === selectedQuery)
    if (!selectedQueryData) return null

    switch (chartType) {
      case "bar":
        return <MockBarChart title={selectedQueryData.label} data={mockData} />
      case "line":
        return <MockLineChart title={selectedQueryData.label} data={mockData} />
      case "pie":
        return <MockPieChart title={selectedQueryData.label} data={mockData} />
      default:
        return <MockBarChart title={selectedQueryData.label} data={mockData} />
    }
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">数据可视化</h1>
            <p className="text-gray-600 mt-1">将查询结果转换为直观的图表</p>
          </div>
          <div className="space-x-2">
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              图表设置
            </Button>
            <Button>
              <Download className="h-4 w-4 mr-2" />
              导出图表
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Chart Configuration */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">图表配置</CardTitle>
                <CardDescription>选择数据源和图表类型</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">数据源</label>
                  <Select value={selectedQuery} onValueChange={setSelectedQuery}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {queries.map((query) => (
                        <SelectItem key={query.value} value={query.value}>
                          {query.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">图表类型</label>
                  <div className="grid grid-cols-1 gap-2">
                    {chartTypes.map((type) => {
                      const Icon = type.icon
                      return (
                        <Button
                          key={type.value}
                          variant={chartType === type.value ? "default" : "outline"}
                          size="sm"
                          onClick={() => setChartType(type.value)}
                          className="justify-start"
                        >
                          <Icon className="h-4 w-4 mr-2" />
                          {type.label}
                        </Button>
                      )
                    })}
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">数据统计</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">总记录数</span>
                  <span className="font-medium">1,350</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">有效数据</span>
                  <span className="font-medium">1,298</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">缺失值</span>
                  <span className="font-medium">52</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">数据完整性</span>
                  <span className="font-medium text-green-600">96.1%</span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Chart Area */}
          <div className="lg:col-span-3">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>{queries.find((q) => q.value === selectedQuery)?.label}</CardTitle>
                    <CardDescription>数据可视化图表</CardDescription>
                  </div>
                  <Badge variant="secondary">{chartTypes.find((t) => t.value === chartType)?.label}</Badge>
                </div>
              </CardHeader>
              <CardContent>{renderChart()}</CardContent>
            </Card>

            {/* Chart Insights */}
            <Card className="mt-6">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <TrendingUp className="h-5 w-5" />
                  <span>数据洞察</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">61-75岁</div>
                    <p className="text-sm text-gray-600">最大年龄组</p>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">420</div>
                    <p className="text-sm text-gray-600">最高数值</p>
                  </div>
                  <div className="text-center p-4 bg-purple-50 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">31.1%</div>
                    <p className="text-sm text-gray-600">占比最高</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Recent Visualizations */}
        <Card>
          <CardHeader>
            <CardTitle>最近的可视化</CardTitle>
            <CardDescription>您最近创建的图表</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {[
                { name: "患者年龄分布", type: "柱状图", date: "2024-01-15" },
                { name: "生命体征趋势", type: "折线图", date: "2024-01-14" },
                { name: "诊断分类", type: "饼图", date: "2024-01-13" },
                { name: "药物使用统计", type: "柱状图", date: "2024-01-12" },
              ].map((viz, index) => (
                <Card key={index} className="cursor-pointer hover:shadow-md transition-shadow">
                  <CardContent className="pt-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <BarChart3 className="h-4 w-4 text-blue-600" />
                      <Badge variant="outline" className="text-xs">
                        {viz.type}
                      </Badge>
                    </div>
                    <h4 className="font-medium text-sm mb-1">{viz.name}</h4>
                    <p className="text-xs text-gray-500">{viz.date}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
