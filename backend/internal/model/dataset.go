package model

import (
	"encoding/json"
	"time"
)

type Dataset struct {
	ID           string    `json:"id" db:"id"`
	Name         string    `json:"name" db:"name"`
	Description  string    `json:"description" db:"description"`
	Version      string    `json:"version" db:"version"`
	PatientCount int       `json:"patient_count" db:"patient_count"`
	Status       string    `json:"status" db:"status"`
	LastUpdated  time.Time `json:"last_updated" db:"last_updated"`
}

type DataField struct {
	ID          string          `json:"id" db:"id"`
	DatasetID   string          `json:"dataset_id" db:"dataset_id"`
	Name        string          `json:"name" db:"name"`
	NameEn      string          `json:"name_en" db:"name_en"`
	Code        string          `json:"code" db:"code"`
	DataType    string          `json:"type" db:"data_type"`
	Category    string          `json:"category" db:"category"`
	Description string          `json:"description" db:"description"`
	Unit        *string         `json:"unit,omitempty" db:"unit"`
	ValueRange  *string         `json:"range,omitempty" db:"value_range"`
	Examples    json.RawMessage `json:"examples,omitempty" db:"examples"`
}

type DatasetTable struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	RecordCount int    `json:"record_count"`
}

type DatasetDetailResponse struct {
	ID          string         `json:"id"`
	Name        string         `json:"name"`
	Description string         `json:"description"`
	Tables      []DatasetTable `json:"tables"`
	Fields      []DataField    `json:"fields"`
}

type DatasetListResponse struct {
	Datasets []Dataset `json:"datasets"`
}

type FieldCategory struct {
	ID    string `json:"id"`
	Name  string `json:"name"`
	Count int    `json:"count"`
}

type DictionarySearchResponse struct {
	Fields []DataField `json:"fields"`
	Total  int         `json:"total"`
}

type CategoriesResponse struct {
	Categories []FieldCategory `json:"categories"`
}

// DatasetInfo 数据集信息
type DatasetInfo struct {
	ID          string      `json:"id"`
	Name        string      `json:"name"`
	Description string      `json:"description"`
	Version     string      `json:"version"`
	Schemas     []string    `json:"schemas"`
	Tables      []TableInfo `json:"tables"`
	TableCount  int         `json:"table_count"`
	Features    interface{} `json:"features"`
}

// TableInfo 表信息
type TableInfo struct {
	Name        string `json:"name"`
	Schema      string `json:"schema"`
	Description string `json:"description,omitempty"`
	RowCount    int64  `json:"row_count,omitempty"`
}

// TableDetailInfo 表详细信息
type TableDetailInfo struct {
	Name        string       `json:"name"`
	Schema      string       `json:"schema"`
	Description string       `json:"description"`
	RowCount    int64        `json:"row_count"`
	Columns     []ColumnInfo `json:"columns"`
	Indexes     []IndexInfo  `json:"indexes,omitempty"`
}

// ColumnInfo 列信息
type ColumnInfo struct {
	Name         string `json:"name"`
	DataType     string `json:"data_type"`
	IsNullable   bool   `json:"is_nullable"`
	DefaultValue string `json:"default_value,omitempty"`
	Description  string `json:"description,omitempty"`
	Position     int    `json:"position"`
}

// IndexInfo 索引信息
type IndexInfo struct {
	Name     string   `json:"name"`
	Columns  []string `json:"columns"`
	IsUnique bool     `json:"is_unique"`
}

// DatasetStats 数据集统计
type DatasetStats struct {
	DatasetID    string     `json:"dataset_id"`
	TableCount   int        `json:"table_count"`
	CacheHitRate float64    `json:"cache_hit_rate"`
	CacheSize    int        `json:"cache_size"`
	TotalQueries int        `json:"total_queries"`
	LastAccessed *time.Time `json:"last_accessed"`
}
