"use client"

import React, { use<PERSON><PERSON>, use<PERSON>ffe<PERSON> } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Checkbox } from "@/components/ui/checkbox"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Progress } from "@/components/ui/progress"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { 
  Search, 
  Users, 
  Database, 
  FileSpreadsheet, 
  TestTube,
  Heart,
  Activity,
  Pill,
  Plus, 
  Trash2, 
  Download,
  Play,
  Eye,
  Info,
  CheckCircle,
  AlertCircle,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>
} from "lucide-react"
import { toast } from "sonner"

// MIMIC数据分析工作流接口
interface MimicIndicator {
  id: string
  name: string
  nameEn: string
  code: string
  category: string
  description: string
  patientCount: number
  unit?: string
  table: string
}

interface CohortCondition {
  id: string
  category: string
  field: string
  operator: string
  value: string | number
  description: string
}

interface IndicatorExtraction {
  category: string
  type: string
  indicators: string[]
  timeRange: string
  aggregation: string
}

interface MimicQueryConfig {
  studyName: string
  cohortConditions: CohortCondition[]
  indicatorExtractions: IndicatorExtraction[]
  outputFormat: string
}

export default function MimicQueryBuilder() {
  const [activeTab, setActiveTab] = useState("indicator-search")
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [queryConfig, setQueryConfig] = useState<MimicQueryConfig>({
    studyName: "",
    cohortConditions: [],
    indicatorExtractions: [],
    outputFormat: "csv"
  })
  const [estimatedPatients, setEstimatedPatients] = useState<number | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  // 模拟MIMIC指标数据
  const mockIndicators: MimicIndicator[] = [
    {
      id: "lab_glucose",
      name: "血糖",
      nameEn: "Glucose",
      code: "50809",
      category: "laboratory",
      description: "血清葡萄糖浓度",
      patientCount: 45678,
      unit: "mg/dL",
      table: "labevents"
    },
    {
      id: "vital_hr",
      name: "心率",
      nameEn: "Heart Rate",
      code: "220045",
      category: "vitals",
      description: "每分钟心跳次数",
      patientCount: 78234,
      unit: "bpm",
      table: "chartevents"
    },
    {
      id: "lab_creatinine",
      name: "肌酐",
      nameEn: "Creatinine",
      code: "50912",
      category: "laboratory",
      description: "血清肌酐浓度",
      patientCount: 42156,
      unit: "mg/dL",
      table: "labevents"
    },
    {
      id: "vital_bp_sys",
      name: "收缩压",
      nameEn: "Systolic BP",
      code: "220050",
      category: "vitals",
      description: "收缩期血压",
      patientCount: 76543,
      unit: "mmHg",
      table: "chartevents"
    }
  ]

  const categories = [
    { id: "all", name: "全部指标", icon: Database },
    { id: "laboratory", name: "实验室指标", icon: TestTube },
    { id: "vitals", name: "生命体征", icon: Heart },
    { id: "medications", name: "药物治疗", icon: Pill },
    { id: "procedures", name: "操作手术", icon: Activity }
  ]

  // 过滤指标
  const filteredIndicators = mockIndicators.filter(indicator => {
    const matchesSearch = searchTerm === "" || 
      indicator.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      indicator.nameEn.toLowerCase().includes(searchTerm.toLowerCase()) ||
      indicator.code.includes(searchTerm)
    
    const matchesCategory = selectedCategory === "all" || indicator.category === selectedCategory
    
    return matchesSearch && matchesCategory
  })

  // 估算患者数量
  const estimatePatientCount = async () => {
    setIsLoading(true)
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      const estimated = Math.floor(Math.random() * 50000) + 10000
      setEstimatedPatients(estimated)
      toast.success(`估算完成：约 ${estimated.toLocaleString()} 名患者`)
    } catch (error) {
      toast.error("估算失败")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* 研究配置 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span>MIMIC数据分析配置</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="study-name">研究名称</Label>
              <Input
                id="study-name"
                placeholder="输入研究名称"
                value={queryConfig.studyName}
                onChange={(e) => setQueryConfig({
                  ...queryConfig,
                  studyName: e.target.value
                })}
              />
            </div>
            <div>
              <Label htmlFor="output-format">输出格式</Label>
              <Select
                value={queryConfig.outputFormat}
                onValueChange={(value) => setQueryConfig({
                  ...queryConfig,
                  outputFormat: value
                })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="csv">CSV文件</SelectItem>
                  <SelectItem value="excel">Excel文件</SelectItem>
                  <SelectItem value="json">JSON文件</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 主要功能标签页 */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="indicator-search">指标查询</TabsTrigger>
          <TabsTrigger value="cohort-definition">纳排条件</TabsTrigger>
          <TabsTrigger value="indicator-extraction">指标提取</TabsTrigger>
          <TabsTrigger value="data-summary">数据汇总</TabsTrigger>
          <TabsTrigger value="patient-count">病人数查询</TabsTrigger>
        </TabsList>

        {/* 指标查询 */}
        <TabsContent value="indicator-search" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Search className="h-5 w-5" />
                <span>MIMIC指标搜索</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* 搜索和过滤 */}
              <div className="flex space-x-4">
                <div className="flex-1">
                  <Input
                    placeholder="搜索指标名称、英文名或编码..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full"
                  />
                </div>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className="w-48">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map(category => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* 指标列表 */}
              <ScrollArea className="h-96">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {filteredIndicators.map(indicator => (
                    <Card key={indicator.id} className="cursor-pointer hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <h4 className="font-medium">{indicator.name}</h4>
                            <p className="text-sm text-gray-500">{indicator.nameEn}</p>
                          </div>
                          <Badge variant="outline">{indicator.code}</Badge>
                        </div>
                        <p className="text-sm text-gray-600 mb-2">{indicator.description}</p>
                        <div className="flex justify-between items-center">
                          <span className="text-xs text-gray-500">
                            {indicator.patientCount.toLocaleString()} 患者
                          </span>
                          <Badge variant="secondary">{indicator.unit}</Badge>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </ScrollArea>

              {filteredIndicators.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>未找到匹配的指标</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 纳排条件设置 */}
        <TabsContent value="cohort-definition" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Filter className="h-5 w-5" />
                <span>患者队列纳排条件</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* 基本条件 */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="age-min">最小年龄</Label>
                  <Input
                    id="age-min"
                    type="number"
                    placeholder="18"
                    min="0"
                    max="120"
                  />
                </div>
                <div>
                  <Label htmlFor="age-max">最大年龄</Label>
                  <Input
                    id="age-max"
                    type="number"
                    placeholder="80"
                    min="0"
                    max="120"
                  />
                </div>
                <div>
                  <Label htmlFor="gender">性别</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="选择性别" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">不限</SelectItem>
                      <SelectItem value="M">男性</SelectItem>
                      <SelectItem value="F">女性</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Separator />

              {/* 疾病条件 */}
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h4 className="font-medium">疾病/手术条件</h4>
                  <Button variant="outline" size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    添加条件
                  </Button>
                </div>

                <div className="space-y-3">
                  <Card className="p-4">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                      <div>
                        <Label>条件类型</Label>
                        <Select defaultValue="include">
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="include">纳入</SelectItem>
                            <SelectItem value="exclude">排除</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label>编码类型</Label>
                        <Select defaultValue="icd10">
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="icd9">ICD-9</SelectItem>
                            <SelectItem value="icd10">ICD-10</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label>疾病编码</Label>
                        <Input placeholder="如: I21.9" />
                      </div>
                      <div>
                        <Button variant="ghost" size="sm">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <div className="mt-2">
                      <p className="text-sm text-gray-600">急性心肌梗死，未特指</p>
                    </div>
                  </Card>
                </div>
              </div>

              <Separator />

              {/* 实时预览 */}
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex items-center space-x-2 mb-2">
                  <Info className="h-4 w-4 text-blue-600" />
                  <span className="font-medium text-blue-900">条件预览</span>
                </div>
                <p className="text-sm text-blue-800">
                  符合当前条件的预估患者数：<strong>12,345</strong> 人
                </p>
                <Button variant="outline" size="sm" className="mt-2" onClick={estimatePatientCount}>
                  重新估算
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 指标提取 */}
        <TabsContent value="indicator-extraction" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Database className="h-5 w-5" />
                <span>MIMIC指标提取</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="laboratory" className="w-full">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="laboratory">实验室指标</TabsTrigger>
                  <TabsTrigger value="vitals">生命体征</TabsTrigger>
                  <TabsTrigger value="comorbidities">合并症手术</TabsTrigger>
                  <TabsTrigger value="special">特殊指标</TabsTrigger>
                </TabsList>

                {/* 实验室指标 */}
                <TabsContent value="laboratory" className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label>提取类型</Label>
                      <Select defaultValue="icu_24h">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="icu_first">入ICU首次测量值</SelectItem>
                          <SelectItem value="icu_last">入ICU最后一次测量值</SelectItem>
                          <SelectItem value="icu_24h">入ICU24小时测量值</SelectItem>
                          <SelectItem value="admission_all">入院每一次测量值</SelectItem>
                          <SelectItem value="icu_day_n">入ICU第N天测量值</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label>聚合方式</Label>
                      <Select defaultValue="mean">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="mean">平均值</SelectItem>
                          <SelectItem value="max">最大值</SelectItem>
                          <SelectItem value="min">最小值</SelectItem>
                          <SelectItem value="first">首次值</SelectItem>
                          <SelectItem value="last">最后值</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div>
                    <Label>常用实验室指标</Label>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-2">
                      {[
                        { code: "50809", name: "血糖", nameEn: "Glucose" },
                        { code: "50912", name: "肌酐", nameEn: "Creatinine" },
                        { code: "50902", name: "氯化物", nameEn: "Chloride" },
                        { code: "50931", name: "葡萄糖", nameEn: "Glucose" },
                        { code: "50960", name: "镁", nameEn: "Magnesium" },
                        { code: "50971", name: "钾", nameEn: "Potassium" }
                      ].map(indicator => (
                        <div key={indicator.code} className="flex items-center space-x-2">
                          <Checkbox id={indicator.code} />
                          <Label htmlFor={indicator.code} className="text-sm">
                            {indicator.name} ({indicator.code})
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <Label>自定义指标编码</Label>
                    <Input placeholder="输入MIMIC指标编码，用逗号分隔" />
                  </div>
                </TabsContent>

                {/* 生命体征 */}
                <TabsContent value="vitals" className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label>提取类型</Label>
                      <Select defaultValue="icu_24h">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="icu_first">入ICU首次测量值</SelectItem>
                          <SelectItem value="icu_last">入ICU最后一次测量值</SelectItem>
                          <SelectItem value="icu_24h">入ICU24小时测量值</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label>聚合方式</Label>
                      <Select defaultValue="mean">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="mean">平均值</SelectItem>
                          <SelectItem value="max">最大值</SelectItem>
                          <SelectItem value="min">最小值</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div>
                    <Label>生命体征指标</Label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-2">
                      {[
                        { code: "220045", name: "心率", nameEn: "Heart Rate" },
                        { code: "220050", name: "收缩压", nameEn: "Systolic BP" },
                        { code: "220051", name: "舒张压", nameEn: "Diastolic BP" },
                        { code: "220052", name: "平均动脉压", nameEn: "Mean BP" },
                        { code: "220210", name: "呼吸频率", nameEn: "Respiratory Rate" },
                        { code: "223761", name: "体温", nameEn: "Temperature" }
                      ].map(indicator => (
                        <div key={indicator.code} className="flex items-center space-x-2">
                          <Checkbox id={indicator.code} />
                          <Label htmlFor={indicator.code} className="text-sm">
                            {indicator.name} ({indicator.code})
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                </TabsContent>

                {/* 合并症和手术史 */}
                <TabsContent value="comorbidities" className="space-y-4">
                  <Alert>
                    <Info className="h-4 w-4" />
                    <AlertDescription>
                      基于ICD编码提取患者的疾病诊断和手术记录
                    </AlertDescription>
                  </Alert>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label>编码类型</Label>
                      <Select defaultValue="icd10">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="icd9">ICD-9</SelectItem>
                          <SelectItem value="icd10">ICD-10</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label>匹配方式</Label>
                      <Select defaultValue="exact">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="exact">精确匹配</SelectItem>
                          <SelectItem value="prefix">前缀匹配</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div>
                    <Label>ICD编码</Label>
                    <Input placeholder="输入ICD编码，如: I21.9, I25.1" />
                  </div>
                </TabsContent>

                {/* 特殊指标 */}
                <TabsContent value="special" className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {[
                      { id: "lvef", name: "左室射血分数 (LVEF)", description: "超声心动图测量的LVEF值" },
                      { id: "medication", name: "给药数据", description: "患者用药记录和剂量" },
                      { id: "oxygenation", name: "氧合指数", description: "PaO2/FiO2比值" },
                      { id: "ventilation", name: "机械通气", description: "呼吸机使用记录" }
                    ].map(indicator => (
                      <Card key={indicator.id} className="p-4">
                        <div className="flex items-center space-x-2 mb-2">
                          <Checkbox id={indicator.id} />
                          <Label htmlFor={indicator.id} className="font-medium">
                            {indicator.name}
                          </Label>
                        </div>
                        <p className="text-sm text-gray-600">{indicator.description}</p>
                      </Card>
                    ))}
                  </div>
                </TabsContent>
              </Tabs>

              <div className="flex justify-end space-x-2 mt-6">
                <Button variant="outline">
                  <Eye className="h-4 w-4 mr-2" />
                  预览提取
                </Button>
                <Button>
                  <Play className="h-4 w-4 mr-2" />
                  开始提取
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 数据汇总 */}
        <TabsContent value="data-summary" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <FileSpreadsheet className="h-5 w-5" />
                <span>数据汇总合并</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* 文件上传区域 */}
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                <div className="space-y-4">
                  <div className="mx-auto w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <FileSpreadsheet className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-medium">上传数据文件</h3>
                    <p className="text-gray-500">
                      拖拽ZIP文件到此处，或点击选择文件
                    </p>
                  </div>
                  <Button variant="outline">
                    选择文件
                  </Button>
                </div>
              </div>

              {/* 文件格式说明 */}
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  <strong>支持的文件格式：</strong>
                  <ul className="mt-2 space-y-1">
                    <li>• ZIP文件包含多个CSV文件</li>
                    <li>• 每个CSV文件必须包含患者ID列</li>
                    <li>• 患者ID列名应为 "subject_id" 或 "patient_id"</li>
                  </ul>
                </AlertDescription>
              </Alert>

              {/* 处理状态 */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="font-medium">处理进度</span>
                  <span className="text-sm text-gray-500">0/0 文件</span>
                </div>
                <Progress value={0} className="w-full" />
              </div>

              {/* 汇总选项 */}
              <div className="space-y-4">
                <h4 className="font-medium">汇总选项</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="remove-duplicates" defaultChecked />
                    <Label htmlFor="remove-duplicates">移除重复患者</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="validate-ids" defaultChecked />
                    <Label htmlFor="validate-ids">验证患者ID一致性</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="add-source" />
                    <Label htmlFor="add-source">添加数据来源列</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="sort-by-id" defaultChecked />
                    <Label htmlFor="sort-by-id">按患者ID排序</Label>
                  </div>
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex justify-between">
                <Button variant="outline">
                  <Eye className="h-4 w-4 mr-2" />
                  预览结果
                </Button>
                <Button>
                  <Download className="h-4 w-4 mr-2" />
                  下载汇总数据
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="patient-count">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Users className="h-5 w-5" />
                <span>病人数查询</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-4">
                <Button onClick={estimatePatientCount} disabled={isLoading}>
                  {isLoading ? "估算中..." : "估算患者数量"}
                </Button>
                {estimatedPatients && (
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>约 {estimatedPatients.toLocaleString()} 名患者</span>
                  </div>
                )}
              </div>
              {isLoading && <Progress value={33} className="w-full" />}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
