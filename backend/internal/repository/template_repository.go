package repository

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/chungzy/medical-data-platform/internal/model"

	"github.com/google/uuid"
)

type templateRepository struct {
	db *sql.DB
}

func NewTemplateRepository(db *sql.DB) TemplateRepository {
	return &templateRepository{db: db}
}

func (r *templateRepository) Create(ctx context.Context, template *model.QueryTemplate) error {
	query := `
		INSERT INTO query_templates (id, user_id, name, description, category, dataset_id, template_config, is_public, usage_count, rating, author, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
	`

	_, err := r.db.ExecContext(ctx, query,
		template.ID, template.UserID, template.Name, template.Description,
		template.Category, template.DatasetID, template.TemplateConfig,
		template.IsPublic, template.UsageCount, template.Rating,
		template.Author, template.CreatedAt, template.UpdatedAt,
	)
	return err
}

func (r *templateRepository) GetAll(ctx context.Context, category, dataset string, limit, offset int) ([]model.QueryTemplate, int, error) {
	var whereConditions []string
	var args []interface{}
	argIndex := 1

	// 构建WHERE条件
	whereConditions = append(whereConditions, "is_public = true") // 只返回公开模板

	if category != "" {
		whereConditions = append(whereConditions, fmt.Sprintf("category = $%d", argIndex))
		args = append(args, category)
		argIndex++
	}

	if dataset != "" {
		whereConditions = append(whereConditions, fmt.Sprintf("dataset_id = $%d", argIndex))
		args = append(args, dataset)
		argIndex++
	}

	whereClause := "WHERE " + strings.Join(whereConditions, " AND ")

	// 查询数据
	dataQuery := fmt.Sprintf(`
		SELECT id, user_id, name, description, category, dataset_id, template_config, is_public, usage_count, rating, author, created_at, updated_at
		FROM query_templates %s
		ORDER BY usage_count DESC, created_at DESC
		LIMIT $%d OFFSET $%d
	`, whereClause, argIndex, argIndex+1)

	args = append(args, limit, offset)

	rows, err := r.db.QueryContext(ctx, dataQuery, args...)
	if err != nil {
		return nil, 0, err
	}
	defer rows.Close()

	var templates []model.QueryTemplate
	for rows.Next() {
		var template model.QueryTemplate

		err := rows.Scan(
			&template.ID, &template.UserID, &template.Name, &template.Description,
			&template.Category, &template.DatasetID, &template.TemplateConfig,
			&template.IsPublic, &template.UsageCount, &template.Rating,
			&template.Author, &template.CreatedAt, &template.UpdatedAt,
		)
		if err != nil {
			return nil, 0, err
		}

		templates = append(templates, template)
	}

	// 查询总数
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM query_templates %s", whereClause)
	var total int
	err = r.db.QueryRowContext(ctx, countQuery, args[:len(args)-2]...).Scan(&total)
	if err != nil {
		return nil, 0, err
	}

	return templates, total, nil
}

func (r *templateRepository) GetByID(ctx context.Context, id uuid.UUID) (*model.QueryTemplate, error) {
	query := `
		SELECT id, user_id, name, description, category, dataset_id, template_config, is_public, usage_count, rating, author, created_at, updated_at
		FROM query_templates WHERE id = $1
	`

	template := &model.QueryTemplate{}
	err := r.db.QueryRowContext(ctx, query, id).Scan(
		&template.ID, &template.UserID, &template.Name, &template.Description,
		&template.Category, &template.DatasetID, &template.TemplateConfig,
		&template.IsPublic, &template.UsageCount, &template.Rating,
		&template.Author, &template.CreatedAt, &template.UpdatedAt,
	)
	if err != nil {
		return nil, err
	}

	return template, nil
}

func (r *templateRepository) GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.QueryTemplate, int, error) {
	query := `
		SELECT id, user_id, name, description, category, dataset_id, template_config, is_public, usage_count, rating, author, created_at, updated_at
		FROM query_templates WHERE user_id = $1
		ORDER BY created_at DESC
		LIMIT $2 OFFSET $3
	`

	rows, err := r.db.QueryContext(ctx, query, userID, limit, offset)
	if err != nil {
		return nil, 0, err
	}
	defer rows.Close()

	var templates []model.QueryTemplate
	for rows.Next() {
		var template model.QueryTemplate

		err := rows.Scan(
			&template.ID, &template.UserID, &template.Name, &template.Description,
			&template.Category, &template.DatasetID, &template.TemplateConfig,
			&template.IsPublic, &template.UsageCount, &template.Rating,
			&template.Author, &template.CreatedAt, &template.UpdatedAt,
		)
		if err != nil {
			return nil, 0, err
		}

		templates = append(templates, template)
	}

	// 查询总数
	countQuery := `SELECT COUNT(*) FROM query_templates WHERE user_id = $1`
	var total int
	err = r.db.QueryRowContext(ctx, countQuery, userID).Scan(&total)
	if err != nil {
		return nil, 0, err
	}

	return templates, total, nil
}

func (r *templateRepository) Update(ctx context.Context, template *model.QueryTemplate) error {
	query := `
		UPDATE query_templates SET name = $2, description = $3, category = $4, 
		dataset_id = $5, template_config = $6, is_public = $7, updated_at = $8
		WHERE id = $1
	`

	_, err := r.db.ExecContext(ctx, query,
		template.ID, template.Name, template.Description, template.Category,
		template.DatasetID, template.TemplateConfig, template.IsPublic,
		time.Now(),
	)
	return err
}

func (r *templateRepository) Delete(ctx context.Context, id uuid.UUID) error {
	query := `DELETE FROM query_templates WHERE id = $1`
	_, err := r.db.ExecContext(ctx, query, id)
	return err
}

func (r *templateRepository) IncrementUsage(ctx context.Context, id uuid.UUID) error {
	query := `UPDATE query_templates SET usage_count = usage_count + 1 WHERE id = $1`
	_, err := r.db.ExecContext(ctx, query, id)
	return err
}
