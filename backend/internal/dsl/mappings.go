package dsl

// getTableMappings 获取数据集的表映射
func getTableMappings(dataset string) map[string]string {
	switch dataset {
	case "mimic_iv":
		return map[string]string{
			"patients":    "mimiciv_hosp.patients",
			"admissions":  "mimiciv_hosp.admissions",
			"icustays":    "mimiciv_icu.icustays",
			"chartevents": "mimiciv_icu.chartevents",
			"labevents":   "mimiciv_hosp.labevents",
			"diagnoses":   "mimiciv_hosp.diagnoses_icd",
			"procedures":  "mimiciv_hosp.procedures_icd",
		}
	case "eicu":
		return map[string]string{
			"patient":       "eicu_crd.patient",
			"admissiondx":   "eicu_crd.admissiondx",
			"apacheaps":     "eicu_crd.apacheaps",
			"apachepredvar": "eicu_crd.apachepredvar",
			"diagnosis":     "eicu_crd.diagnosis",
			"lab":           "eicu_crd.lab",
			"medication":    "eicu_crd.medication",
		}
	case "nhanes":
		return map[string]string{
			"demographics":  "nhanes.demographics",
			"examination":   "nhanes.examination",
			"laboratory":    "nhanes.laboratory",
			"questionnaire": "nhanes.questionnaire",
		}
	case "pic":
		return map[string]string{
			"patients":  "pic.patients",
			"events":    "pic.events",
			"diagnoses": "pic.diagnoses",
		}
	default:
		return map[string]string{}
	}
}

// getFieldMappings 获取数据集的字段映射
func getFieldMappings(dataset string) map[string]string {
	switch dataset {
	case "mimic_iv":
		return map[string]string{
			"patient_id":     "subject_id",
			"admission_id":   "hadm_id",
			"stay_id":        "stay_id",
			"age":            "anchor_age",
			"gender":         "gender",
			"admission_time": "admittime",
			"discharge_time": "dischtime",
			"death_time":     "deathtime",
			"admission_type": "admission_type",
			"diagnosis":      "long_title",
			"icd_code":       "icd_code",
			"lab_value":      "valuenum",
			"lab_item":       "label",
		}
	case "eicu":
		return map[string]string{
			"patient_id":       "patientunitstayid",
			"hospital_id":      "hospitalid",
			"age":              "age",
			"gender":           "gender",
			"admission_weight": "admissionweight",
			"discharge_status": "unitdischargestatus",
			"apache_score":     "apachescore",
			"predicted_death":  "predictedhospitalmortality",
			"diagnosis":        "diagnosisstring",
			"lab_name":         "labname",
			"lab_result":       "labresult",
		}
	case "nhanes":
		return map[string]string{
			"respondent_id":            "seqn",
			"age":                      "ridageyr",
			"gender":                   "riagendr",
			"race":                     "ridreth3",
			"education":                "dmdeduc2",
			"income":                   "indhhin2",
			"bmi":                      "bmxbmi",
			"weight":                   "bmxwt",
			"height":                   "bmxht",
			"blood_pressure_systolic":  "bpxsy1",
			"blood_pressure_diastolic": "bpxdi1",
		}
	case "pic":
		return map[string]string{
			"patient_id": "patient_id",
			"event_time": "event_time",
			"event_type": "event_type",
			"value":      "value",
			"unit":       "unit",
		}
	default:
		return map[string]string{}
	}
}
