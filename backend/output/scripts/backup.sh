#!/bin/bash

# Medical Data Platform - Database Backup Script
# 医疗数据平台数据库备份脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 配置变量
BACKUP_DIR="/backups"
RETENTION_DAYS=${BACKUP_RETENTION_DAYS:-7}
TIMESTAMP=$(date '+%Y%m%d_%H%M%S')
BACKUP_FILE="medical_platform_backup_${TIMESTAMP}.sql"
BACKUP_PATH="${BACKUP_DIR}/${BACKUP_FILE}"

# 数据库连接信息
DB_HOST=${PGHOST:-postgres}
DB_PORT=${PGPORT:-5432}
DB_USER=${PGUSER:-medical_user}
DB_NAME=${PGDATABASE:-medical_data_platform}
DB_PASSWORD=${PGPASSWORD}

# 压缩设置
COMPRESS=${BACKUP_COMPRESS:-true}
COMPRESS_LEVEL=${BACKUP_COMPRESS_LEVEL:-6}

# 显示帮助信息
show_help() {
    echo "Medical Data Platform - Database Backup Script"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help              显示帮助信息"
    echo "  -f, --full              完整备份（包含数据和结构）"
    echo "  -s, --schema-only       仅备份数据库结构"
    echo "  -d, --data-only         仅备份数据"
    echo "  -t, --tables TABLES     备份指定表（逗号分隔）"
    echo "  -o, --output FILE       指定输出文件名"
    echo "  --no-compress           不压缩备份文件"
    echo "  --retention DAYS        备份保留天数（默认7天）"
    echo ""
    echo "Environment Variables:"
    echo "  PGHOST                  数据库主机（默认：postgres）"
    echo "  PGPORT                  数据库端口（默认：5432）"
    echo "  PGUSER                  数据库用户（默认：medical_user）"
    echo "  PGDATABASE              数据库名称（默认：medical_data_platform）"
    echo "  PGPASSWORD              数据库密码"
    echo "  BACKUP_RETENTION_DAYS   备份保留天数（默认：7）"
    echo "  BACKUP_COMPRESS         是否压缩（默认：true）"
    echo "  BACKUP_COMPRESS_LEVEL   压缩级别（默认：6）"
    echo ""
    echo "Examples:"
    echo "  $0                      # 完整备份"
    echo "  $0 -s                   # 仅备份结构"
    echo "  $0 -d                   # 仅备份数据"
    echo "  $0 -t users,datasets    # 备份指定表"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v pg_dump >/dev/null 2>&1; then
        log_error "pg_dump 未找到，请安装 PostgreSQL 客户端"
        exit 1
    fi
    
    if [[ "$COMPRESS" == "true" ]] && ! command -v gzip >/dev/null 2>&1; then
        log_warning "gzip 未找到，将不压缩备份文件"
        COMPRESS=false
    fi
    
    log_success "依赖检查完成"
}

# 检查数据库连接
check_database_connection() {
    log_info "检查数据库连接..."
    
    if [[ -z "$DB_PASSWORD" ]]; then
        log_error "数据库密码未设置（PGPASSWORD环境变量）"
        exit 1
    fi
    
    export PGPASSWORD="$DB_PASSWORD"
    
    if ! pg_isready -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" >/dev/null 2>&1; then
        log_error "无法连接到数据库 $DB_HOST:$DB_PORT/$DB_NAME"
        exit 1
    fi
    
    log_success "数据库连接正常"
}

# 创建备份目录
create_backup_directory() {
    log_info "创建备份目录..."
    
    if [[ ! -d "$BACKUP_DIR" ]]; then
        mkdir -p "$BACKUP_DIR"
        log_info "创建备份目录: $BACKUP_DIR"
    fi
    
    if [[ ! -w "$BACKUP_DIR" ]]; then
        log_error "备份目录不可写: $BACKUP_DIR"
        exit 1
    fi
    
    log_success "备份目录准备完成"
}

# 执行备份
perform_backup() {
    local backup_type=$1
    local tables=$2
    local output_file=$3
    
    log_info "开始数据库备份..."
    log_info "备份类型: $backup_type"
    log_info "输出文件: $output_file"
    
    # 构建 pg_dump 命令
    local pg_dump_cmd="pg_dump -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME"
    
    case $backup_type in
        "schema-only")
            pg_dump_cmd="$pg_dump_cmd --schema-only"
            ;;
        "data-only")
            pg_dump_cmd="$pg_dump_cmd --data-only"
            ;;
        "tables")
            if [[ -n "$tables" ]]; then
                IFS=',' read -ra TABLE_ARRAY <<< "$tables"
                for table in "${TABLE_ARRAY[@]}"; do
                    pg_dump_cmd="$pg_dump_cmd --table=$table"
                done
            fi
            ;;
        "full"|*)
            # 完整备份，不需要额外参数
            ;;
    esac
    
    # 添加通用选项
    pg_dump_cmd="$pg_dump_cmd --verbose --no-password --create --clean --if-exists"
    
    # 执行备份
    local start_time=$(date +%s)
    
    if [[ "$COMPRESS" == "true" ]]; then
        log_info "执行压缩备份..."
        if eval "$pg_dump_cmd" | gzip -$COMPRESS_LEVEL > "${output_file}.gz"; then
            output_file="${output_file}.gz"
        else
            log_error "备份失败"
            return 1
        fi
    else
        log_info "执行备份..."
        if ! eval "$pg_dump_cmd" > "$output_file"; then
            log_error "备份失败"
            return 1
        fi
    fi
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    # 检查备份文件
    if [[ ! -f "$output_file" ]]; then
        log_error "备份文件未生成: $output_file"
        return 1
    fi
    
    local file_size=$(du -h "$output_file" | cut -f1)
    log_success "备份完成"
    log_info "备份文件: $output_file"
    log_info "文件大小: $file_size"
    log_info "耗时: ${duration}秒"
    
    return 0
}

# 验证备份文件
verify_backup() {
    local backup_file=$1
    
    log_info "验证备份文件..."
    
    if [[ ! -f "$backup_file" ]]; then
        log_error "备份文件不存在: $backup_file"
        return 1
    fi
    
    # 检查文件大小
    local file_size=$(stat -f%z "$backup_file" 2>/dev/null || stat -c%s "$backup_file" 2>/dev/null || echo "0")
    if [[ "$file_size" -lt 1024 ]]; then
        log_warning "备份文件可能过小: ${file_size} bytes"
    fi
    
    # 检查压缩文件完整性
    if [[ "$backup_file" == *.gz ]]; then
        if ! gzip -t "$backup_file" >/dev/null 2>&1; then
            log_error "压缩文件损坏: $backup_file"
            return 1
        fi
    fi
    
    log_success "备份文件验证通过"
    return 0
}

# 清理旧备份
cleanup_old_backups() {
    log_info "清理旧备份文件..."
    
    local deleted_count=0
    
    # 查找并删除超过保留期的备份文件
    while IFS= read -r -d '' file; do
        if [[ -f "$file" ]]; then
            rm -f "$file"
            log_info "删除旧备份: $(basename "$file")"
            ((deleted_count++))
        fi
    done < <(find "$BACKUP_DIR" -name "medical_platform_backup_*.sql*" -type f -mtime +$RETENTION_DAYS -print0 2>/dev/null)
    
    if [[ $deleted_count -gt 0 ]]; then
        log_success "清理完成，删除了 $deleted_count 个旧备份文件"
    else
        log_info "没有需要清理的旧备份文件"
    fi
}

# 发送通知（可扩展）
send_notification() {
    local status=$1
    local message=$2
    
    # 这里可以添加邮件、Slack、钉钉等通知逻辑
    log_info "通知: $status - $message"
    
    # 示例：写入系统日志
    logger -t "medical-platform-backup" "$status: $message"
}

# 主函数
main() {
    local backup_type="full"
    local tables=""
    local output_file=""
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -f|--full)
                backup_type="full"
                shift
                ;;
            -s|--schema-only)
                backup_type="schema-only"
                shift
                ;;
            -d|--data-only)
                backup_type="data-only"
                shift
                ;;
            -t|--tables)
                backup_type="tables"
                tables="$2"
                shift 2
                ;;
            -o|--output)
                output_file="$2"
                shift 2
                ;;
            --no-compress)
                COMPRESS=false
                shift
                ;;
            --retention)
                RETENTION_DAYS="$2"
                if ! [[ "$RETENTION_DAYS" =~ ^[0-9]+$ ]]; then
                    log_error "保留天数必须是数字"
                    exit 1
                fi
                shift 2
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 设置输出文件
    if [[ -z "$output_file" ]]; then
        case $backup_type in
            "schema-only")
                output_file="${BACKUP_DIR}/medical_platform_schema_${TIMESTAMP}.sql"
                ;;
            "data-only")
                output_file="${BACKUP_DIR}/medical_platform_data_${TIMESTAMP}.sql"
                ;;
            "tables")
                output_file="${BACKUP_DIR}/medical_platform_tables_${TIMESTAMP}.sql"
                ;;
            *)
                output_file="$BACKUP_PATH"
                ;;
        esac
    else
        # 如果指定了输出文件，使用绝对路径
        if [[ "$output_file" != /* ]]; then
            output_file="${BACKUP_DIR}/$output_file"
        fi
    fi
    
    # 显示备份信息
    echo "========================================"
    echo "  医疗数据平台数据库备份"
    echo "========================================"
    echo "时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "数据库: $DB_HOST:$DB_PORT/$DB_NAME"
    echo "备份类型: $backup_type"
    echo "输出文件: $output_file"
    echo "压缩: $(if [[ "$COMPRESS" == "true" ]]; then echo "是"; else echo "否"; fi)"
    echo "保留天数: $RETENTION_DAYS"
    echo "========================================"
    
    # 执行备份流程
    local backup_success=false
    
    if check_dependencies && \
       check_database_connection && \
       create_backup_directory && \
       perform_backup "$backup_type" "$tables" "$output_file" && \
       verify_backup "$output_file"; then
        backup_success=true
    fi
    
    if [[ "$backup_success" == "true" ]]; then
        cleanup_old_backups
        send_notification "SUCCESS" "数据库备份成功: $(basename "$output_file")"
        log_success "数据库备份流程完成"
        exit 0
    else
        send_notification "FAILED" "数据库备份失败"
        log_error "数据库备份流程失败"
        exit 1
    fi
}

# 信号处理
trap 'log_error "备份脚本被中断"; exit 1' INT TERM

# 执行主函数
main "$@" 