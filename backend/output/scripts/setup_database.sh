#!/bin/bash

# 医学数据分析平台数据库设置脚本
# Medical Data Platform Database Setup Script

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
DB_NAME="medical_data_platform"
DB_USER="medical_w"
DB_PASSWORD="medical_password_2024"
POSTGRES_USER="apple"

echo -e "${BLUE}=== 医学数据分析平台数据库设置 ===${NC}"
echo ""

# 检查PostgreSQL是否安装
check_postgresql() {
    echo -e "${YELLOW}检查PostgreSQL安装状态...${NC}"
    
    if command -v psql >/dev/null 2>&1; then
        echo -e "${GREEN}✓ PostgreSQL已安装${NC}"
        psql --version
    else
        echo -e "${RED}✗ PostgreSQL未安装${NC}"
        echo ""
        echo "请先安装PostgreSQL："
        echo "  macOS: brew install postgresql"
        echo "  Ubuntu: sudo apt-get install postgresql postgresql-contrib"
        echo "  CentOS: sudo yum install postgresql postgresql-server"
        exit 1
    fi
    echo ""
}

# 检查PostgreSQL服务状态
check_postgresql_service() {
    echo -e "${YELLOW}检查PostgreSQL服务状态...${NC}"
    
    # macOS (Homebrew)
    if [[ "$OSTYPE" == "darwin"* ]]; then
        if brew services list | grep postgresql | grep started >/dev/null 2>&1; then
            echo -e "${GREEN}✓ PostgreSQL服务正在运行${NC}"
        else
            echo -e "${YELLOW}启动PostgreSQL服务...${NC}"
            brew services start postgresql
            sleep 2
        fi
    # Linux
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        if systemctl is-active --quiet postgresql; then
            echo -e "${GREEN}✓ PostgreSQL服务正在运行${NC}"
        else
            echo -e "${YELLOW}启动PostgreSQL服务...${NC}"
            sudo systemctl start postgresql
            sudo systemctl enable postgresql
        fi
    fi
    echo ""
}

# 创建数据库用户
create_database_user() {
    echo -e "${YELLOW}创建数据库用户...${NC}"
    
    # 检查用户是否已存在
    if psql -U $POSTGRES_USER -d postgres -tAc "SELECT 1 FROM pg_roles WHERE rolname='$DB_USER'" | grep -q 1; then
        echo -e "${GREEN}✓ 用户 $DB_USER 已存在${NC}"
    else
        echo -e "${BLUE}创建用户 $DB_USER...${NC}"
        psql -U $POSTGRES_USER -d postgres -c "CREATE USER $DB_USER WITH PASSWORD '$DB_PASSWORD';"
        psql -U $POSTGRES_USER -d postgres -c "ALTER USER $DB_USER CREATEDB;"
        echo -e "${GREEN}✓ 用户 $DB_USER 创建成功${NC}"
    fi
    echo ""
}

# 创建数据库
create_database() {
    echo -e "${YELLOW}创建数据库...${NC}"
    
    # 检查数据库是否已存在
    if psql -U $POSTGRES_USER -d postgres -lqt | cut -d \| -f 1 | grep -qw $DB_NAME; then
        echo -e "${GREEN}✓ 数据库 $DB_NAME 已存在${NC}"
    else
        echo -e "${BLUE}创建数据库 $DB_NAME...${NC}"
        psql -U $POSTGRES_USER -d postgres -c "CREATE DATABASE $DB_NAME OWNER $DB_USER;"
        echo -e "${GREEN}✓ 数据库 $DB_NAME 创建成功${NC}"
    fi
    echo ""
}

# 执行数据库迁移
run_migrations() {
    echo -e "${YELLOW}执行数据库迁移...${NC}"
    
    if [ -f "migrations/001_create_tables.sql" ]; then
        echo -e "${BLUE}执行建表语句...${NC}"
        psql -U $DB_USER -d $DB_NAME -f migrations/001_create_tables.sql
        echo -e "${GREEN}✓ 建表语句执行成功${NC}"
    else
        echo -e "${RED}✗ 找不到迁移文件 migrations/001_create_tables.sql${NC}"
    fi
    
    if [ -f "migrations/002_sample_data.sql" ]; then
        echo -e "${BLUE}插入示例数据...${NC}"
        psql -U $DB_USER -d $DB_NAME -f migrations/002_sample_data.sql
        echo -e "${GREEN}✓ 示例数据插入成功${NC}"
    else
        echo -e "${YELLOW}⚠ 找不到示例数据文件 migrations/002_sample_data.sql${NC}"
    fi
    echo ""
}

# 测试数据库连接
test_connection() {
    echo -e "${YELLOW}测试数据库连接...${NC}"
    
    if psql -U $DB_USER -d $DB_NAME -c "SELECT version();" >/dev/null 2>&1; then
        echo -e "${GREEN}✓ 数据库连接测试成功${NC}"
        echo -e "${BLUE}数据库信息：${NC}"
        psql -U $DB_USER -d $DB_NAME -c "SELECT version();"
    else
        echo -e "${RED}✗ 数据库连接测试失败${NC}"
        exit 1
    fi
    echo ""
}

# 生成配置文件
generate_config() {
    echo -e "${YELLOW}生成配置文件...${NC}"
    
    if [ ! -f "config.yaml" ]; then
        if [ -f "config.example.yaml" ]; then
            cp config.example.yaml config.yaml
            
            # 更新数据库配置
            sed -i.bak "s/user: \"postgres\"/user: \"$DB_USER\"/" config.yaml
            sed -i.bak "s/password: \"your_password_here\"/password: \"$DB_PASSWORD\"/" config.yaml
            rm config.yaml.bak
            
            echo -e "${GREEN}✓ 配置文件 config.yaml 已生成${NC}"
        else
            echo -e "${RED}✗ 找不到配置模板文件 config.example.yaml${NC}"
        fi
    else
        echo -e "${YELLOW}⚠ 配置文件 config.yaml 已存在，跳过生成${NC}"
    fi
    echo ""
}

# 显示连接信息
show_connection_info() {
    echo -e "${GREEN}=== 数据库设置完成 ===${NC}"
    echo ""
    echo -e "${BLUE}数据库连接信息：${NC}"
    echo "  主机: localhost"
    echo "  端口: 5432"
    echo "  数据库: $DB_NAME"
    echo "  用户: $DB_USER"
    echo "  密码: $DB_PASSWORD"
    echo ""
    echo -e "${BLUE}连接命令：${NC}"
    echo "  psql -U $DB_USER -d $DB_NAME"
    echo ""
    echo -e "${BLUE}配置文件：${NC}"
    echo "  config.yaml 已更新数据库配置"
    echo ""
    echo -e "${GREEN}现在可以启动应用程序：${NC}"
    echo "  go run cmd/server/main.go"
    echo "  或"
    echo "  ./medical-platform"
}

# 主函数
main() {
    echo "此脚本将为医学数据分析平台设置PostgreSQL数据库"
    echo ""
    read -p "是否继续? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "操作已取消"
        exit 0
    fi
    echo ""
    
    #check_postgresql
    check_postgresql_service
    create_database_user
    create_database
    run_migrations
    test_connection
    generate_config
    show_connection_info
}

# 错误处理
trap 'echo -e "\n${RED}脚本执行出错，请检查上述错误信息${NC}"' ERR

# 执行主函数
main "$@" 