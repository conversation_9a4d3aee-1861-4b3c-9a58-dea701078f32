#!/bin/bash

# Medical Data Platform Server - Config Script (Deployment Package)
# 医疗数据平台服务器配置脚本（部署包）

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
APP_ROOT="$(dirname "$SCRIPT_DIR")"

# 显示帮助
show_help() {
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  init            初始化配置文件"
    echo "  check           检查配置"
    echo "  backup          备份配置"
    echo "  restore FILE    恢复配置"
    echo ""
    echo "Options:"
    echo "  -h, --help      显示帮助"
}

# 初始化配置
init_config() {
    log_info "初始化配置文件..."

    local config_file="$APP_ROOT/configs/config.yaml"
    local example_file="$APP_ROOT/configs/config.example.yaml"

    if [[ -f "$config_file" ]]; then
        log_warning "配置文件已存在: $config_file"
        read -p "是否覆盖? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "取消初始化"
            return 0
        fi
    fi

    if [[ -f "$example_file" ]]; then
        cp "$example_file" "$config_file"
        log_success "配置文件已创建: $config_file"
        log_info "请编辑配置文件以适应您的环境"
    else
        log_error "示例配置文件不存在: $example_file"
        return 1
    fi
}

# 检查配置
check_config() {
    log_info "检查配置..."

    local config_file="$APP_ROOT/configs/config.yaml"

    if [[ ! -f "$config_file" ]]; then
        log_error "配置文件不存在: $config_file"
        log_info "运行 '$0 init' 来创建配置文件"
        return 1
    fi

    log_success "配置文件存在: $config_file"

    # 这里可以添加更多的配置验证逻辑
    log_info "配置检查完成"
}

# 备份配置
backup_config() {
    log_info "备份配置文件..."

    local config_file="$APP_ROOT/configs/config.yaml"
    local backup_file="$APP_ROOT/configs/config.backup.$(date +%Y%m%d_%H%M%S).yaml"

    if [[ ! -f "$config_file" ]]; then
        log_error "配置文件不存在: $config_file"
        return 1
    fi

    cp "$config_file" "$backup_file"
    log_success "配置已备份到: $backup_file"
}

# 恢复配置
restore_config() {
    local backup_file=$1

    if [[ -z "$backup_file" ]]; then
        log_error "请指定备份文件"
        return 1
    fi

    if [[ ! -f "$backup_file" ]]; then
        log_error "备份文件不存在: $backup_file"
        return 1
    fi

    local config_file="$APP_ROOT/configs/config.yaml"

    log_info "恢复配置文件..."
    cp "$backup_file" "$config_file"
    log_success "配置已恢复: $config_file"
}

# 主函数
main() {
    if [[ $# -eq 0 ]]; then
        show_help
        exit 1
    fi

    case $1 in
        init)
            init_config
            ;;
        check)
            check_config
            ;;
        backup)
            backup_config
            ;;
        restore)
            restore_config "$2"
            ;;
        -h|--help)
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

main "$@"
