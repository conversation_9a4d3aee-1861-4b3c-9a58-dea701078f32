"use client"

import { DashboardLayout } from "@/components/dashboard-layout"
import MimicQueryBuilder from "@/components/query-builder/mimic-query-builder"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Database, Users, FileSpreadsheet, Activity } from "lucide-react"

export default function MimicAnalysisPage() {
  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* 页面标题和说明 */}
        <div>
          <h1 className="text-3xl font-bold text-gray-900">MIMIC数据分析平台</h1>
          <p className="text-gray-600 mt-2">
            无需编写SQL代码，轻松提取和分析MIMIC-IV重症监护数据库中的医疗数据
          </p>
        </div>

        {/* 数据库概览 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Users className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">患者总数</p>
                  <p className="text-xl font-bold">382,278</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Activity className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">ICU住院次数</p>
                  <p className="text-xl font-bold">76,540</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <FileSpreadsheet className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">实验室记录</p>
                  <p className="text-xl font-bold">122M+</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <Database className="h-5 w-5 text-orange-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">数据版本</p>
                  <p className="text-xl font-bold">MIMIC-IV v2.2</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 功能特性 */}
        <Card>
          <CardHeader>
            <CardTitle>平台特性</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <h4 className="font-medium">🔍 智能指标搜索</h4>
                <p className="text-sm text-gray-600">
                  支持中英文关键词搜索，快速定位所需的医学指标和编码
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">👥 灵活队列定义</h4>
                <p className="text-sm text-gray-600">
                  通过年龄、性别、疾病等条件灵活定义研究队列
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">📊 多维数据提取</h4>
                <p className="text-sm text-gray-600">
                  支持实验室指标、生命体征、合并症等多种数据类型提取
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">⏱️ 时间范围控制</h4>
                <p className="text-sm text-gray-600">
                  精确控制数据提取的时间范围，如入ICU24小时内数据
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">🔄 数据汇总合并</h4>
                <p className="text-sm text-gray-600">
                  自动合并多个提取结果，生成完整的分析数据集
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">📈 实时患者统计</h4>
                <p className="text-sm text-gray-600">
                  实时显示符合条件的患者数量，帮助评估研究可行性
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 支持的数据类型 */}
        <Card>
          <CardHeader>
            <CardTitle>支持的数据类型</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {[
                "实验室检验", "生命体征", "用药记录", "诊断编码", 
                "手术记录", "重症评分", "机械通气", "血管活性药物",
                "液体平衡", "微生物培养", "影像检查", "护理记录"
              ].map(type => (
                <Badge key={type} variant="secondary">{type}</Badge>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 主要查询构建器 */}
        <MimicQueryBuilder />

        {/* 使用说明 */}
        <Card>
          <CardHeader>
            <CardTitle>使用流程</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-sm font-medium text-blue-600">1</span>
                </div>
                <div>
                  <h4 className="font-medium">指标查询</h4>
                  <p className="text-sm text-gray-600">搜索和选择需要分析的医学指标</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-sm font-medium text-blue-600">2</span>
                </div>
                <div>
                  <h4 className="font-medium">纳排条件</h4>
                  <p className="text-sm text-gray-600">定义研究队列的纳入和排除标准</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-sm font-medium text-blue-600">3</span>
                </div>
                <div>
                  <h4 className="font-medium">指标提取</h4>
                  <p className="text-sm text-gray-600">配置数据提取参数并执行提取</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-sm font-medium text-blue-600">4</span>
                </div>
                <div>
                  <h4 className="font-medium">数据汇总</h4>
                  <p className="text-sm text-gray-600">合并多个提取结果为完整数据集</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
