"use client"

import React, { use<PERSON>tate, useEffect } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Users,
  Database,
  FileSpreadsheet,
  Code,
  Plus,
  Trash2,
  Download,
  Play,
  Eye,
  Settings,
  Link
} from "lucide-react"
import { Medical<PERSON>ueryResponse } from "@/lib/api/medical-query"

interface Field {
  id: string
  name: string
  nameEn: string
  type: string
  category: string
  description: string
  table: string
}

interface Table {
  name: string
  description: string
  recordCount: number
  fields: Field[]
}

interface QueryCondition {
  field: string
  operator: string
  value: string
  logic?: 'AND' | 'OR'
}

interface TableJoin {
  table: string
  type: 'INNER' | 'LEFT' | 'RIGHT' | 'FULL'
  on: string
  alias?: string
}

interface QueryConfig {
  dataset: string
  mainTable: string
  cohortConditions: QueryCondition[]
  joins: TableJoin[]
  selectedFields: Field[]
  additionalConditions: QueryCondition[]
  limit: number
}

interface QueryResult extends MedicalQueryResponse {
  fields?: Field[]
}

export default function EnhancedQueryBuilder() {
  const [dataset, setDataset] = useState("")
  const [datasets, setDatasets] = useState([])
  const [tables, setTables] = useState<Table[]>([])
  const [queryConfig, setQueryConfig] = useState<QueryConfig>({
    dataset: "",
    mainTable: "",
    cohortConditions: [],
    joins: [],
    selectedFields: [],
    additionalConditions: [],
    limit: 1000
  })
  const [generatedSQL, setGeneratedSQL] = useState("")
  const [activeTab, setActiveTab] = useState("cohort")
  const [queryResults, setQueryResults] = useState<QueryResult | null>(null)
  const [loading, setLoading] = useState(false)

  // 获取数据集列表
  useEffect(() => {
    fetchDatasets()
  }, [])

  // 当选择数据集时获取表信息
  useEffect(() => {
    if (dataset) {
      fetchTables(dataset)
    }
  }, [dataset])

  const fetchDatasets = async () => {
    // API调用获取数据集
    // setDatasets(response.data)
  }

  const fetchTables = async (datasetId: string) => {
    // API调用获取表信息
    // setTables(response.data)
  }

  // 1. 人群筛选器组件
  const CohortFilter = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          人群筛选器
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Label>主表选择</Label>
          <Select value={queryConfig.mainTable} onValueChange={(value) => 
            setQueryConfig(prev => ({ ...prev, mainTable: value }))
          }>
            <SelectTrigger>
              <SelectValue placeholder="选择主表（如患者表）" />
            </SelectTrigger>
            <SelectContent>
              {tables.map(table => (
                <SelectItem key={table.name} value={table.name}>
                  {table.name} - {table.description}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div>
          <div className="flex items-center justify-between mb-2">
            <Label>筛选条件</Label>
            <Button 
              size="sm" 
              variant="outline"
              onClick={() => addCohortCondition()}
            >
              <Plus className="h-4 w-4 mr-1" />
              添加条件
            </Button>
          </div>
          
          <div className="space-y-2">
            {queryConfig.cohortConditions.map((condition, index) => (
              <ConditionRow 
                key={index}
                condition={condition}
                onUpdate={(updated) => updateCohortCondition(index, updated)}
                onRemove={() => removeCohortCondition(index)}
                showLogic={index > 0}
              />
            ))}
          </div>
        </div>

        <Alert>
          <Users className="h-4 w-4" />
          <AlertDescription>
            人群筛选器将作为子查询，先筛选出符合条件的患者ID列表，然后用于后续表关联
          </AlertDescription>
        </Alert>
      </CardContent>
    </Card>
  )

  // 2. 表关联器组件
  const TableJoiner = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Link className="h-5 w-5" />
          表关联设置
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <Label>表关联配置</Label>
          <Button 
            size="sm" 
            variant="outline"
            onClick={() => addTableJoin()}
          >
            <Plus className="h-4 w-4 mr-1" />
            添加关联
          </Button>
        </div>

        <div className="space-y-3">
          {queryConfig.joins.map((join, index) => (
            <JoinRow
              key={index}
              join={join}
              tables={tables}
              onUpdate={(updated) => updateTableJoin(index, updated)}
              onRemove={() => removeTableJoin(index)}
            />
          ))}
        </div>

        {queryConfig.joins.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <Database className="h-12 w-12 mx-auto mb-2 opacity-50" />
            <p>尚未配置表关联</p>
            <p className="text-sm">点击"添加关联"开始配置多表查询</p>
          </div>
        )}
      </CardContent>
    </Card>
  )

  // 3. 字段选择器组件  
  const FieldSelector = () => (
    <Card>
      <CardHeader>
        <CardTitle>字段选择器</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* 可用字段 */}
          <div>
            <Label className="mb-2 block">可用字段</Label>
            <ScrollArea className="h-96 border rounded-md p-2">
              {tables.map(table => (
                <div key={table.name} className="mb-4">
                  <h4 className="font-medium mb-2">{table.name}</h4>
                  {table.fields?.map(field => (
                    <div key={field.id} className="flex items-center space-x-2 mb-1">
                      <Checkbox 
                        checked={queryConfig.selectedFields.some(f => f.id === field.id)}
                        onCheckedChange={() => toggleField(field)}
                      />
                      <span className="text-sm">{field.name}</span>
                    </div>
                  ))}
                </div>
              ))}
            </ScrollArea>
          </div>

          {/* 已选择字段 */}
          <div>
            <Label className="mb-2 block">已选择字段 ({queryConfig.selectedFields.length})</Label>
            <ScrollArea className="h-96 border rounded-md p-2">
              {queryConfig.selectedFields.map(field => (
                <div key={field.id} className="flex items-center justify-between mb-2 p-2 bg-muted rounded">
                  <div>
                    <span className="font-medium">{field.name}</span>
                    <Badge variant="secondary" className="ml-2">{field.table}</Badge>
                  </div>
                  <Button 
                    size="sm" 
                    variant="ghost"
                    onClick={() => removeField(field)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </ScrollArea>
          </div>
        </div>
      </CardContent>
    </Card>
  )

  // 4. SQL预览和执行组件
  const SQLPreview = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Code className="h-5 w-5" />
          SQL预览与执行
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <Button onClick={generateSQL} variant="outline">
            <Code className="h-4 w-4 mr-2" />
            生成SQL
          </Button>
          <Button onClick={executeQuery} disabled={!generatedSQL || loading}>
            <Play className="h-4 w-4 mr-2" />
            {loading ? "执行中..." : "执行查询"}
          </Button>
          <Button onClick={previewResults} variant="outline" disabled={!queryResults}>
            <Eye className="h-4 w-4 mr-2" />
            预览结果
          </Button>
        </div>

        {generatedSQL && (
          <div>
            <Label>生成的SQL查询</Label>
            <Textarea 
              value={generatedSQL}
              onChange={(e) => setGeneratedSQL(e.target.value)}
              className="font-mono text-sm mt-2"
              rows={8}
            />
          </div>
        )}

        {queryResults && (
          <div>
            <Label>查询统计</Label>
            <div className="grid grid-cols-3 gap-4 mt-2">
              <div className="p-3 bg-muted rounded">
                <div className="text-2xl font-bold">{queryResults.total}</div>
                <div className="text-sm text-muted-foreground">总记录数</div>
              </div>
              <div className="p-3 bg-muted rounded">
                <div className="text-2xl font-bold">{queryResults.fields?.length || 0}</div>
                <div className="text-sm text-muted-foreground">字段数</div>
              </div>
              <div className="p-3 bg-muted rounded">
                <div className="text-2xl font-bold">{queryResults.executionTime}ms</div>
                <div className="text-sm text-muted-foreground">执行时间</div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )

  // 5. 导出组件
  const ExportSection = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileSpreadsheet className="h-5 w-5" />
          数据导出
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label>导出格式</Label>
            <Select defaultValue="excel">
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="excel">Excel (.xlsx)</SelectItem>
                <SelectItem value="csv">CSV (.csv)</SelectItem>
                <SelectItem value="json">JSON (.json)</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label>记录限制</Label>
            <Input 
              type="number"
              value={queryConfig.limit}
              onChange={(e) => setQueryConfig(prev => ({ ...prev, limit: parseInt(e.target.value) }))}
              placeholder="1000"
            />
          </div>
        </div>

        <Button 
          onClick={exportData} 
          disabled={!queryResults}
          className="w-full"
        >
          <Download className="h-4 w-4 mr-2" />
          导出数据
        </Button>

        <Alert>
          <FileSpreadsheet className="h-4 w-4" />
          <AlertDescription>
            导出将包含所有选择的字段和筛选后的数据。大数据集将使用异步导出。
          </AlertDescription>
        </Alert>
      </CardContent>
    </Card>
  )

  // 工具函数
  const addCohortCondition = () => {
    setQueryConfig(prev => ({
      ...prev,
      cohortConditions: [...prev.cohortConditions, {
        field: "",
        operator: "=",
        value: "",
        logic: prev.cohortConditions.length > 0 ? "AND" : undefined
      }]
    }))
  }

  const updateCohortCondition = (index: number, condition: QueryCondition) => {
    setQueryConfig(prev => ({
      ...prev,
      cohortConditions: prev.cohortConditions.map((c, i) => i === index ? condition : c)
    }))
  }

  const removeCohortCondition = (index: number) => {
    setQueryConfig(prev => ({
      ...prev,
      cohortConditions: prev.cohortConditions.filter((_, i) => i !== index)
    }))
  }

  const addTableJoin = () => {
    setQueryConfig(prev => ({
      ...prev,
      joins: [...prev.joins, {
        table: "",
        type: "LEFT",
        on: "",
        alias: ""
      }]
    }))
  }

  const updateTableJoin = (index: number, join: TableJoin) => {
    setQueryConfig(prev => ({
      ...prev,
      joins: prev.joins.map((j, i) => i === index ? join : j)
    }))
  }

  const removeTableJoin = (index: number) => {
    setQueryConfig(prev => ({
      ...prev,
      joins: prev.joins.filter((_, i) => i !== index)
    }))
  }

  const toggleField = (field: Field) => {
    setQueryConfig(prev => ({
      ...prev,
      selectedFields: prev.selectedFields.some(f => f.id === field.id)
        ? prev.selectedFields.filter(f => f.id !== field.id)
        : [...prev.selectedFields, field]
    }))
  }

  const removeField = (field: Field) => {
    setQueryConfig(prev => ({
      ...prev,
      selectedFields: prev.selectedFields.filter(f => f.id !== field.id)
    }))
  }

  const generateSQL = async () => {
    // 调用后端API生成SQL
    try {
      // const response = await apiClient.generateSQL(queryConfig)
      // setGeneratedSQL(response.data.sql)
      
      // 临时示例SQL
      setGeneratedSQL(`
WITH cohort AS (
  SELECT DISTINCT subject_id 
  FROM patients 
  WHERE ${queryConfig.cohortConditions.map(c => `${c.field} ${c.operator} '${c.value}'`).join(' AND ')}
)
SELECT ${queryConfig.selectedFields.map(f => `${f.table}.${f.nameEn}`).join(', ')}
FROM cohort c
${queryConfig.joins.map(j => `${j.type} JOIN ${j.table} ON ${j.on}`).join('\n')}
LIMIT ${queryConfig.limit};
      `.trim())
    } catch (error) {
      console.error("SQL生成失败:", error)
    }
  }

  const executeQuery = async () => {
    setLoading(true)
    try {
      // const response = await apiClient.executeQuery({ sql: generatedSQL })
      // setQueryResults(response.data)
      
      // 临时模拟结果
      setTimeout(() => {
        setQueryResults({
          queryId: "mock-query-" + Date.now(),
          studyName: "Enhanced Query Result",
          total: 1250,
          fields: queryConfig.selectedFields,
          executionTime: 350,
          status: "completed",
          data: [],
          metadata: {}
        })
        setLoading(false)
      }, 2000)
    } catch (error) {
      console.error("查询执行失败:", error)
      setLoading(false)
    }
  }

  const exportData = async () => {
    try {
      // const response = await apiClient.exportData({ queryId: queryResults.queryId })
      // window.open(response.data.downloadUrl)
      alert("导出功能开发中...")
    } catch (error) {
      console.error("数据导出失败:", error)
    }
  }

  const previewResults = () => {
    // 打开结果预览对话框
    alert("结果预览功能开发中...")
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold">增强型查询构建器</h2>
          <p className="text-muted-foreground">构建复杂的多表联合查询，筛选特定人群并导出数据</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Settings className="h-4 w-4 mr-2" />
            设置
          </Button>
        </div>
      </div>

      {/* 数据集选择 */}
      <Card>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label>选择数据集</Label>
              <Select value={dataset} onValueChange={setDataset}>
                <SelectTrigger>
                  <SelectValue placeholder="选择要查询的数据集" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="mimic_iv">MIMIC-IV</SelectItem>
                  <SelectItem value="eicu">eICU-CRD</SelectItem>
                  <SelectItem value="nhanes">NHANES</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {dataset && (
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="cohort">人群筛选</TabsTrigger>
            <TabsTrigger value="joins">表关联</TabsTrigger>
            <TabsTrigger value="fields">字段选择</TabsTrigger>
            <TabsTrigger value="execute">执行导出</TabsTrigger>
          </TabsList>

          <TabsContent value="cohort" className="space-y-4">
            <CohortFilter />
          </TabsContent>

          <TabsContent value="joins" className="space-y-4">
            <TableJoiner />
          </TabsContent>

          <TabsContent value="fields" className="space-y-4">
            <FieldSelector />
          </TabsContent>

          <TabsContent value="execute" className="space-y-4">
            <SQLPreview />
            <ExportSection />
          </TabsContent>
        </Tabs>
      )}
    </div>
  )
}

// 条件行组件
function ConditionRow({ 
  condition, 
  onUpdate, 
  onRemove, 
  showLogic = false 
}: {
  condition: QueryCondition
  onUpdate: (condition: QueryCondition) => void
  onRemove: () => void
  showLogic?: boolean
}) {
  return (
    <div className="flex items-center gap-2 p-2 border rounded">
      {showLogic && (
        <Select 
          value={condition.logic} 
          onValueChange={(value) => onUpdate({ ...condition, logic: value as 'AND' | 'OR' })}
        >
          <SelectTrigger className="w-20">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="AND">AND</SelectItem>
            <SelectItem value="OR">OR</SelectItem>
          </SelectContent>
        </Select>
      )}
      
      <Select value={condition.field} onValueChange={(value) => onUpdate({ ...condition, field: value })}>
        <SelectTrigger className="flex-1">
          <SelectValue placeholder="选择字段" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="age">年龄</SelectItem>
          <SelectItem value="gender">性别</SelectItem>
          <SelectItem value="admission_type">入院类型</SelectItem>
        </SelectContent>
      </Select>

      <Select value={condition.operator} onValueChange={(value) => onUpdate({ ...condition, operator: value })}>
        <SelectTrigger className="w-24">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="=">=</SelectItem>
          <SelectItem value="!=">!=</SelectItem>
          <SelectItem value=">">&gt;</SelectItem>
          <SelectItem value=">=">&gt;=</SelectItem>
          <SelectItem value="<">&lt;</SelectItem>
          <SelectItem value="<=">&lt;=</SelectItem>
          <SelectItem value="LIKE">包含</SelectItem>
        </SelectContent>
      </Select>

      <Input 
        value={condition.value}
        onChange={(e) => onUpdate({ ...condition, value: e.target.value })}
        placeholder="值"
        className="flex-1"
      />

      <Button size="sm" variant="ghost" onClick={onRemove}>
        <Trash2 className="h-4 w-4" />
      </Button>
    </div>
  )
}

// 表关联行组件
function JoinRow({ 
  join, 
  tables, 
  onUpdate, 
  onRemove 
}: {
  join: TableJoin
  tables: Table[]
  onUpdate: (join: TableJoin) => void
  onRemove: () => void
}) {
  return (
    <div className="flex items-center gap-2 p-3 border rounded">
      <Select value={join.type} onValueChange={(value) => onUpdate({ ...join, type: value as any })}>
        <SelectTrigger className="w-32">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="LEFT">LEFT JOIN</SelectItem>
          <SelectItem value="INNER">INNER JOIN</SelectItem>
          <SelectItem value="RIGHT">RIGHT JOIN</SelectItem>
          <SelectItem value="FULL">FULL JOIN</SelectItem>
        </SelectContent>
      </Select>

      <Select value={join.table} onValueChange={(value) => onUpdate({ ...join, table: value })}>
        <SelectTrigger className="flex-1">
          <SelectValue placeholder="选择表" />
        </SelectTrigger>
        <SelectContent>
          {tables.map(table => (
            <SelectItem key={table.name} value={table.name}>
              {table.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      <span className="text-sm text-muted-foreground">ON</span>

      <Input 
        value={join.on}
        onChange={(e) => onUpdate({ ...join, on: e.target.value })}
        placeholder="关联条件 (如: patients.subject_id = admissions.subject_id)"
        className="flex-1"
      />

      <Button size="sm" variant="ghost" onClick={onRemove}>
        <Trash2 className="h-4 w-4" />
      </Button>
    </div>
  )
} 