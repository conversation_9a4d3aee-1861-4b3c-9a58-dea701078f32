# 医学数据分析平台后端 - 部署指南

## 概述

本文档提供了医学数据分析平台后端的完整部署指南，包括开发环境、测试环境和生产环境的部署方法。

## 系统要求

### 最低要求
- **操作系统**: Linux (Ubuntu 20.04+), macOS (10.15+), Windows 10+
- **Go版本**: 1.19 或更高版本
- **PostgreSQL**: 13 或更高版本
- **内存**: 最少 2GB RAM
- **存储**: 最少 10GB 可用空间

### 推荐配置
- **CPU**: 4核心或更多
- **内存**: 8GB RAM 或更多
- **存储**: SSD，50GB 或更多
- **网络**: 稳定的互联网连接

## 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd medical-data-platform-server
```

### 2. 环境配置
```bash
# 复制环境变量配置文件
cp env.example .env

# 编辑配置文件
vim .env
```

### 3. 数据库设置
```bash
# 创建数据库
createdb medical_data_platform

# 运行数据库迁移
./scripts/setup_database.sh
```

### 4. 启动应用
```bash
# 使用启动脚本
./scripts/start.sh --dev

# 或使用 Makefile
make dev
```

## 详细部署步骤

### 开发环境部署

#### 1. 安装依赖
```bash
# 安装 Go 依赖
make deps

# 安装开发工具
make install-tools
```

#### 2. 配置数据库
```bash
# 启动 PostgreSQL (如果使用 Docker)
docker run -d \
  --name postgres-dev \
  -e POSTGRES_USER=medical_w \
  -e POSTGRES_PASSWORD=medical_password_2024 \
  -e POSTGRES_DB=medical_data_platform \
  -p 5432:5432 \
  postgres:15

# 运行迁移
make db-migrate
```

#### 3. 启动开发服务器
```bash
# 方法1: 使用 Makefile
make dev

# 方法2: 使用启动脚本
./scripts/start.sh --dev

# 方法3: 直接运行
go run cmd/server/main.go
```

### 生产环境部署

#### 1. 服务器准备
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装必要软件
sudo apt install -y git postgresql postgresql-contrib nginx certbot

# 安装 Go
wget https://go.dev/dl/go1.21.0.linux-amd64.tar.gz
sudo tar -C /usr/local -xzf go1.21.0.linux-amd64.tar.gz
echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.bashrc
source ~/.bashrc
```

#### 2. 应用部署
```bash
# 克隆代码
git clone <repository-url> /opt/medical-platform
cd /opt/medical-platform

# 设置环境变量
cp env.example .env
# 编辑 .env 文件，设置生产环境配置

# 构建应用
make build

# 创建系统用户
sudo useradd -r -s /bin/false medical-platform

# 设置权限
sudo chown -R medical-platform:medical-platform /opt/medical-platform
sudo chmod +x /opt/medical-platform/bin/server
```

#### 3. 系统服务配置
创建 systemd 服务文件：

```bash
sudo tee /etc/systemd/system/medical-platform.service > /dev/null <<EOF
[Unit]
Description=Medical Data Platform Server
After=network.target postgresql.service

[Service]
Type=simple
User=medical-platform
Group=medical-platform
WorkingDirectory=/opt/medical-platform
ExecStart=/opt/medical-platform/bin/server
Restart=always
RestartSec=5
Environment=GIN_MODE=release
Environment=LOG_LEVEL=info
EnvironmentFile=/opt/medical-platform/.env

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/medical-platform/logs /opt/medical-platform/exports

[Install]
WantedBy=multi-user.target
EOF
```

启动服务：
```bash
sudo systemctl daemon-reload
sudo systemctl enable medical-platform
sudo systemctl start medical-platform
sudo systemctl status medical-platform
```

#### 4. Nginx 反向代理配置
```bash
sudo tee /etc/nginx/sites-available/medical-platform > /dev/null <<EOF
server {
    listen 80;
    server_name your-domain.com;

    # 重定向到 HTTPS
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    # SSL 证书配置
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;

    # SSL 安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # 反向代理配置
    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }

    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
}
EOF

# 启用站点
sudo ln -s /etc/nginx/sites-available/medical-platform /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

#### 5. SSL 证书配置
```bash
# 获取 Let's Encrypt 证书
sudo certbot --nginx -d your-domain.com

# 设置自动续期
sudo crontab -e
# 添加以下行：
# 0 12 * * * /usr/bin/certbot renew --quiet
```

### Docker 部署

#### 1. 构建 Docker 镜像
```bash
# 构建镜像
make docker-build

# 或手动构建
docker build -t medical-platform:latest .
```

#### 2. 使用 Docker Compose
创建 `docker-compose.yml` 文件：

```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - DATABASE_URL=**************************************************/medical_data_platform?sslmode=disable
      - JWT_SECRET=your-production-jwt-secret
      - LOG_LEVEL=info
      - GIN_MODE=release
    depends_on:
      - db
    volumes:
      - ./logs:/app/logs
      - ./exports:/app/exports

  db:
    image: postgres:15
    environment:
      - POSTGRES_USER=medical_w
      - POSTGRES_PASSWORD=medical_password_2024
      - POSTGRES_DB=medical_data_platform
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./migrations:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app

volumes:
  postgres_data:
```

启动服务：
```bash
docker-compose up -d
```

### Kubernetes 部署

#### 1. 创建 Kubernetes 配置文件

**Namespace:**
```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: medical-platform
```

**ConfigMap:**
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: medical-platform-config
  namespace: medical-platform
data:
  LOG_LEVEL: "info"
  GIN_MODE: "release"
  SERVER_PORT: "8080"
```

**Secret:**
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: medical-platform-secret
  namespace: medical-platform
type: Opaque
data:
  DATABASE_URL: <base64-encoded-database-url>
  JWT_SECRET: <base64-encoded-jwt-secret>
```

**Deployment:**
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: medical-platform
  namespace: medical-platform
spec:
  replicas: 3
  selector:
    matchLabels:
      app: medical-platform
  template:
    metadata:
      labels:
        app: medical-platform
    spec:
      containers:
      - name: medical-platform
        image: medical-platform:latest
        ports:
        - containerPort: 8080
        envFrom:
        - configMapRef:
            name: medical-platform-config
        - secretRef:
            name: medical-platform-secret
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
```

**Service:**
```yaml
apiVersion: v1
kind: Service
metadata:
  name: medical-platform-service
  namespace: medical-platform
spec:
  selector:
    app: medical-platform
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: LoadBalancer
```

#### 2. 部署到 Kubernetes
```bash
kubectl apply -f k8s/
```

## 监控和日志

### 1. 日志管理
```bash
# 查看应用日志
make logs

# 查看 Gin 日志
make gin-logs

# 查看系统服务日志
sudo journalctl -u medical-platform -f
```

### 2. 健康检查
```bash
# 检查应用健康状态
make health

# 或直接访问
curl http://localhost:8080/health
```

### 3. 性能监控
建议集成以下监控工具：
- **Prometheus**: 指标收集
- **Grafana**: 可视化仪表板
- **Jaeger**: 分布式追踪
- **ELK Stack**: 日志聚合和分析

## 备份和恢复

### 数据库备份
```bash
# 创建备份
pg_dump -h localhost -U medical_w medical_data_platform > backup_$(date +%Y%m%d_%H%M%S).sql

# 恢复备份
psql -h localhost -U medical_w medical_data_platform < backup_20231214_120000.sql
```

### 应用数据备份
```bash
# 备份导出文件和日志
tar -czf app_data_backup_$(date +%Y%m%d_%H%M%S).tar.gz logs/ exports/
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库服务状态
   - 验证连接字符串
   - 检查防火墙设置

2. **应用启动失败**
   - 检查日志文件
   - 验证环境变量
   - 确认端口未被占用

3. **性能问题**
   - 检查数据库查询性能
   - 监控内存使用情况
   - 分析日志中的慢请求

### 调试命令
```bash
# 检查应用状态
systemctl status medical-platform

# 查看详细日志
journalctl -u medical-platform --since "1 hour ago"

# 检查端口占用
netstat -tlnp | grep :8080

# 检查数据库连接
psql -h localhost -U medical_w -d medical_data_platform -c "SELECT version();"
```

## 安全建议

1. **定期更新依赖**
   ```bash
   make deps-update
   ```

2. **使用强密码**
   - JWT Secret 至少 32 字符
   - 数据库密码复杂度高

3. **启用 HTTPS**
   - 使用 SSL/TLS 证书
   - 配置安全头

4. **网络安全**
   - 配置防火墙
   - 限制数据库访问
   - 使用 VPN 或私有网络

5. **定期备份**
   - 自动化备份脚本
   - 测试恢复流程

## 性能优化

1. **数据库优化**
   - 创建适当的索引
   - 定期 VACUUM 和 ANALYZE
   - 调整连接池大小

2. **应用优化**
   - 启用 Gzip 压缩
   - 配置缓存
   - 优化查询逻辑

3. **系统优化**
   - 调整文件描述符限制
   - 优化内核参数
   - 使用 SSD 存储

## 联系支持

如果在部署过程中遇到问题，请：

1. 查看日志文件获取详细错误信息
2. 检查本文档的故障排除部分
3. 提交 Issue 到项目仓库
4. 联系技术支持团队

---

**注意**: 在生产环境中部署前，请务必在测试环境中验证所有配置和功能。 