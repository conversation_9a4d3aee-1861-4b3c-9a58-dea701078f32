package service

import (
	"context"

	"github.com/chungzy/medical-data-platform/internal/model"
	"github.com/chungzy/medical-data-platform/internal/repository"
)

type DictionaryService struct {
	dictionaryRepo repository.DictionaryRepository
}

func NewDictionaryService(dictionaryRepo repository.DictionaryRepository) *DictionaryService {
	return &DictionaryService{
		dictionaryRepo: dictionaryRepo,
	}
}

// SearchFields 搜索数据字段
func (ds *DictionaryService) SearchFields(ctx context.Context, query, dataset, category string, limit, offset int) (*model.DictionarySearchResponse, error) {
	fields, total, err := ds.dictionaryRepo.SearchFields(ctx, query, dataset, category, limit, offset)
	if err != nil {
		return nil, err
	}

	response := &model.DictionarySearchResponse{
		Fields: fields,
		Total:  total,
	}

	return response, nil
}

// GetCategories 获取字段分类
func (ds *DictionaryService) GetCategories(ctx context.Context, dataset string) (*model.CategoriesResponse, error) {
	categories, err := ds.dictionaryRepo.GetCategories(ctx, dataset)
	if err != nil {
		return nil, err
	}

	response := &model.CategoriesResponse{
		Categories: categories,
	}

	return response, nil
}

// GetFieldByID 根据ID获取字段详情
func (ds *DictionaryService) GetFieldByID(ctx context.Context, id string) (*model.DataField, error) {
	return ds.dictionaryRepo.GetFieldByID(ctx, id)
}
