version: '3.8'

services:
  # 医疗数据平台后端服务 - 生产环境
  medical-platform:
    image: ${DOCKER_REGISTRY:-ghcr.io}/${DOCKER_IMAGE:-medical-data-platform/server}:${VERSION:-latest}
    container_name: medical-platform-prod
    ports:
      - "8080:8080"
    environment:
      - GIN_MODE=release
      - LOG_LEVEL=info
      - LOG_DIR=/app/logs
      - DATABASE_HOST=postgres
      - DATABASE_PORT=5432
      - DATABASE_USER=${DB_USER:-medical_user}
      - DATABASE_PASSWORD=${DB_PASSWORD}
      - DATABASE_NAME=${DB_NAME:-medical_data_platform}
      - DATABASE_SSLMODE=require
      - JWT_SECRET=${JWT_SECRET}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
    volumes:
      - app_logs:/app/logs
      - app_exports:/app/exports
      - app_configs:/app/configs:ro
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - medical-network
    restart: always
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 512M
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp:noexec,nosuid,size=100m

  # PostgreSQL数据库 - 生产环境
  postgres:
    image: postgres:15-alpine
    container_name: medical-postgres-prod
    environment:
      - POSTGRES_USER=${DB_USER:-medical_user}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      - POSTGRES_DB=${DB_NAME:-medical_data_platform}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
      - POSTGRES_SHARED_PRELOAD_LIBRARIES=pg_stat_statements
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - postgres_backups:/backups
      - ./migrations:/docker-entrypoint-initdb.d:ro
    networks:
      - medical-network
    restart: always
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-medical_user} -d ${DB_NAME:-medical_data_platform}"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '0.5'
          memory: 1G
    security_opt:
      - no-new-privileges:true
    command: >
      postgres
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c random_page_cost=1.1
      -c effective_io_concurrency=200
      -c work_mem=4MB
      -c min_wal_size=1GB
      -c max_wal_size=4GB
      -c log_statement=all
      -c log_duration=on
      -c log_min_duration_statement=1000

  # Redis缓存 - 生产环境
  redis:
    image: redis:7-alpine
    container_name: medical-redis-prod
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
    volumes:
      - redis_data:/data
      - redis_config:/usr/local/etc/redis:ro
    networks:
      - medical-network
    restart: always
    healthcheck:
      test: ["CMD", "redis-cli", "--no-auth-warning", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 256M
    security_opt:
      - no-new-privileges:true
    command: >
      redis-server
      --requirepass ${REDIS_PASSWORD:-}
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
      --appendonly yes
      --appendfsync everysec

  # Nginx反向代理 - 生产环境
  nginx:
    image: nginx:alpine
    container_name: medical-nginx-prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - medical-platform
    networks:
      - medical-network
    restart: always
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.1'
          memory: 128M
    security_opt:
      - no-new-privileges:true

  # 数据库备份服务
  postgres-backup:
    image: postgres:15-alpine
    container_name: medical-postgres-backup
    environment:
      - PGUSER=${DB_USER:-medical_user}
      - PGPASSWORD=${DB_PASSWORD}
      - PGDATABASE=${DB_NAME:-medical_data_platform}
      - PGHOST=postgres
      - BACKUP_SCHEDULE=${BACKUP_SCHEDULE:-0 2 * * *}
    volumes:
      - postgres_backups:/backups
      - ./scripts/backup.sh:/backup.sh:ro
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - medical-network
    restart: always
    command: >
      sh -c "
        apk add --no-cache dcron &&
        echo '${BACKUP_SCHEDULE:-0 2 * * *} /backup.sh' | crontab - &&
        crond -f -l 2
      "

volumes:
  postgres_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_DIR:-/opt/medical-platform}/postgres
  postgres_backups:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${BACKUP_DIR:-/opt/medical-platform}/backups
  redis_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_DIR:-/opt/medical-platform}/redis
  redis_config:
    driver: local
  app_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${LOG_DIR:-/opt/medical-platform}/logs
  app_exports:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${EXPORT_DIR:-/opt/medical-platform}/exports
  app_configs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${CONFIG_DIR:-/opt/medical-platform}/configs
  nginx_logs:
    driver: local

networks:
  medical-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16 