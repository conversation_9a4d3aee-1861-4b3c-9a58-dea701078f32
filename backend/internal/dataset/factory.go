package dataset

import (
	"fmt"
	"time"

	"github.com/chungzy/medical-data-platform/pkg/dataset"
)

// DatasetConfigProvider 数据集配置提供者
type DatasetConfigProvider struct{}

// NewDatasetConfigProvider 创建数据集配置提供者
func NewDatasetConfigProvider() *DatasetConfigProvider {
	return &DatasetConfigProvider{}
}

// ServiceConfig 服务配置
type ServiceConfig struct {
	MaxRows    int           `json:"max_rows"`
	CacheSize  int           `json:"cache_size"`
	CacheTTL   time.Duration `json:"cache_ttl"`
	MaxFields  int           `json:"max_fields"`
	MaxFilters int           `json:"max_filters"`
	MaxJoins   int           `json:"max_joins"`
}

// GetDatasetConfig 获取数据集配置
func (dcp *DatasetConfigProvider) GetDatasetConfig(datasetType dataset.DatasetType) (*dataset.DatasetConfig, error) {
	return dataset.GetDatasetConfig(datasetType)
}

// GetServiceConfig 获取数据集特定的服务配置
func (dcp *DatasetConfigProvider) GetServiceConfig(datasetType dataset.DatasetType) (*ServiceConfig, error) {
	switch datasetType {
	case dataset.DatasetTypeMIMICIV:
		return &ServiceConfig{
			MaxRows:    10000,
			CacheSize:  1000,
			CacheTTL:   time.Hour,
			MaxFields:  50,
			MaxFilters: 20,
			MaxJoins:   10,
		}, nil
	case dataset.DatasetTypeEICU:
		return &ServiceConfig{
			MaxRows:    5000,
			CacheSize:  500,
			CacheTTL:   time.Hour,
			MaxFields:  30,
			MaxFilters: 15,
			MaxJoins:   8,
		}, nil
	case dataset.DatasetTypeNHANES:
		return &ServiceConfig{
			MaxRows:    15000,
			CacheSize:  800,
			CacheTTL:   time.Hour,
			MaxFields:  40,
			MaxFilters: 18,
			MaxJoins:   6,
		}, nil
	case dataset.DatasetTypePIC:
		return &ServiceConfig{
			MaxRows:    8000,
			CacheSize:  600,
			CacheTTL:   time.Hour,
			MaxFields:  35,
			MaxFilters: 16,
			MaxJoins:   7,
		}, nil
	default:
		return nil, fmt.Errorf("unsupported dataset type: %s", datasetType)
	}
}

// GetTableSchemaMapping 获取表到schema的映射
func (dcp *DatasetConfigProvider) GetTableSchemaMapping(datasetType dataset.DatasetType) map[string]string {
	return dataset.GetTableSchemaMapping(datasetType)
}

// ValidateDatasetType 验证数据集类型
func (dcp *DatasetConfigProvider) ValidateDatasetType(datasetType dataset.DatasetType) error {
	if !dataset.ValidateDatasetType(datasetType) {
		return fmt.Errorf("unsupported dataset type: %s", datasetType)
	}
	return nil
}

// GetSupportedDatasets 获取支持的数据集列表
func (dcp *DatasetConfigProvider) GetSupportedDatasets() []dataset.DatasetType {
	return dataset.GetSupportedDatasets()
}
