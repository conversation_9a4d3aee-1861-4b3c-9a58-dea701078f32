package service

import (
	"errors"
	"regexp"
	"strings"
	"time"

	"golang.org/x/crypto/bcrypt"
)

type PasswordService struct {
	minLength        int
	requireUppercase bool
	requireLowercase bool
	requireNumbers   bool
	requireSpecial   bool
	maxAge           time.Duration
}

type PasswordPolicy struct {
	MinLength        int           `json:"min_length"`
	RequireUppercase bool          `json:"require_uppercase"`
	RequireLowercase bool          `json:"require_lowercase"`
	RequireNumbers   bool          `json:"require_numbers"`
	RequireSpecial   bool          `json:"require_special"`
	MaxAge           time.Duration `json:"max_age"`
}

type PasswordValidationResult struct {
	IsValid bool     `json:"is_valid"`
	Errors  []string `json:"errors"`
	Score   int      `json:"score"` // 密码强度评分 0-100
}

func NewPasswordService(policy PasswordPolicy) *PasswordService {
	return &PasswordService{
		minLength:        policy.MinLength,
		requireUppercase: policy.RequireUppercase,
		requireLowercase: policy.RequireLowercase,
		requireNumbers:   policy.RequireNumbers,
		requireSpecial:   policy.RequireSpecial,
		maxAge:           policy.MaxAge,
	}
}

// GetDefaultPolicy 获取默认密码策略
func GetDefaultPolicy() PasswordPolicy {
	return PasswordPolicy{
		MinLength:        8,
		RequireUppercase: true,
		RequireLowercase: true,
		RequireNumbers:   true,
		RequireSpecial:   true,
		MaxAge:           90 * 24 * time.Hour, // 90天
	}
}

// ValidatePassword 验证密码是否符合策略
func (ps *PasswordService) ValidatePassword(password string) PasswordValidationResult {
	var errors []string
	score := 0

	// 检查长度
	if len(password) < ps.minLength {
		errors = append(errors, "密码长度至少需要8位")
	} else {
		score += 20
		if len(password) >= 12 {
			score += 10
		}
	}

	// 检查大写字母
	hasUppercase := regexp.MustCompile(`[A-Z]`).MatchString(password)
	if ps.requireUppercase && !hasUppercase {
		errors = append(errors, "密码必须包含大写字母")
	} else if hasUppercase {
		score += 15
	}

	// 检查小写字母
	hasLowercase := regexp.MustCompile(`[a-z]`).MatchString(password)
	if ps.requireLowercase && !hasLowercase {
		errors = append(errors, "密码必须包含小写字母")
	} else if hasLowercase {
		score += 15
	}

	// 检查数字
	hasNumbers := regexp.MustCompile(`[0-9]`).MatchString(password)
	if ps.requireNumbers && !hasNumbers {
		errors = append(errors, "密码必须包含数字")
	} else if hasNumbers {
		score += 15
	}

	// 检查特殊字符
	hasSpecial := regexp.MustCompile(`[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]`).MatchString(password)
	if ps.requireSpecial && !hasSpecial {
		errors = append(errors, "密码必须包含特殊字符")
	} else if hasSpecial {
		score += 15
	}

	// 检查常见弱密码
	if ps.isCommonPassword(password) {
		errors = append(errors, "密码过于常见，请选择更安全的密码")
		score -= 20
	}

	// 检查重复字符
	if ps.hasRepeatingChars(password) {
		errors = append(errors, "密码不能包含过多重复字符")
		score -= 10
	}

	// 检查连续字符
	if ps.hasSequentialChars(password) {
		errors = append(errors, "密码不能包含连续字符序列")
		score -= 10
	}

	// 额外加分项
	if len(password) >= 16 {
		score += 10
	}

	// 确保分数在0-100范围内
	if score < 0 {
		score = 0
	}
	if score > 100 {
		score = 100
	}

	return PasswordValidationResult{
		IsValid: len(errors) == 0,
		Errors:  errors,
		Score:   score,
	}
}

// HashPassword 加密密码
func (ps *PasswordService) HashPassword(password string) (string, error) {
	// 验证密码
	validation := ps.ValidatePassword(password)
	if !validation.IsValid {
		return "", errors.New("密码不符合安全策略: " + strings.Join(validation.Errors, ", "))
	}

	// 使用bcrypt加密
	hashedBytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}

	return string(hashedBytes), nil
}

// VerifyPassword 验证密码
func (ps *PasswordService) VerifyPassword(hashedPassword, password string) error {
	return bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(password))
}

// GenerateSecurePassword 生成安全密码
func (ps *PasswordService) GenerateSecurePassword(length int) string {
	if length < ps.minLength {
		length = ps.minLength
	}

	const (
		lowercase = "abcdefghijklmnopqrstuvwxyz"
		uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
		numbers   = "0123456789"
		special   = "!@#$%^&*()_+-=[]{}|;:,.<>?"
	)

	var charset string
	var required []byte

	if ps.requireLowercase {
		charset += lowercase
		required = append(required, lowercase[0])
	}
	if ps.requireUppercase {
		charset += uppercase
		required = append(required, uppercase[0])
	}
	if ps.requireNumbers {
		charset += numbers
		required = append(required, numbers[0])
	}
	if ps.requireSpecial {
		charset += special
		required = append(required, special[0])
	}

	// 这里应该使用加密安全的随机数生成器
	// 为了简化，这里只是示例实现
	password := make([]byte, length)
	
	// 首先确保包含必需的字符类型
	for i, char := range required {
		if i < length {
			password[i] = char
		}
	}

	// 填充剩余位置
	// 实际实现应该使用crypto/rand
	
	return string(password)
}

// isCommonPassword 检查是否为常见弱密码
func (ps *PasswordService) isCommonPassword(password string) bool {
	commonPasswords := []string{
		"password", "123456", "123456789", "12345678", "12345",
		"1234567", "admin", "qwerty", "abc123", "password123",
		"admin123", "root", "user", "test", "guest",
	}

	lowerPassword := strings.ToLower(password)
	for _, common := range commonPasswords {
		if lowerPassword == common {
			return true
		}
	}

	return false
}

// hasRepeatingChars 检查是否有过多重复字符
func (ps *PasswordService) hasRepeatingChars(password string) bool {
	if len(password) < 3 {
		return false
	}

	count := 1
	for i := 1; i < len(password); i++ {
		if password[i] == password[i-1] {
			count++
			if count >= 3 {
				return true
			}
		} else {
			count = 1
		}
	}

	return false
}

// hasSequentialChars 检查是否有连续字符序列
func (ps *PasswordService) hasSequentialChars(password string) bool {
	if len(password) < 3 {
		return false
	}

	for i := 0; i < len(password)-2; i++ {
		// 检查递增序列
		if password[i+1] == password[i]+1 && password[i+2] == password[i]+2 {
			return true
		}
		// 检查递减序列
		if password[i+1] == password[i]-1 && password[i+2] == password[i]-2 {
			return true
		}
	}

	return false
}

// GetPasswordStrengthText 获取密码强度文本描述
func GetPasswordStrengthText(score int) string {
	switch {
	case score >= 80:
		return "非常强"
	case score >= 60:
		return "强"
	case score >= 40:
		return "中等"
	case score >= 20:
		return "弱"
	default:
		return "非常弱"
	}
}

// GetPasswordStrengthColor 获取密码强度颜色
func GetPasswordStrengthColor(score int) string {
	switch {
	case score >= 80:
		return "green"
	case score >= 60:
		return "blue"
	case score >= 40:
		return "yellow"
	case score >= 20:
		return "orange"
	default:
		return "red"
	}
}
