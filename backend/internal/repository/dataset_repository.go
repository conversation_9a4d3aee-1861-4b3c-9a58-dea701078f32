package repository

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"

	"github.com/chungzy/medical-data-platform/internal/model"
	"github.com/chungzy/medical-data-platform/pkg/database"
	"github.com/chungzy/medical-data-platform/pkg/logger"
)

type datasetRepository struct {
	db         *sql.DB
	dsm        *database.DataSourceManager
	schemaRepo SchemaRepository
}

func NewDatasetRepository(db *sql.DB) DatasetRepository {
	return &datasetRepository{db: db}
}

// NewDatasetRepositoryWithDSM 创建带数据源管理器的数据集Repository
func NewDatasetRepositoryWithDSM(db *sql.DB, dsm *database.DataSourceManager) DatasetRepository {
	return &datasetRepository{db: db, dsm: dsm}
}

// NewDatasetRepositoryWithSchema 创建带schema repository的数据集Repository
func NewDatasetRepositoryWithSchema(db *sql.DB, dsm *database.DataSourceManager, schemaRepo SchemaRepository) DatasetRepository {
	return &datasetRepository{db: db, dsm: dsm, schemaRepo: schemaRepo}
}

func (r *datasetRepository) GetAll(ctx context.Context) ([]model.Dataset, error) {
	query := `
		SELECT id, name, description, version, patient_count, status, last_updated
		FROM datasets
		ORDER BY name
	`

	rows, err := r.db.QueryContext(ctx, query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var datasets []model.Dataset
	for rows.Next() {
		var dataset model.Dataset
		err := rows.Scan(
			&dataset.ID, &dataset.Name, &dataset.Description,
			&dataset.Version, &dataset.PatientCount, &dataset.Status,
			&dataset.LastUpdated,
		)
		if err != nil {
			return nil, err
		}
		datasets = append(datasets, dataset)
	}

	return datasets, rows.Err()
}

func (r *datasetRepository) GetByID(ctx context.Context, id string) (*model.Dataset, error) {
	query := `
		SELECT id, name, description, version, patient_count, status, last_updated
		FROM datasets WHERE id = $1
	`

	dataset := &model.Dataset{}
	err := r.db.QueryRowContext(ctx, query, id).Scan(
		&dataset.ID, &dataset.Name, &dataset.Description,
		&dataset.Version, &dataset.PatientCount, &dataset.Status,
		&dataset.LastUpdated,
	)
	if err != nil {
		return nil, err
	}

	return dataset, nil
}

func (r *datasetRepository) GetFields(ctx context.Context, datasetID string) ([]model.DataField, error) {
	// 优先使用schema repository获取真实字段信息
	if r.schemaRepo != nil {
		// 获取数据集的所有表
		tables, err := r.schemaRepo.GetDatasetTables(ctx, datasetID)
		if err == nil && len(tables) > 0 {
			var allFields []model.DataField

			// 遍历每个表获取字段信息
			for _, table := range tables {
				fields, err := r.schemaRepo.GetDatasetFields(ctx, datasetID, table.Name)
				if err != nil {
					logger.Warn("Failed to get fields for table", map[string]interface{}{
						"dataset_id": datasetID,
						"table_name": table.Name,
						"error":      err,
					})
					continue
				}
				allFields = append(allFields, fields...)
			}

			if len(allFields) > 0 {
				logger.Info("Retrieved fields from schema repository", map[string]interface{}{
					"dataset_id":  datasetID,
					"field_count": len(allFields),
				})
				return allFields, nil
			}
		}
		logger.Warn("Failed to get fields from schema repository, falling back to database", map[string]interface{}{
			"dataset_id": datasetID,
			"error":      err,
		})
	}

	// 降级到数据库查询
	query := `
		SELECT id, dataset_id, name, name_en, code, data_type, category, description, unit, value_range, examples
		FROM data_fields WHERE dataset_id = $1
		ORDER BY category, name
	`

	rows, err := r.db.QueryContext(ctx, query, datasetID)
	if err != nil {
		logger.Error("Failed to query data_fields table", err, map[string]interface{}{
			"dataset_id": datasetID,
		})
		return nil, err
	}
	defer rows.Close()

	var fields []model.DataField
	for rows.Next() {
		var field model.DataField
		var examples []byte

		err := rows.Scan(
			&field.ID, &field.DatasetID, &field.Name, &field.NameEn,
			&field.Code, &field.DataType, &field.Category, &field.Description,
			&field.Unit, &field.ValueRange, &examples,
		)
		if err != nil {
			return nil, err
		}

		if len(examples) > 0 {
			field.Examples = json.RawMessage(examples)
		}

		fields = append(fields, field)
	}

	return fields, rows.Err()
}

func (r *datasetRepository) GetTables(ctx context.Context, datasetID string) ([]model.DatasetTable, error) {
	// 优先使用schema repository获取真实表信息
	if r.schemaRepo != nil {
		tables, err := r.schemaRepo.GetDatasetTables(ctx, datasetID)
		if err == nil && len(tables) > 0 {
			logger.Info("Retrieved tables from schema repository", map[string]interface{}{
				"dataset_id":  datasetID,
				"table_count": len(tables),
			})
			return tables, nil
		}
		logger.Warn("Failed to get tables from schema repository, trying data source", map[string]interface{}{
			"dataset_id": datasetID,
			"error":      err,
		})
	}

	// 次选：从数据源管理器获取表信息
	if r.dsm != nil {
		tables, err := r.getTablesFromDataSource(ctx, datasetID)
		if err == nil && len(tables) > 0 {
			logger.Info("Retrieved tables from data source", map[string]interface{}{
				"dataset_id":  datasetID,
				"table_count": len(tables),
			})
			return tables, nil
		}
		logger.Warn("Failed to get tables from data source, falling back to mock data", map[string]interface{}{
			"dataset_id": datasetID,
			"error":      err,
		})
	}

	// 最后降级到模拟数据
	return r.getMockTables(datasetID), nil
}

// getTablesFromDataSource 从真实数据源获取表信息
func (r *datasetRepository) getTablesFromDataSource(ctx context.Context, datasetID string) ([]model.DatasetTable, error) {
	// 获取数据源连接
	db, err := r.dsm.GetConnection(datasetID)
	if err != nil {
		return nil, fmt.Errorf("failed to get data source connection: %w", err)
	}

	// 查询表信息 - 使用PostgreSQL的information_schema
	query := `
		SELECT
			table_name,
			COALESCE(obj_description(c.oid), '') as table_comment,
			COALESCE(n_tup_ins, 0) as estimated_rows
		FROM information_schema.tables t
		LEFT JOIN pg_class c ON c.relname = t.table_name
		LEFT JOIN pg_stat_user_tables s ON s.relname = t.table_name
		WHERE t.table_schema = ANY(string_to_array($1, ','))
		AND t.table_type = 'BASE TABLE'
		ORDER BY t.table_name
	`

	// 获取schema配置
	schemas := r.getDatasetSchemas(datasetID)

	rows, err := db.QueryContext(ctx, query, schemas)
	if err != nil {
		return nil, fmt.Errorf("failed to query table information: %w", err)
	}
	defer rows.Close()

	var tables []model.DatasetTable
	for rows.Next() {
		var table model.DatasetTable
		var description sql.NullString
		var recordCount sql.NullInt64

		err := rows.Scan(&table.Name, &description, &recordCount)
		if err != nil {
			logger.Error("Failed to scan table row", err, map[string]interface{}{
				"dataset_id": datasetID,
			})
			continue
		}

		if description.Valid {
			table.Description = description.String
		}
		if recordCount.Valid {
			table.RecordCount = int(recordCount.Int64)
		}

		tables = append(tables, table)
	}

	return tables, rows.Err()
}

// getDatasetSchemas 获取数据集对应的schema
func (r *datasetRepository) getDatasetSchemas(datasetID string) string {
	schemaMap := map[string]string{
		"mimic-iv": "mimiciv_hosp,mimiciv_icu,mimiciv_ed",
		"eicu":     "eicu_crd",
		"nhanes":   "nhanes",
		"pic":      "pic",
	}

	if schema, exists := schemaMap[datasetID]; exists {
		return schema
	}
	return "public"
}

// getMockTables 获取模拟表数据（保留作为后备）
func (r *datasetRepository) getMockTables(datasetID string) []model.DatasetTable {
	switch datasetID {
	case "mimic-iv", "mimic_iv":
		return []model.DatasetTable{
			{Name: "patients", Description: "Patient demographics", RecordCount: 382278},
			{Name: "admissions", Description: "Hospital admissions", RecordCount: 523740},
			{Name: "icustays", Description: "ICU stays", RecordCount: 76540},
			{Name: "chartevents", Description: "Chart events", RecordCount: 329499788},
			{Name: "labevents", Description: "Laboratory events", RecordCount: 122103667},
		}
	case "eicu":
		return []model.DatasetTable{
			{Name: "patient", Description: "Patient information", RecordCount: 139367},
			{Name: "admissiondx", Description: "Admission diagnoses", RecordCount: 165865},
			{Name: "diagnosis", Description: "Diagnoses", RecordCount: 2704494},
			{Name: "lab", Description: "Laboratory results", RecordCount: 27872575},
		}
	case "nhanes":
		return []model.DatasetTable{
			{Name: "demographics", Description: "Demographics", RecordCount: 15560},
			{Name: "examination", Description: "Physical examination", RecordCount: 15560},
			{Name: "laboratory", Description: "Laboratory results", RecordCount: 15560},
			{Name: "questionnaire", Description: "Questionnaire responses", RecordCount: 15560},
		}
	case "pic":
		return []model.DatasetTable{
			{Name: "patients", Description: "Patient information", RecordCount: 12881},
			{Name: "events", Description: "Clinical events", RecordCount: 2500000},
			{Name: "diagnoses", Description: "Diagnoses", RecordCount: 50000},
		}
	default:
		return []model.DatasetTable{}
	}
}

// ValidateDatasetConnection 验证数据集连接
func (r *datasetRepository) ValidateDatasetConnection(ctx context.Context, datasetID string) error {
	// 使用schema repository验证连接
	if r.schemaRepo != nil {
		if err := r.schemaRepo.ValidateDataset(ctx, datasetID); err != nil {
			logger.Error("Schema repository validation failed", err, map[string]interface{}{
				"dataset_id": datasetID,
			})
			return fmt.Errorf("schema validation failed: %w", err)
		}
		logger.Info("Dataset connection validated successfully", map[string]interface{}{
			"dataset_id": datasetID,
		})
		return nil
	}

	// 降级到数据源管理器验证
	if r.dsm != nil {
		if !r.dsm.IsDataSourceHealthy(datasetID) {
			return fmt.Errorf("data source %s is not healthy", datasetID)
		}
		logger.Info("Data source health check passed", map[string]interface{}{
			"dataset_id": datasetID,
		})
		return nil
	}

	return fmt.Errorf("no validation method available for dataset %s", datasetID)
}
