# 配置文档

## 概述

医学数据分析平台使用YAML格式的配置文件进行配置管理，不再依赖环境变量。系统会自动查找配置文件并加载相应的配置。

## 配置文件位置

系统会按以下顺序查找配置文件：

1. `config.yaml`
2. `config.yml`
3. `configs/config.yaml`
4. `configs/config.yml`
5. `./config.yaml`
6. `./config.yml`

## 快速开始

1. 复制配置模板：
```bash
cp config.example.yaml config.yaml
```

2. 修改数据库配置：
```yaml
database:
  host: "localhost"
  port: 5432
  user: "postgres"
  password: "your_actual_password"
  dbname: "medical_data_platform"
  sslmode: "disable"
```

3. 修改JWT密钥：
```yaml
jwt:
  secret_key: "your-strong-secret-key"
  expiration_hours: 24
```

## 配置项说明

### 应用程序配置 (app)

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `name` | string | "Medical Data Platform" | 应用程序名称 |
| `version` | string | "1.0.0" | 应用程序版本 |
| `environment` | string | "development" | 运行环境：development, production, testing |
| `log_level` | string | "info" | 日志级别：debug, info, warn, error |
| `max_query_results` | int | 10000 | 单次查询最大返回结果数 |
| `export_file_retention_days` | int | 7 | 导出文件保留天数 |
| `max_concurrent_queries` | int | 5 | 用户最大并发查询数 |

### 服务器配置 (server)

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `host` | string | "localhost" | 服务器监听地址 |
| `port` | string | "8080" | 服务器端口 |
| `mode` | string | "debug" | Gin模式：debug, release |

### 数据库配置 (database)

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `host` | string | "localhost" | 数据库主机地址 |
| `port` | int | 5432 | 数据库端口 |
| `user` | string | "postgres" | 数据库用户名 |
| `password` | string | - | 数据库密码 |
| `dbname` | string | "medical_data_platform" | 数据库名称 |
| `sslmode` | string | "disable" | SSL模式：disable, require, verify-ca, verify-full |

### JWT配置 (jwt)

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `secret_key` | string | - | JWT签名密钥（生产环境必须修改） |
| `expiration_hours` | int | 24 | Token过期时间（小时） |

## 环境特定配置

### 开发环境 (config.yaml)
```yaml
app:
  environment: "development"
  log_level: "debug"

server:
  mode: "debug"

database:
  sslmode: "disable"
```

### 生产环境 (config.production.yaml)
```yaml
app:
  environment: "production"
  log_level: "warn"
  max_query_results: 50000
  max_concurrent_queries: 10

server:
  host: "0.0.0.0"
  mode: "release"

database:
  sslmode: "require"

jwt:
  expiration_hours: 8
```

## 数据库连接配置

### PostgreSQL连接示例

#### 本地开发环境
```yaml
database:
  host: "localhost"
  port: 5432
  user: "postgres"
  password: "password"
  dbname: "medical_data_platform"
  sslmode: "disable"
```

#### Docker环境
```yaml
database:
  host: "postgres"  # Docker服务名
  port: 5432
  user: "medical_user"
  password: "medical_password"
  dbname: "medical_data_platform"
  sslmode: "disable"
```

#### 云数据库环境
```yaml
database:
  host: "your-cloud-db-host.com"
  port: 5432
  user: "medical_platform_user"
  password: "your-strong-password"
  dbname: "medical_data_platform"
  sslmode: "require"
```

## 安全配置建议

### 生产环境安全配置

1. **强密码策略**：
   - 数据库密码至少16位，包含大小写字母、数字和特殊字符
   - JWT密钥至少32位随机字符串

2. **SSL/TLS配置**：
   ```yaml
   database:
     sslmode: "require"  # 或 "verify-full"
   ```

3. **访问控制**：
   ```yaml
   server:
     host: "0.0.0.0"  # 仅在需要外部访问时使用
   ```

4. **日志配置**：
   ```yaml
   app:
     log_level: "warn"  # 生产环境减少日志输出
   ```

## 配置验证

启动应用时，系统会自动验证配置文件：

1. **必需配置检查**：验证数据库连接信息、JWT密钥等必需配置
2. **格式验证**：检查YAML格式是否正确
3. **连接测试**：测试数据库连接是否可用

## 故障排除

### 常见问题

1. **配置文件未找到**
   ```
   Configuration file not found. Please create config.yaml
   ```
   解决方案：确保在项目根目录有 `config.yaml` 文件

2. **YAML格式错误**
   ```
   Failed to parse config file: yaml: line X: found character that cannot start any token
   ```
   解决方案：检查YAML文件格式，注意缩进和语法

3. **数据库连接失败**
   ```
   Failed to ping database: connection refused
   ```
   解决方案：检查数据库配置和数据库服务状态

### 调试配置

启用调试模式查看详细配置信息：
```yaml
app:
  log_level: "debug"
```

## 配置文件管理

### 版本控制

- 将 `config.example.yaml` 加入版本控制
- 将实际的 `config.yaml` 添加到 `.gitignore`
- 为不同环境创建不同的配置文件

### 配置文件示例

```bash
# 项目结构
├── config.example.yaml      # 配置模板（版本控制）
├── config.yaml             # 开发环境配置（不纳入版本控制）
├── config.production.yaml  # 生产环境配置（不纳入版本控制）
├── config.testing.yaml     # 测试环境配置（不纳入版本控制）
└── .gitignore              # 忽略实际配置文件
```

### .gitignore 配置
```gitignore
# 配置文件
config.yaml
config.production.yaml
config.testing.yaml
config.local.yaml

# 但保留模板文件
!config.example.yaml
``` 