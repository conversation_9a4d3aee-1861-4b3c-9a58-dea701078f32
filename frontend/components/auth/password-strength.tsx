"use client"

import { useState, useEffect } from "react"
import { Check, X, <PERSON>, EyeOff } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"

interface PasswordValidation {
  isValid: boolean
  errors: string[]
  score: number
}

interface PasswordStrengthProps {
  value: string
  onChange: (value: string) => void
  label?: string
  placeholder?: string
  required?: boolean
  showStrength?: boolean
  showRequirements?: boolean
}

export function PasswordStrength({
  value,
  onChange,
  label = "密码",
  placeholder = "请输入密码",
  required = true,
  showStrength = true,
  showRequirements = true,
}: PasswordStrengthProps) {
  const [showPassword, setShowPassword] = useState(false)
  const [validation, setValidation] = useState<PasswordValidation>({
    isValid: false,
    errors: [],
    score: 0,
  })

  // 密码验证规则
  const requirements = [
    {
      id: "length",
      text: "至少8个字符",
      test: (password: string) => password.length >= 8,
    },
    {
      id: "uppercase",
      text: "包含大写字母",
      test: (password: string) => /[A-Z]/.test(password),
    },
    {
      id: "lowercase",
      text: "包含小写字母",
      test: (password: string) => /[a-z]/.test(password),
    },
    {
      id: "number",
      text: "包含数字",
      test: (password: string) => /[0-9]/.test(password),
    },
    {
      id: "special",
      text: "包含特殊字符",
      test: (password: string) => /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password),
    },
  ]

  // 计算密码强度
  useEffect(() => {
    if (!value) {
      setValidation({ isValid: false, errors: [], score: 0 })
      return
    }

    const errors: string[] = []
    let score = 0

    // 检查每个要求
    requirements.forEach((req) => {
      if (!req.test(value)) {
        errors.push(req.text)
      } else {
        score += 20
      }
    })

    // 额外加分项
    if (value.length >= 12) score += 10
    if (value.length >= 16) score += 10

    // 检查常见弱密码
    const commonPasswords = [
      "password", "123456", "123456789", "12345678", "12345",
      "1234567", "admin", "qwerty", "abc123", "password123",
    ]
    if (commonPasswords.includes(value.toLowerCase())) {
      errors.push("密码过于常见")
      score -= 20
    }

    // 检查重复字符
    if (hasRepeatingChars(value)) {
      errors.push("不能包含过多重复字符")
      score -= 10
    }

    // 确保分数在0-100范围内
    score = Math.max(0, Math.min(100, score))

    setValidation({
      isValid: errors.length === 0,
      errors,
      score,
    })
  }, [value])

  const hasRepeatingChars = (password: string): boolean => {
    if (password.length < 3) return false
    let count = 1
    for (let i = 1; i < password.length; i++) {
      if (password[i] === password[i - 1]) {
        count++
        if (count >= 3) return true
      } else {
        count = 1
      }
    }
    return false
  }

  const getStrengthText = (score: number): string => {
    if (score >= 80) return "非常强"
    if (score >= 60) return "强"
    if (score >= 40) return "中等"
    if (score >= 20) return "弱"
    return "非常弱"
  }

  const getStrengthColor = (score: number): string => {
    if (score >= 80) return "bg-green-500"
    if (score >= 60) return "bg-blue-500"
    if (score >= 40) return "bg-yellow-500"
    if (score >= 20) return "bg-orange-500"
    return "bg-red-500"
  }

  const getStrengthTextColor = (score: number): string => {
    if (score >= 80) return "text-green-600"
    if (score >= 60) return "text-blue-600"
    if (score >= 40) return "text-yellow-600"
    if (score >= 20) return "text-orange-600"
    return "text-red-600"
  }

  return (
    <div className="space-y-3">
      <div className="space-y-2">
        <Label htmlFor="password">{label}</Label>
        <div className="relative">
          <Input
            id="password"
            type={showPassword ? "text" : "password"}
            value={value}
            onChange={(e) => onChange(e.target.value)}
            placeholder={placeholder}
            required={required}
            className={`pr-10 ${
              value && !validation.isValid ? "border-red-500" : ""
            }`}
          />
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
            onClick={() => setShowPassword(!showPassword)}
          >
            {showPassword ? (
              <EyeOff className="h-4 w-4 text-gray-400" />
            ) : (
              <Eye className="h-4 w-4 text-gray-400" />
            )}
          </Button>
        </div>
      </div>

      {value && showStrength && (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">密码强度</span>
            <span className={`text-sm font-medium ${getStrengthTextColor(validation.score)}`}>
              {getStrengthText(validation.score)}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all duration-300 ${getStrengthColor(validation.score)}`}
              style={{ width: `${validation.score}%` }}
            />
          </div>
        </div>
      )}

      {value && showRequirements && (
        <div className="space-y-2">
          <span className="text-sm text-gray-600">密码要求</span>
          <div className="space-y-1">
            {requirements.map((req) => {
              const isValid = req.test(value)
              return (
                <div key={req.id} className="flex items-center space-x-2">
                  {isValid ? (
                    <Check className="h-4 w-4 text-green-500" />
                  ) : (
                    <X className="h-4 w-4 text-red-500" />
                  )}
                  <span
                    className={`text-sm ${
                      isValid ? "text-green-600" : "text-red-600"
                    }`}
                  >
                    {req.text}
                  </span>
                </div>
              )
            })}
          </div>
        </div>
      )}

      {value && validation.errors.length > 0 && (
        <div className="space-y-1">
          {validation.errors.map((error, index) => (
            <p key={index} className="text-sm text-red-600">
              {error}
            </p>
          ))}
        </div>
      )}
    </div>
  )
}

// 密码确认组件
interface PasswordConfirmProps {
  password: string
  confirmPassword: string
  onConfirmPasswordChange: (value: string) => void
  label?: string
  placeholder?: string
}

export function PasswordConfirm({
  password,
  confirmPassword,
  onConfirmPasswordChange,
  label = "确认密码",
  placeholder = "请再次输入密码",
}: PasswordConfirmProps) {
  const [showPassword, setShowPassword] = useState(false)
  const isMatch = password && confirmPassword && password === confirmPassword
  const hasError = confirmPassword && !isMatch

  return (
    <div className="space-y-2">
      <Label htmlFor="confirm-password">{label}</Label>
      <div className="relative">
        <Input
          id="confirm-password"
          type={showPassword ? "text" : "password"}
          value={confirmPassword}
          onChange={(e) => onConfirmPasswordChange(e.target.value)}
          placeholder={placeholder}
          className={`pr-10 ${hasError ? "border-red-500" : isMatch ? "border-green-500" : ""}`}
        />
        <Button
          type="button"
          variant="ghost"
          size="sm"
          className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
          onClick={() => setShowPassword(!showPassword)}
        >
          {showPassword ? (
            <EyeOff className="h-4 w-4 text-gray-400" />
          ) : (
            <Eye className="h-4 w-4 text-gray-400" />
          )}
        </Button>
      </div>
      {hasError && (
        <p className="text-sm text-red-600">密码不一致</p>
      )}
      {isMatch && (
        <p className="text-sm text-green-600">密码匹配</p>
      )}
    </div>
  )
}
