# 医学数据分析平台 (MedData Analytics)

一个面向医学生和医学科研人员的多数据集医学数据分析平台，支持MIMIC、EICU、NHANES、PIC等主流医学数据库。

## 项目概述

本平台旨在让用户无需编写SQL，快速完成复杂数据查询与提取，并支持基础可视化和导出，极大降低医学数据分析门槛，辅助科研论文写作和医学学习。

## 核心功能

- ✅ **多数据集支持**: MIMIC-IV、EICU、NHANES、PIC等医学数据库
- ✅ **用户管理**: 支持医学生、医生、科研人员等不同角色
- ✅ **数据字典**: 完整的数据字段说明和搜索功能
- ✅ **可视化查询构建器**: 无需SQL的直观查询界面
- ✅ **预设查询模板**: 常用科研查询一键执行
- ✅ **数据可视化**: 多种图表类型支持
- ✅ **帮助文档**: 完整的使用教程和指南

## 技术栈

### 前端
- **框架**: Next.js 14 (App Router)
- **UI组件**: shadcn/ui + Tailwind CSS
- **图标**: Lucide React
- **状态管理**: React Hooks
- **类型检查**: TypeScript

### 后端API接口需求

以下是前端需要的后端API接口规范：

#### 1. 用户认证模块

\`\`\`typescript
// 用户注册
POST /api/auth/register
{
  name: string
  email: string
  phone?: string
  password: string
  role: 'student' | 'doctor' | 'researcher' | 'teacher' | 'analyst'
}

// 用户登录
POST /api/auth/login
{
  email: string
  password: string
}

// 获取用户信息
GET /api/auth/me
Headers: { Authorization: "Bearer <token>" }
\`\`\`

#### 2. 数据集管理模块

\`\`\`typescript
// 获取可用数据集列表
GET /api/datasets
Response: {
  datasets: Array<{
    id: string
    name: string
    description: string
    version: string
    patientCount: number
    status: 'active' | 'maintenance' | 'inactive'
    lastUpdated: string
  }>
}

// 获取数据集详细信息
GET /api/datasets/:id
Response: {
  id: string
  name: string
  description: string
  tables: Array<{
    name: string
    description: string
    recordCount: number
  }>
  fields: Array<{
    id: string
    name: string
    nameEn: string
    code: string
    type: string
    category: string
    description: string
    unit?: string
    range?: string
    examples?: string[]
  }>
}
\`\`\`

#### 3. 数据字典模块

\`\`\`typescript
// 搜索数据字段
GET /api/dictionary/search?q=<query>&dataset=<dataset>&category=<category>
Response: {
  fields: Array<DataField>
  total: number
}

// 获取字段分类
GET /api/dictionary/categories
Response: {
  categories: Array<{
    id: string
    name: string
    count: number
  }>
}
\`\`\`

#### 4. 查询构建模块

\`\`\`typescript
// 执行查询
POST /api/queries/execute
{
  dataset: string
  fields: string[]
  conditions: Array<{
    field: string
    operator: string
    value: string
    logic?: 'AND' | 'OR'
  }>
  timeRange?: {
    start: string
    end: string
  }
  limit?: number
  offset?: number
}

Response: {
  queryId: string
  data: Array<Record<string, any>>
  total: number
  executionTime: number
  status: 'completed' | 'running' | 'failed'
}

// 获取查询状态
GET /api/queries/:queryId/status
Response: {
  queryId: string
  status: 'completed' | 'running' | 'failed'
  progress?: number
  error?: string
}

// 获取查询结果
GET /api/queries/:queryId/results?page=<page>&limit=<limit>
Response: {
  data: Array<Record<string, any>>
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}
\`\`\`

#### 5. 查询模板模块

\`\`\`typescript
// 获取查询模板列表
GET /api/templates?category=<category>&dataset=<dataset>
Response: {
  templates: Array<{
    id: string
    name: string
    description: string
    category: string
    dataset: string
    fields: string[]
    conditions: Array<QueryCondition>
    author: string
    usage: number
    rating: number
    isPublic: boolean
    createdAt: string
  }>
}

// 创建查询模板
POST /api/templates
{
  name: string
  description: string
  category: string
  dataset: string
  fields: string[]
  conditions: Array<QueryCondition>
  isPublic: boolean
}

// 使用模板创建查询
POST /api/templates/:id/use
Response: {
  queryConfig: QueryConfig
}
\`\`\`

#### 6. 数据导出模块

\`\`\`typescript
// 导出查询结果
POST /api/export
{
  queryId: string
  format: 'csv' | 'excel' | 'json'
  fields?: string[]
}

Response: {
  downloadUrl: string
  expiresAt: string
}
\`\`\`

#### 7. 用户历史模块

\`\`\`typescript
// 获取查询历史
GET /api/history/queries?page=<page>&limit=<limit>
Response: {
  queries: Array<{
    id: string
    name: string
    dataset: string
    executedAt: string
    status: string
    recordCount?: number
  }>
  pagination: PaginationInfo
}

// 保存查询为模板
POST /api/history/queries/:id/save-as-template
{
  name: string
  description: string
  isPublic: boolean
}
\`\`\`

#### 8. 统计分析模块

\`\`\`typescript
// 获取基础统计信息
POST /api/analytics/basic-stats
{
  queryId: string
  fields: string[]
}

Response: {
  stats: Record<string, {
    count: number
    mean?: number
    median?: number
    std?: number
    min?: number
    max?: number
    missing: number
  }>
}

// 生成可视化数据
POST /api/analytics/visualization
{
  queryId: string
  chartType: 'bar' | 'line' | 'pie' | 'scatter'
  xField: string
  yField?: string
  groupBy?: string
}

Response: {
  chartData: Array<Record<string, any>>
  config: ChartConfig
}
\`\`\`

## 数据库设计建议

### 核心表结构

\`\`\`sql
-- 用户表
CREATE TABLE users (
  id UUID PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  phone VARCHAR(20),
  password_hash VARCHAR(255) NOT NULL,
  role VARCHAR(20) NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- 数据集表
CREATE TABLE datasets (
  id VARCHAR(50) PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  version VARCHAR(20),
  patient_count INTEGER,
  status VARCHAR(20) DEFAULT 'active',
  last_updated TIMESTAMP DEFAULT NOW()
);

-- 数据字段表
CREATE TABLE data_fields (
  id UUID PRIMARY KEY,
  dataset_id VARCHAR(50) REFERENCES datasets(id),
  name VARCHAR(100) NOT NULL,
  name_en VARCHAR(100),
  code VARCHAR(50),
  data_type VARCHAR(20),
  category VARCHAR(50),
  description TEXT,
  unit VARCHAR(20),
  value_range VARCHAR(100),
  examples JSONB
);

-- 查询历史表
CREATE TABLE query_history (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES users(id),
  name VARCHAR(200),
  dataset_id VARCHAR(50) REFERENCES datasets(id),
  query_config JSONB NOT NULL,
  status VARCHAR(20) DEFAULT 'completed',
  record_count INTEGER,
  execution_time INTEGER,
  created_at TIMESTAMP DEFAULT NOW()
);

-- 查询模板表
CREATE TABLE query_templates (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES users(id),
  name VARCHAR(200) NOT NULL,
  description TEXT,
  category VARCHAR(50),
  dataset_id VARCHAR(50) REFERENCES datasets(id),
  template_config JSONB NOT NULL,
  is_public BOOLEAN DEFAULT false,
  usage_count INTEGER DEFAULT 0,
  rating DECIMAL(2,1) DEFAULT 0,
  created_at TIMESTAMP DEFAULT NOW()
);
\`\`\`

## 安装和运行

### 前端开发环境

\`\`\`bash
# 克隆项目
git clone <repository-url>
cd medical-data-platform

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 启动生产服务器
npm start
\`\`\`

### 环境变量配置

创建 `.env.local` 文件：

\`\`\`env
# API基础URL
NEXT_PUBLIC_API_BASE_URL=http://localhost:8080/api

# 认证相关
NEXTAUTH_SECRET=your-secret-key
NEXTAUTH_URL=http://localhost:3000

# 数据库连接（如果需要）
DATABASE_URL=postgresql://username:password@localhost:5432/meddata
\`\`\`

## 部署建议

### 前端部署
- **推荐**: Vercel (与Next.js完美集成)
- **备选**: Netlify, AWS Amplify, 自建服务器

### 后端部署
- **推荐**: 
  - API服务: Docker + Kubernetes
  - 数据库: PostgreSQL (支持JSONB)
  - 缓存: Redis
  - 文件存储: AWS S3 或 MinIO

### 安全考虑
- 实施严格的数据访问控制
- 使用HTTPS加密传输
- 定期备份数据
- 实施审计日志
- 符合HIPAA、GDPR等医疗数据规范

## 开发路线图

### Phase 1 (当前)
- ✅ 基础UI界面
- ✅ 用户认证系统
- ✅ 数据字典浏览
- ✅ 查询构建器

### Phase 2 (计划中)
- 🔄 后端API开发
- 🔄 数据库集成
- 🔄 查询执行引擎
- 🔄 数据导出功能

### Phase 3 (未来)
- 📋 高级统计分析
- 📋 机器学习集成
- 📋 协作功能
- 📋 API开放平台

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系我们

- 项目维护者: [Your Name]
- 邮箱: <EMAIL>
- 项目链接: [https://github.com/your-username/medical-data-platform](https://github.com/your-username/medical-data-platform)
\`\`\`
