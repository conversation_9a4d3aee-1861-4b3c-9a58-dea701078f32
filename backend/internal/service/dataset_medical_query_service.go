package service

import (
	"context"
	"fmt"

	datasetconfig "github.com/chungzy/medical-data-platform/internal/dataset"
	"github.com/chungzy/medical-data-platform/internal/model"
	"github.com/chungzy/medical-data-platform/internal/repository"
	"github.com/chungzy/medical-data-platform/pkg/database"
	"github.com/chungzy/medical-data-platform/pkg/dataset"
	"github.com/chungzy/medical-data-platform/pkg/logger"
	"github.com/chungzy/medical-data-platform/pkg/query"
)

// DatasetMedicalQueryService 数据集特定的医学查询服务
type DatasetMedicalQueryService struct {
	configProvider  *datasetconfig.DatasetConfigProvider
	dsm             *database.DataSourceManager
	schemaRepo      repository.SchemaRepository
	datasetServices map[dataset.DatasetType]*MedicalDatasetService
	queryRepo       repository.QueryRepository
	medicalRepo     repository.MedicalQueryRepository
	dictionaryRepo  repository.DictionaryRepository
	datasetRepo     repository.DatasetRepository
}

// MedicalDatasetService 医学数据集特定服务
type MedicalDatasetService struct {
	Config       *dataset.DatasetConfig
	FieldService *MedicalFieldService
	QueryEngine  *query.QueryEngine
	Validator    *query.QueryValidator
	DatasetType  dataset.DatasetType
}

// NewDatasetMedicalQueryService 创建数据集医学查询服务
func NewDatasetMedicalQueryService(
	dsm *database.DataSourceManager,
	schemaRepo repository.SchemaRepository,
	queryRepo repository.QueryRepository,
	medicalRepo repository.MedicalQueryRepository,
	dictionaryRepo repository.DictionaryRepository,
	datasetRepo repository.DatasetRepository,
) *DatasetMedicalQueryService {
	return &DatasetMedicalQueryService{
		configProvider:  datasetconfig.NewDatasetConfigProvider(),
		dsm:             dsm,
		schemaRepo:      schemaRepo,
		datasetServices: make(map[dataset.DatasetType]*MedicalDatasetService),
		queryRepo:       queryRepo,
		medicalRepo:     medicalRepo,
		dictionaryRepo:  dictionaryRepo,
		datasetRepo:     datasetRepo,
	}
}

// getDatasetService 获取数据集特定服务
func (dmqs *DatasetMedicalQueryService) getDatasetService(datasetID string) (*MedicalDatasetService, error) {
	datasetType := dataset.DatasetType(datasetID)

	// 检查缓存
	if service, exists := dmqs.datasetServices[datasetType]; exists {
		return service, nil
	}

	// 创建新的数据集服务
	service, err := dmqs.createDatasetService(datasetType)
	if err != nil {
		return nil, fmt.Errorf("failed to create dataset service for %s: %w", datasetID, err)
	}

	// 缓存服务
	dmqs.datasetServices[datasetType] = service

	logger.Info("Created dataset service", map[string]interface{}{
		"dataset_id":   datasetID,
		"dataset_type": datasetType,
		"table_count":  service.Config.TableCount,
	})

	return service, nil
}

// createDatasetService 创建数据集服务（依赖注入方式）
func (dmqs *DatasetMedicalQueryService) createDatasetService(datasetType dataset.DatasetType) (*MedicalDatasetService, error) {
	// 验证数据集类型
	if err := dmqs.configProvider.ValidateDatasetType(datasetType); err != nil {
		return nil, err
	}

	// 获取数据集配置
	config, err := dmqs.configProvider.GetDatasetConfig(datasetType)
	if err != nil {
		return nil, err
	}

	// 获取服务配置
	serviceConfig, err := dmqs.configProvider.GetServiceConfig(datasetType)
	if err != nil {
		return nil, err
	}

	// 创建字段服务
	fieldService := NewMedicalFieldService(dmqs.schemaRepo)

	// 创建查询引擎
	cache := query.NewMemoryCache(serviceConfig.CacheSize, serviceConfig.CacheTTL)
	queryEngine := query.NewQueryEngine(dmqs.dsm, cache)
	queryEngine.SetMaxRows(serviceConfig.MaxRows)

	// 创建验证器
	validator := query.NewQueryValidator()
	validator.SetMaxFields(serviceConfig.MaxFields)
	validator.SetMaxFilters(serviceConfig.MaxFilters)
	validator.SetMaxJoins(serviceConfig.MaxJoins)

	return &MedicalDatasetService{
		Config:       config,
		FieldService: fieldService,
		QueryEngine:  queryEngine,
		Validator:    validator,
		DatasetType:  datasetType,
	}, nil
}

// GetMedicalCategories 获取数据集特定的医学字段分类
func (dmqs *DatasetMedicalQueryService) GetMedicalCategories(ctx context.Context, datasetID string) ([]model.MedicalFieldCategory, error) {
	logger.Info("Getting medical field categories for dataset", map[string]interface{}{
		"dataset_id": datasetID,
	})

	// 获取数据集特定服务
	datasetService, err := dmqs.getDatasetService(datasetID)
	if err != nil {
		logger.Error("Failed to get dataset service", err, map[string]interface{}{
			"dataset_id": datasetID,
		})
		return dmqs.getFallbackCategories(datasetID), nil
	}

	// 使用数据集特定的字段服务
	categories, err := datasetService.FieldService.GetMedicalFieldCategories(ctx, datasetID)
	if err != nil {
		logger.Error("Failed to get medical categories from dataset service", err, map[string]interface{}{
			"dataset_id": datasetID,
		})
		return dmqs.getFallbackCategories(datasetID), nil
	}

	logger.Info("Retrieved medical categories from dataset service", map[string]interface{}{
		"dataset_id":     datasetID,
		"category_count": len(categories),
	})

	return categories, nil
}

// ExecuteQuery 执行数据集特定的查询
func (dmqs *DatasetMedicalQueryService) ExecuteQuery(ctx context.Context, dsl *query.MedicalQueryDSL, userID string) (*model.MedicalQueryResponse, error) {
	logger.Info("Executing dataset-specific query", map[string]interface{}{
		"dataset_id": dsl.DatasetID,
		"query_id":   dsl.QueryID,
		"user_id":    userID,
	})

	// 获取数据集特定服务
	datasetService, err := dmqs.getDatasetService(dsl.DatasetID)
	if err != nil {
		return nil, fmt.Errorf("failed to get dataset service: %w", err)
	}

	// 使用数据集特定的验证器验证查询
	validationResult := datasetService.Validator.ValidateQuery(dsl)
	if !validationResult.Valid {
		return &model.MedicalQueryResponse{
			Status: "error",
			Error: &model.QueryError{
				Code:        "VALIDATION_FAILED",
				Message:     "Query validation failed",
				Details:     validationResult.Errors,
				Recoverable: true,
			},
		}, fmt.Errorf("query validation failed: %v", validationResult.Errors)
	}

	// 使用数据集特定的查询引擎执行查询
	result, err := datasetService.QueryEngine.ExecuteQuery(ctx, dsl)
	if err != nil {
		return &model.MedicalQueryResponse{
			Status: "error",
			Error: &model.QueryError{
				Code:        "EXECUTION_FAILED",
				Message:     err.Error(),
				Recoverable: true,
			},
		}, err
	}

	// 转换结果
	response := dmqs.convertQueryResult(result, datasetService.Config)

	logger.Info("Dataset-specific query executed successfully", map[string]interface{}{
		"dataset_id":     dsl.DatasetID,
		"query_id":       dsl.QueryID,
		"execution_time": result.ExecutionTime,
		"rows_returned":  result.Total,
	})

	return response, nil
}

// convertQueryResult 转换查询结果
func (dmqs *DatasetMedicalQueryService) convertQueryResult(result *query.QueryResult, config *dataset.DatasetConfig) *model.MedicalQueryResponse {
	response := &model.MedicalQueryResponse{
		StudyName:     result.StudyName,
		Data:          result.Data,
		Total:         result.Total,
		ExecutionTime: int(result.ExecutionTime.Milliseconds()),
		Status:        result.Status,
		Metadata:      make(map[string]interface{}),
	}

	// 添加数据集特定信息
	response.DataSource = &model.DataSourceInfo{
		ID:          string(config.Type),
		Name:        config.Name,
		Description: config.Description,
		Version:     config.Version,
		IsHealthy:   true,
	}

	// 添加元数据
	response.Metadata["dataset_type"] = config.Type
	response.Metadata["dataset_version"] = config.Version
	response.Metadata["tables_used"] = result.Metadata.TablesUsed
	response.Metadata["fields_used"] = result.Metadata.FieldsUsed
	response.Metadata["cache_hit"] = result.Metadata.CacheHit
	response.Metadata["performance"] = result.Metadata.Performance
	response.Metadata["dataset_features"] = config.Features

	return response
}

// GetDatasetInfo 获取数据集信息
func (dmqs *DatasetMedicalQueryService) GetDatasetInfo(ctx context.Context, datasetID string) (*model.DatasetInfo, error) {
	datasetService, err := dmqs.getDatasetService(datasetID)
	if err != nil {
		return nil, fmt.Errorf("failed to get dataset service: %w", err)
	}

	config := datasetService.Config
	tableMapping := dmqs.configProvider.GetTableSchemaMapping(datasetService.DatasetType)

	// 获取表信息
	var tables []model.TableInfo
	for tableName, schema := range tableMapping {
		tables = append(tables, model.TableInfo{
			Name:   tableName,
			Schema: schema,
		})
	}

	return &model.DatasetInfo{
		ID:          string(config.Type),
		Name:        config.Name,
		Description: config.Description,
		Version:     config.Version,
		Schemas:     config.Schemas,
		Tables:      tables,
		TableCount:  config.TableCount,
		Features:    config.Features,
	}, nil
}

// GetSupportedDatasets 获取支持的数据集列表
func (dmqs *DatasetMedicalQueryService) GetSupportedDatasets(ctx context.Context) ([]model.DatasetInfo, error) {
	supportedTypes := dmqs.configProvider.GetSupportedDatasets()
	var datasets []model.DatasetInfo

	for _, datasetType := range supportedTypes {
		config, err := dmqs.configProvider.GetDatasetConfig(datasetType)
		if err != nil {
			logger.Warn("Failed to get config for dataset", map[string]interface{}{
				"dataset_type": datasetType,
				"error":        err,
			})
			continue
		}

		datasets = append(datasets, model.DatasetInfo{
			ID:          string(config.Type),
			Name:        config.Name,
			Description: config.Description,
			Version:     config.Version,
			Schemas:     config.Schemas,
			TableCount:  config.TableCount,
			Features:    config.Features,
		})
	}

	return datasets, nil
}

// ValidateDatasetAccess 验证数据集访问权限
func (dmqs *DatasetMedicalQueryService) ValidateDatasetAccess(ctx context.Context, userID, datasetID string) error {
	// 这里可以实现基于用户的数据集访问控制
	// 目前简单验证数据集是否支持
	return dmqs.configProvider.ValidateDatasetType(dataset.DatasetType(datasetID))
}

// GetDatasetStats 获取数据集统计信息
func (dmqs *DatasetMedicalQueryService) GetDatasetStats(ctx context.Context, datasetID string) (*model.DatasetStats, error) {
	datasetService, err := dmqs.getDatasetService(datasetID)
	if err != nil {
		return nil, fmt.Errorf("failed to get dataset service: %w", err)
	}

	// 获取缓存统计
	cacheStats := datasetService.QueryEngine.GetCacheStats()

	return &model.DatasetStats{
		DatasetID:    datasetID,
		TableCount:   datasetService.Config.TableCount,
		CacheHitRate: cacheStats.HitRate,
		CacheSize:    cacheStats.Size,
		TotalQueries: 0, // 需要从查询历史获取
		LastAccessed: nil,
	}, nil
}

// getFallbackCategories 获取降级分类
func (dmqs *DatasetMedicalQueryService) getFallbackCategories(datasetID string) []model.MedicalFieldCategory {
	// 返回基础的医学分类作为降级方案
	return []model.MedicalFieldCategory{
		{
			ID:          "demographics",
			Name:        "人口统计学",
			Description: "患者基本信息",
			Icon:        "users",
			Fields:      []model.MedicalField{},
		},
		{
			ID:          "clinical_measurements",
			Name:        "临床测量",
			Description: "生命体征和检验结果",
			Icon:        "activity",
			Fields:      []model.MedicalField{},
		},
		{
			ID:          "temporal",
			Name:        "时间信息",
			Description: "时间相关字段",
			Icon:        "clock",
			Fields:      []model.MedicalField{},
		},
	}
}

// ClearDatasetCache 清除数据集缓存
func (dmqs *DatasetMedicalQueryService) ClearDatasetCache(datasetID string) error {
	datasetType := dataset.DatasetType(datasetID)

	if service, exists := dmqs.datasetServices[datasetType]; exists {
		service.QueryEngine.ClearCache()
		delete(dmqs.datasetServices, datasetType)

		logger.Info("Cleared dataset cache", map[string]interface{}{
			"dataset_id": datasetID,
		})
	}

	return nil
}

// GetDatasetTableInfo 获取数据集表信息
func (dmqs *DatasetMedicalQueryService) GetDatasetTableInfo(ctx context.Context, datasetID, tableName string) (*model.TableDetailInfo, error) {
	datasetService, err := dmqs.getDatasetService(datasetID)
	if err != nil {
		return nil, fmt.Errorf("failed to get dataset service: %w", err)
	}

	// 验证表是否属于该数据集
	tableMapping := dmqs.configProvider.GetTableSchemaMapping(datasetService.DatasetType)
	schema, exists := tableMapping[tableName]
	if !exists {
		return nil, fmt.Errorf("table %s not found in dataset %s", tableName, datasetID)
	}

	// 这里可以调用schema repository获取详细的表信息
	return &model.TableDetailInfo{
		Name:        tableName,
		Schema:      schema,
		Description: fmt.Sprintf("Table %s in %s dataset", tableName, datasetID),
		RowCount:    0, // 需要从实际数据库查询
		Columns:     []model.ColumnInfo{},
	}, nil
}
