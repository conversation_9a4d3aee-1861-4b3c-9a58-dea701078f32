"use client"

import React, { createContext, useContext, useEffect, useState } from "react"
import { apiClient } from "@/lib/api"

interface User {
  id: string
  name: string
  email: string
  role: string
  phone?: string
}

interface AuthContextType {
  user: User | null
  loading: boolean
  login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>
  register: (userData: any) => Promise<{ success: boolean; error?: string }>
  logout: () => void
  isAuthenticated: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    checkAuth()
  }, [])
  useEffect
  const checkAuth = async () => {
    // 检查开发模式
    const isDevMode = process.env.NEXT_PUBLIC_DEV_MODE === 'true'
    const bypassAuth = process.env.NEXT_PUBLIC_BYPASS_AUTH === 'true'

    if (bypassAuth && isDevMode) {
      // 开发模式：模拟管理员用户
      const devUser: User = {
        id: "dev-user-1",
        name: "开发用户",
        email: "<EMAIL>",
        role: "admin",
        phone: "13800138000"
      }
      setUser(devUser)
      setLoading(false)
      return
    }

    try {
      const response = await apiClient.getProfile()
      if (response.data) {
        setUser(response.data)
      }
    } catch (error) {
      console.error("Auth check failed:", error)
    } finally {
      setLoading(false)
    }
  }

  const login = async (email: string, password: string) => {
    // 检查开发模式
    const isDevMode = process.env.NEXT_PUBLIC_DEV_MODE === 'true'
    const bypassAuth = process.env.NEXT_PUBLIC_BYPASS_AUTH === 'true'

    if (bypassAuth && isDevMode) {
      // 开发模式：免密码登录
      const devUser: User = {
        id: "dev-user-1",
        name: "开发用户",
        email: email || "<EMAIL>",
        role: "admin",
        phone: "13800138000"
      }
      setUser(devUser)
      // 模拟设置token
      apiClient.setToken("dev-token-123")
      return { success: true }
    }

    try {
      const response = await apiClient.login({ email, password })
      if (response.data) {
        apiClient.setToken(response.data.token)
        setUser(response.data.user)
        return { success: true }
      } else {
        return { success: false, error: response.error || "Login failed" }
      }
    } catch (error) {
      return { success: false, error: "Network error" }
    }
  }

  const register = async (userData: any) => {
    try {
      const response = await apiClient.register(userData)
      if (response.data) {
        apiClient.setToken(response.data.token)
        setUser(response.data.user)
        return { success: true }
      } else {
        return { success: false, error: response.error || "Registration failed" }
      }
    } catch (error) {
      return { success: false, error: "Network error" }
    }
  }

  const logout = () => {
    apiClient.clearToken()
    setUser(null)
  }

  return (
    <AuthContext.Provider
      value={{
        user,
        loading,
        login,
        register,
        logout,
        isAuthenticated: !!user,
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}
