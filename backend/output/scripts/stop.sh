#!/bin/bash

# Medical Data Platform Server - Stop Script (Deployment Package)
# 医疗数据平台服务器停止脚本（部署包）

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
APP_ROOT="$(dirname "$SCRIPT_DIR")"
PID_FILE="$APP_ROOT/medical-platform.pid"

# 显示帮助
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo "Options:"
    echo "  -f, --force     强制停止（使用SIGKILL）"
    echo "  -t, --timeout   超时时间（默认30秒）"
    echo "  -h, --help      显示帮助"
}

# 停止服务
stop_service() {
    local force_kill=${1:-false}
    local timeout=${2:-30}

    log_info "停止医疗数据平台服务器..."

    # 检查PID文件
    if [[ ! -f "$PID_FILE" ]]; then
        log_warning "PID文件不存在: $PID_FILE"

        # 尝试通过进程名查找
        local pids=$(pgrep -f "medical-data-platform" 2>/dev/null || true)
        if [[ -z "$pids" ]]; then
            log_warning "未找到运行中的服务进程"
            return 0
        else
            log_info "通过进程名找到运行中的服务: PIDs $pids"
            for pid in $pids; do
                stop_process_by_pid "$pid" "$force_kill" "$timeout"
            done
            return 0
        fi
    fi

    # 读取PID
    local pid=$(cat "$PID_FILE")
    if [[ -z "$pid" ]]; then
        log_error "PID文件为空"
        return 1
    fi

    # 检查进程是否存在
    if ! kill -0 "$pid" 2>/dev/null; then
        log_warning "进程 $pid 不存在，清理PID文件"
        rm -f "$PID_FILE"
        return 0
    fi

    # 停止进程
    stop_process_by_pid "$pid" "$force_kill" "$timeout"

    # 清理PID文件
    rm -f "$PID_FILE"
    log_success "PID文件已清理"
}

# 根据PID停止进程
stop_process_by_pid() {
    local pid=$1
    local force_kill=${2:-false}
    local timeout=${3:-30}

    log_info "正在停止进程 $pid..."

    if [[ "$force_kill" == "true" ]]; then
        log_warning "强制停止进程 $pid"
        kill -KILL "$pid" 2>/dev/null || true
        sleep 1
    else
        log_info "发送SIGTERM信号到进程 $pid"
        kill -TERM "$pid" 2>/dev/null || true

        # 等待进程退出
        local count=0
        while kill -0 "$pid" 2>/dev/null && [[ $count -lt $timeout ]]; do
            sleep 1
            count=$((count + 1))
            if [[ $((count % 5)) -eq 0 ]]; then
                log_info "等待进程退出... ($count/${timeout}s)"
            fi
        done

        # 检查进程是否仍在运行
        if kill -0 "$pid" 2>/dev/null; then
            log_warning "进程未在超时时间内退出，发送SIGKILL信号"
            kill -KILL "$pid" 2>/dev/null || true
            sleep 1
        fi
    fi

    # 最终检查
    if kill -0 "$pid" 2>/dev/null; then
        log_error "无法停止进程 $pid"
        return 1
    else
        log_success "进程 $pid 已停止"
        return 0
    fi
}

# 主函数
main() {
    local force_kill=false
    local timeout=30

    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -f|--force)
                force_kill=true
                shift
                ;;
            -t|--timeout)
                timeout="$2"
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done

    stop_service "$force_kill" "$timeout"
    log_success "医疗数据平台服务已停止"
}

main "$@"
