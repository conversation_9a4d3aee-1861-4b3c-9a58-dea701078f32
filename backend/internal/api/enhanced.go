package api

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"

	"github.com/chungzy/medical-data-platform/internal/model"
	"github.com/chungzy/medical-data-platform/internal/query"
	"github.com/chungzy/medical-data-platform/internal/service"
	"github.com/chungzy/medical-data-platform/pkg/logger"
)

// EnhancedController 增强功能控制器
type EnhancedController struct {
	queryBuilder          *query.EnhancedQueryBuilder
	enhancedExportService *service.EnhancedExportServiceV2
	exportService         *service.ExportService
}

// NewEnhancedController 创建增强功能控制器
func NewEnhancedController(
	queryBuilder *query.EnhancedQueryBuilder,
	enhancedExportService *service.EnhancedExportServiceV2,
	exportService *service.ExportService,
) *EnhancedController {
	return &EnhancedController{
		queryBuilder:          queryBuilder,
		enhancedExportService: enhancedExportService,
		exportService:         exportService,
	}
}

// CreateEnhancedExport 创建增强导出
func (ec *EnhancedController) CreateEnhancedExport(c *gin.Context) {
	c.Set("business_operation", "create_enhanced_export")

	// 从JWT中获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		logger.ErrorWithGinContext(c, nil, "User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User authentication required"})
		return
	}

	userUUID, ok := userID.(uuid.UUID)
	if !ok {
		logger.ErrorWithGinContext(c, nil, "Invalid user ID format")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	var req service.ExportRequestV2
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.ErrorWithGinContext(c, err, "Invalid request for enhanced export")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request", "details": err.Error()})
		return
	}

	// 设置默认过期时间
	if req.ExpirationDays <= 0 {
		req.ExpirationDays = 7
	}

	logger.InfoWithGinContext(c, "Creating enhanced export", map[string]interface{}{
		"user_id": userUUID,
		"dataset": req.QueryDSL.Dataset,
		"format":  req.Format,
	})

	response, err := ec.enhancedExportService.CreateExport(c.Request.Context(), userUUID, &req)
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to create enhanced export")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create export", "details": err.Error()})
		return
	}

	logger.InfoWithGinContext(c, "Enhanced export created successfully", map[string]interface{}{
		"user_id":   userUUID,
		"export_id": response.ID,
		"format":    response.ExportType,
	})

	c.JSON(http.StatusOK, response)
}

// GetExportStatus 获取导出状态
func (ec *EnhancedController) GetExportStatus(c *gin.Context) {
	c.Set("business_operation", "get_export_status")

	exportIDStr := c.Param("id")
	if exportIDStr == "" {
		logger.ErrorWithGinContext(c, nil, "Export ID is required")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Export ID is required"})
		return
	}

	exportID, err := uuid.Parse(exportIDStr)
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Invalid export ID format")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid export ID"})
		return
	}

	// 从JWT中获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		logger.ErrorWithGinContext(c, nil, "User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User authentication required"})
		return
	}

	userUUID, ok := userID.(uuid.UUID)
	if !ok {
		logger.ErrorWithGinContext(c, nil, "Invalid user ID format")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	logger.InfoWithGinContext(c, "Getting export status", map[string]interface{}{
		"user_id":   userUUID,
		"export_id": exportID,
	})

	status, err := ec.exportService.GetExportStatus(c.Request.Context(), exportID, userUUID)
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to get export status")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get export status", "details": err.Error()})
		return
	}

	logger.InfoWithGinContext(c, "Export status retrieved successfully", map[string]interface{}{
		"user_id":   userUUID,
		"export_id": exportID,
		"status":    status.Status,
	})

	c.JSON(http.StatusOK, status)
}

// ValidateQuery 验证查询
func (ec *EnhancedController) ValidateQuery(c *gin.Context) {
	c.Set("business_operation", "validate_query")

	var req struct {
		QueryDSL *model.QueryDSL `json:"query_dsl" validate:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		logger.ErrorWithGinContext(c, err, "Invalid request for query validation")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request", "details": err.Error()})
		return
	}

	logger.InfoWithGinContext(c, "Validating query", map[string]interface{}{
		"dataset": req.QueryDSL.Dataset,
	})

	result := ec.queryBuilder.ValidateQuery(req.QueryDSL)

	logger.InfoWithGinContext(c, "Query validation completed", map[string]interface{}{
		"dataset": req.QueryDSL.Dataset,
		"valid":   result.Valid,
		"errors":  len(result.Errors),
		"warnings": len(result.Warnings),
	})

	c.JSON(http.StatusOK, result)
}

// GetQueryPlan 获取查询计划
func (ec *EnhancedController) GetQueryPlan(c *gin.Context) {
	c.Set("business_operation", "get_query_plan")

	var req struct {
		QueryDSL *model.QueryDSL `json:"query_dsl" validate:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		logger.ErrorWithGinContext(c, err, "Invalid request for query plan")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request", "details": err.Error()})
		return
	}

	logger.InfoWithGinContext(c, "Building query plan", map[string]interface{}{
		"dataset": req.QueryDSL.Dataset,
	})

	plan, err := ec.queryBuilder.BuildQuery(req.QueryDSL)
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to build query plan")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to build query plan", "details": err.Error()})
		return
	}

	logger.InfoWithGinContext(c, "Query plan built successfully", map[string]interface{}{
		"dataset":        req.QueryDSL.Dataset,
		"complexity":     plan.Complexity,
		"estimated_rows": plan.EstimatedRows,
	})

	c.JSON(http.StatusOK, plan)
}
