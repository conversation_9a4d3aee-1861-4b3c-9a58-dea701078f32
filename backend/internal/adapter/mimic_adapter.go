package adapter

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"github.com/chungzy/medical-data-platform/internal/model"
)

// MimicAdapter MIMIC-IV数据集适配器
type MimicAdapter struct {
	db          *sql.DB
	name        string
	description string
	version     string
	schemas     []string
	tableMaps   map[string]string
	fieldMaps   map[string]string
	joinRules   map[string]string
}

// NewMimicAdapter 创建MIMIC适配器
func NewMimicAdapter(db *sql.DB) *MimicAdapter {
	return &MimicAdapter{
		db:          db,
		name:        "mimic_iv",
		description: "MIMIC-IV Critical Care Database",
		version:     "2.2",
		schemas:     []string{"mimiciv_hosp", "mimiciv_icu", "mimiciv_ed"},
		tableMaps:   getMimicTableMaps(),
		fieldMaps:   getMimicFieldMaps(),
		joinRules:   getMimicJoinRules(),
	}
}

// GetName 获取适配器名称
func (m *MimicAdapter) GetName() string {
	return m.name
}

// GetDescription 获取描述
func (m *MimicAdapter) GetDescription() string {
	return m.description
}

// GetVersion 获取版本
func (m *MimicAdapter) GetVersion() string {
	return m.version
}

// GetSchemas 获取模式列表
func (m *MimicAdapter) GetSchemas() []string {
	return m.schemas
}

// GetTables 获取表列表
func (m *MimicAdapter) GetTables(ctx context.Context) ([]model.DatasetTable, error) {
	tables := []model.DatasetTable{
		{Name: "patients", Description: "Patient demographics", RecordCount: 382278},
		{Name: "admissions", Description: "Hospital admissions", RecordCount: 523740},
		{Name: "icustays", Description: "ICU stays", RecordCount: 76540},
		{Name: "chartevents", Description: "Chart events", RecordCount: 329499788},
		{Name: "labevents", Description: "Laboratory events", RecordCount: 122103667},
		{Name: "diagnoses", Description: "Diagnoses", RecordCount: 4756326},
		{Name: "procedures", Description: "Procedures", RecordCount: 696881},
		{Name: "prescriptions", Description: "Prescriptions", RecordCount: 17527935},
	}
	return tables, nil
}

// GetFields 获取字段列表
func (m *MimicAdapter) GetFields(ctx context.Context, table string) ([]model.DataField, error) {
	// 这里应该从数据库查询实际字段信息
	// 为了演示，返回一些示例字段
	switch table {
	case "patients":
		return []model.DataField{
			{Name: "subject_id", DataType: "integer", Category: "identifier", Description: "Patient ID"},
			{Name: "gender", DataType: "varchar", Category: "demographic", Description: "Patient gender"},
			{Name: "anchor_age", DataType: "integer", Category: "demographic", Description: "Patient age"},
			{Name: "dod", DataType: "timestamp", Category: "outcome", Description: "Date of death"},
		}, nil
	case "admissions":
		return []model.DataField{
			{Name: "hadm_id", DataType: "integer", Category: "identifier", Description: "Hospital admission ID"},
			{Name: "subject_id", DataType: "integer", Category: "identifier", Description: "Patient ID"},
			{Name: "admittime", DataType: "timestamp", Category: "temporal", Description: "Admission time"},
			{Name: "dischtime", DataType: "timestamp", Category: "temporal", Description: "Discharge time"},
			{Name: "admission_type", DataType: "varchar", Category: "clinical", Description: "Type of admission"},
		}, nil
	default:
		return []model.DataField{}, nil
	}
}

// GetFieldCategories 获取字段分类
func (m *MimicAdapter) GetFieldCategories(ctx context.Context) ([]model.FieldCategory, error) {
	categories := []model.FieldCategory{
		{ID: "identifier", Name: "标识符", Count: 15},
		{ID: "demographic", Name: "人口统计学", Count: 8},
		{ID: "temporal", Name: "时间相关", Count: 12},
		{ID: "clinical", Name: "临床信息", Count: 45},
		{ID: "laboratory", Name: "实验室检查", Count: 156},
		{ID: "vital_signs", Name: "生命体征", Count: 23},
		{ID: "medication", Name: "药物治疗", Count: 89},
		{ID: "outcome", Name: "结局指标", Count: 7},
	}
	return categories, nil
}

// BuildQuery 构建查询
func (m *MimicAdapter) BuildQuery(dsl *model.QueryDSL) (string, []interface{}, error) {
	var query strings.Builder
	var args []interface{}
	argIndex := 1

	// SELECT 子句
	selectClause := m.buildSelectClause(dsl.Select)
	query.WriteString("SELECT ")
	query.WriteString(selectClause)

	// FROM 子句
	fromTable := m.getDefaultTable()
	if dsl.From != "" {
		fromTable = m.MapTable(dsl.From)
	}
	query.WriteString(" FROM ")
	query.WriteString(fromTable)

	// JOIN 子句
	if len(dsl.Joins) > 0 {
		joinClause := m.buildJoinClause(dsl.Joins)
		query.WriteString(" ")
		query.WriteString(joinClause)
	}

	// WHERE 子句
	if dsl.Where != nil {
		whereClause, whereArgs := m.buildWhereClause(dsl.Where, argIndex)
		query.WriteString(" WHERE ")
		query.WriteString(whereClause)
		args = append(args, whereArgs...)
		argIndex += len(whereArgs)
	}

	// ORDER BY 子句
	if len(dsl.OrderBy) > 0 {
		orderByClause := m.buildOrderByClause(dsl.OrderBy)
		query.WriteString(" ORDER BY ")
		query.WriteString(orderByClause)
	}

	// LIMIT 和 OFFSET
	if dsl.Limit != nil {
		query.WriteString(fmt.Sprintf(" LIMIT %d", *dsl.Limit))
	}
	if dsl.Offset != nil {
		query.WriteString(fmt.Sprintf(" OFFSET %d", *dsl.Offset))
	}

	return query.String(), args, nil
}

// ExecuteQuery 执行查询
func (m *MimicAdapter) ExecuteQuery(ctx context.Context, sql string, args []interface{}) ([]map[string]interface{}, error) {
	rows, err := m.db.QueryContext(ctx, sql, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	// 获取列信息
	columns, err := rows.Columns()
	if err != nil {
		return nil, err
	}

	var results []map[string]interface{}
	for rows.Next() {
		// 创建扫描目标
		values := make([]interface{}, len(columns))
		valuePtrs := make([]interface{}, len(columns))
		for i := range values {
			valuePtrs[i] = &values[i]
		}

		// 扫描行
		if err := rows.Scan(valuePtrs...); err != nil {
			return nil, err
		}

		// 构建结果映射
		row := make(map[string]interface{})
		for i, col := range columns {
			val := values[i]
			if b, ok := val.([]byte); ok {
				row[col] = string(b)
			} else {
				row[col] = val
			}
		}
		results = append(results, row)
	}

	return results, rows.Err()
}

// ExecuteQueryWithCount 执行查询并返回总数
func (m *MimicAdapter) ExecuteQueryWithCount(ctx context.Context, sql string, args []interface{}) ([]map[string]interface{}, int, error) {
	// 执行查询获取数据
	results, err := m.ExecuteQuery(ctx, sql, args)
	if err != nil {
		return nil, 0, err
	}

	// 简化处理，返回结果数量作为总数
	// 实际应该执行COUNT查询
	total := len(results)
	return results, total, nil
}

// ValidateQuery 验证查询
func (m *MimicAdapter) ValidateQuery(dsl *model.QueryDSL) error {
	if dsl.Dataset != m.name {
		return fmt.Errorf("invalid dataset: expected %s, got %s", m.name, dsl.Dataset)
	}

	if len(dsl.Select) == 0 {
		return fmt.Errorf("select fields cannot be empty")
	}

	// 验证字段是否存在
	for _, field := range dsl.Select {
		if !m.isValidField(field) {
			return fmt.Errorf("invalid field: %s", field)
		}
	}

	return nil
}

// MapField 映射字段
func (m *MimicAdapter) MapField(field string) string {
	if mapped, exists := m.fieldMaps[field]; exists {
		return mapped
	}
	return field
}

// MapTable 映射表
func (m *MimicAdapter) MapTable(table string) string {
	if mapped, exists := m.tableMaps[table]; exists {
		return mapped
	}
	return table
}

// GetJoinClause 获取JOIN子句
func (m *MimicAdapter) GetJoinClause(table string) string {
	if joinClause, exists := m.joinRules[table]; exists {
		return joinClause
	}
	return ""
}

// OptimizeQuery 优化查询
func (m *MimicAdapter) OptimizeQuery(sql string) string {
	// 简单的查询优化逻辑
	// 实际应该包含更复杂的优化规则
	return sql
}

// EstimateRowCount 估算行数
func (m *MimicAdapter) EstimateRowCount(dsl *model.QueryDSL) int {
	// 简化的行数估算
	baseCount := 1000000 // 基础行数

	// 根据条件调整估算
	if dsl.Where != nil {
		baseCount = baseCount / 10 // 有条件时减少估算
	}

	if dsl.Limit != nil && *dsl.Limit < baseCount {
		return *dsl.Limit
	}

	return baseCount
}

// GetQueryComplexity 获取查询复杂度
func (m *MimicAdapter) GetQueryComplexity(dsl *model.QueryDSL) string {
	score := 0

	// 字段数量影响复杂度
	score += len(dsl.Select)

	// JOIN数量影响复杂度
	score += len(dsl.Joins) * 2

	// 条件复杂度
	if dsl.Where != nil {
		score += m.calculateConditionComplexity(dsl.Where)
	}

	if score < 5 {
		return "low"
	} else if score < 15 {
		return "medium"
	} else {
		return "high"
	}
}

// PreviewData 预览数据
func (m *MimicAdapter) PreviewData(ctx context.Context, table string, limit int) ([]map[string]interface{}, error) {
	mappedTable := m.MapTable(table)
	sql := fmt.Sprintf("SELECT * FROM %s LIMIT %d", mappedTable, limit)
	return m.ExecuteQuery(ctx, sql, nil)
}

// GetSampleData 获取样本数据
func (m *MimicAdapter) GetSampleData(ctx context.Context, table string, fields []string, limit int) ([]map[string]interface{}, error) {
	mappedTable := m.MapTable(table)
	mappedFields := make([]string, len(fields))
	for i, field := range fields {
		mappedFields[i] = m.MapField(field)
	}

	fieldList := strings.Join(mappedFields, ", ")
	sql := fmt.Sprintf("SELECT %s FROM %s LIMIT %d", fieldList, mappedTable, limit)
	return m.ExecuteQuery(ctx, sql, nil)
}

// HealthCheck 健康检查
func (m *MimicAdapter) HealthCheck(ctx context.Context) error {
	return m.db.PingContext(ctx)
}

// 辅助方法

func (m *MimicAdapter) buildSelectClause(fields []string) string {
	var mappedFields []string
	for _, field := range fields {
		mappedField := m.MapField(field)
		mappedFields = append(mappedFields, mappedField)
	}
	return strings.Join(mappedFields, ", ")
}

func (m *MimicAdapter) buildJoinClause(joins []model.JoinClause) string {
	var joinParts []string
	for _, join := range joins {
		joinType := "INNER"
		if join.Type != "" {
			joinType = strings.ToUpper(join.Type)
		}
		mappedTable := m.MapTable(join.Table)
		joinPart := fmt.Sprintf("%s JOIN %s ON %s", joinType, mappedTable, join.On)
		joinParts = append(joinParts, joinPart)
	}
	return strings.Join(joinParts, " ")
}

func (m *MimicAdapter) buildWhereClause(where *model.ConditionGroup, argIndex int) (string, []interface{}) {
	var conditions []string
	var args []interface{}

	for _, condition := range where.Conditions {
		conditionStr, conditionArgs := m.buildCondition(condition, argIndex+len(args))
		conditions = append(conditions, conditionStr)
		args = append(args, conditionArgs...)
	}

	for _, group := range where.Groups {
		groupStr, groupArgs := m.buildWhereClause(&group, argIndex+len(args))
		conditions = append(conditions, fmt.Sprintf("(%s)", groupStr))
		args = append(args, groupArgs...)
	}

	logic := "AND"
	if where.Logic != "" {
		logic = where.Logic
	}

	return strings.Join(conditions, fmt.Sprintf(" %s ", logic)), args
}

func (m *MimicAdapter) buildCondition(condition model.Condition, argIndex int) (string, []interface{}) {
	mappedField := m.MapField(condition.Field)

	switch condition.Operator {
	case "IS_NULL":
		return fmt.Sprintf("%s IS NULL", mappedField), nil
	case "IS_NOT_NULL":
		return fmt.Sprintf("%s IS NOT NULL", mappedField), nil
	case "IN":
		// 处理IN操作符
		if values, ok := condition.Value.([]interface{}); ok {
			placeholders := make([]string, len(values))
			for i := range values {
				placeholders[i] = fmt.Sprintf("$%d", argIndex+i)
			}
			return fmt.Sprintf("%s IN (%s)", mappedField, strings.Join(placeholders, ",")), values
		}
		return fmt.Sprintf("%s IN ($%d)", mappedField, argIndex), []interface{}{condition.Value}
	case "NOT_IN":
		// 处理NOT IN操作符
		if values, ok := condition.Value.([]interface{}); ok {
			placeholders := make([]string, len(values))
			for i := range values {
				placeholders[i] = fmt.Sprintf("$%d", argIndex+i)
			}
			return fmt.Sprintf("%s NOT IN (%s)", mappedField, strings.Join(placeholders, ",")), values
		}
		return fmt.Sprintf("%s NOT IN ($%d)", mappedField, argIndex), []interface{}{condition.Value}
	default:
		return fmt.Sprintf("%s %s $%d", mappedField, condition.Operator, argIndex), []interface{}{condition.Value}
	}
}

func (m *MimicAdapter) buildOrderByClause(orderBy []model.OrderClause) string {
	var orderParts []string
	for _, order := range orderBy {
		mappedField := m.MapField(order.Field)
		direction := "ASC"
		if order.Direction != "" {
			direction = order.Direction
		}
		orderParts = append(orderParts, fmt.Sprintf("%s %s", mappedField, direction))
	}
	return strings.Join(orderParts, ", ")
}

func (m *MimicAdapter) getDefaultTable() string {
	return "mimiciv_hosp.patients p"
}

func (m *MimicAdapter) isValidField(field string) bool {
	// 简化的字段验证
	// 实际应该从数据字典中验证
	validFields := map[string]bool{
		"subject_id": true, "hadm_id": true, "gender": true, "anchor_age": true,
		"admittime": true, "dischtime": true, "admission_type": true, "dod": true,
	}
	return validFields[field] || validFields[m.MapField(field)]
}

func (m *MimicAdapter) calculateConditionComplexity(where *model.ConditionGroup) int {
	complexity := len(where.Conditions)
	for _, group := range where.Groups {
		complexity += m.calculateConditionComplexity(&group)
	}
	return complexity
}

// 配置映射

func getMimicTableMaps() map[string]string {
	return map[string]string{
		"patients":      "mimiciv_hosp.patients",
		"admissions":    "mimiciv_hosp.admissions",
		"icustays":      "mimiciv_icu.icustays",
		"chartevents":   "mimiciv_icu.chartevents",
		"labevents":     "mimiciv_hosp.labevents",
		"diagnoses":     "mimiciv_hosp.diagnoses_icd",
		"procedures":    "mimiciv_hosp.procedures_icd",
		"prescriptions": "mimiciv_hosp.prescriptions",
	}
}

func getMimicFieldMaps() map[string]string {
	return map[string]string{
		"patient_id":     "subject_id",
		"admission_id":   "hadm_id",
		"stay_id":        "stay_id",
		"age":            "anchor_age",
		"gender":         "gender",
		"admission_time": "admittime",
		"discharge_time": "dischtime",
		"death_time":     "deathtime",
		"admission_type": "admission_type",
		"diagnosis":      "long_title",
		"icd_code":       "icd_code",
		"lab_value":      "valuenum",
		"lab_item":       "label",
	}
}

func getMimicJoinRules() map[string]string {
	return map[string]string{
		"admissions":    " LEFT JOIN mimiciv_hosp.admissions a ON p.subject_id = a.subject_id",
		"icustays":      " LEFT JOIN mimiciv_icu.icustays i ON a.hadm_id = i.hadm_id",
		"chartevents":   " LEFT JOIN mimiciv_icu.chartevents c ON i.stay_id = c.stay_id",
		"labevents":     " LEFT JOIN mimiciv_hosp.labevents l ON a.hadm_id = l.hadm_id",
		"diagnoses":     " LEFT JOIN mimiciv_hosp.diagnoses_icd d ON a.hadm_id = d.hadm_id",
		"procedures":    " LEFT JOIN mimiciv_hosp.procedures_icd pr ON a.hadm_id = pr.hadm_id",
		"prescriptions": " LEFT JOIN mimiciv_hosp.prescriptions rx ON a.hadm_id = rx.hadm_id",
	}
}
