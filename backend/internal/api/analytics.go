package api

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"

	"github.com/chungzy/medical-data-platform/internal/model"
	"github.com/chungzy/medical-data-platform/internal/service"
	"github.com/chungzy/medical-data-platform/pkg/logger"
)

type AnalyticsController struct {
	analyticsService *service.AnalyticsService
}

func NewAnalyticsController(analyticsService *service.AnalyticsService) *AnalyticsController {
	return &AnalyticsController{
		analyticsService: analyticsService,
	}
}

// BasicStats 基础统计分析
func (ac *AnalyticsController) BasicStats(c *gin.Context) {
	c.Set("business_operation", "basic_stats")

	logger.InfoWithGinContext(c, "Getting basic stats")

	userID, exists := c.Get("user_id")
	if !exists {
		logger.WarnWithGinContext(c, "User ID not found in context")
		c.<PERSON>(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req model.BasicStatsRequest
	if err := c.ShouldBind<PERSON>SON(&req); err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to parse basic stats request")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format", "details": err.Error()})
		return
	}

	logger.InfoWithGinContext(c, "Basic stats request parsed", map[string]interface{}{
		"user_id": userID,
	})

	stats, err := ac.analyticsService.GetBasicStats(c.Request.Context(), userID.(uuid.UUID), &req)
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to get basic stats", map[string]interface{}{
			"user_id": userID,
		})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get basic stats", "details": err.Error()})
		return
	}

	logger.InfoWithGinContext(c, "Basic stats retrieved successfully", map[string]interface{}{
		"user_id": userID,
	})

	c.JSON(http.StatusOK, stats)
}

// GenerateVisualization 生成可视化数据
func (ac *AnalyticsController) GenerateVisualization(c *gin.Context) {
	c.Set("business_operation", "generate_visualization")

	logger.InfoWithGinContext(c, "Generating visualization")

	userID, exists := c.Get("user_id")
	if !exists {
		logger.WarnWithGinContext(c, "User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req model.VisualizationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to parse visualization request")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format", "details": err.Error()})
		return
	}

	logger.InfoWithGinContext(c, "Visualization request parsed", map[string]interface{}{
		"user_id":    userID,
		"chart_type": req.ChartType,
	})

	visualization, err := ac.analyticsService.GenerateVisualization(c.Request.Context(), userID.(uuid.UUID), &req)
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to generate visualization", map[string]interface{}{
			"user_id":    userID,
			"chart_type": req.ChartType,
		})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate visualization", "details": err.Error()})
		return
	}

	logger.InfoWithGinContext(c, "Visualization generated successfully", map[string]interface{}{
		"user_id":    userID,
		"chart_type": req.ChartType,
	})

	c.JSON(http.StatusOK, visualization)
}
