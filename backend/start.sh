#!/bin/bash

# Medical Data Platform Server - Start Script (Root)
# 医疗数据平台服务器启动脚本（根目录）

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 检查是否有output目录（部署包）
if [[ -d "$SCRIPT_DIR/output" && -f "$SCRIPT_DIR/output/scripts/start.sh" ]]; then
    echo "检测到部署包，使用部署包启动脚本..."
    exec "$SCRIPT_DIR/output/scripts/start.sh" "$@"
else
    # 执行开发环境的启动脚本
    exec "$SCRIPT_DIR/scripts/start.sh" "$@"
fi
