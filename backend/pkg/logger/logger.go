package logger

import (
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"runtime"

	"github.com/sirupsen/logrus"
	"gopkg.in/natefinch/lumberjack.v2"

	"github.com/chungzy/medical-data-platform/internal/config"
)

var (
	Logger    *logrus.Logger
	GinLogger *logrus.Logger
)

// LogLevelHook 实现日志级别分离
type LogLevelHook struct {
	infoWriter io.Writer
	warnWriter io.Writer
}

func (hook *LogLevelHook) Levels() []logrus.Level {
	return logrus.AllLevels
}

func (hook *LogLevelHook) Fire(entry *logrus.Entry) error {
	line, err := entry.String()
	if err != nil {
		return err
	}

	switch entry.Level {
	case logrus.PanicLevel, logrus.FatalLevel, logrus.ErrorLevel, logrus.WarnLevel:
		_, err = hook.warnWriter.Write([]byte(line))
	case logrus.InfoLevel, logrus.DebugLevel, logrus.TraceLevel:
		_, err = hook.infoWriter.Write([]byte(line))
	}

	return err
}

// InitWithConfig 使用配置初始化日志系统
func InitWithConfig(cfg *config.Config) error {
	// 初始化应用日志
	Logger = logrus.New()
	if err := setupLoggerWithConfig(Logger, "app", &cfg.App.Log); err != nil {
		return fmt.Errorf("failed to setup app logger: %w", err)
	}

	// 初始化Gin日志
	GinLogger = logrus.New()
	if err := setupLoggerWithConfig(GinLogger, "gin", &cfg.App.Log); err != nil {
		return fmt.Errorf("failed to setup gin logger: %w", err)
	}

	return nil
}

func init() {
	// 初始化应用日志（兼容性保持）
	Logger = logrus.New()
	setupLoggerCompat(Logger, "app")

	// 初始化Gin日志（兼容性保持）
	GinLogger = logrus.New()
	setupLoggerCompat(GinLogger, "gin")
}

func setupLoggerCompat(logger *logrus.Logger, logType string) {
	// 设置输出格式为文本格式（非JSON）
	logger.SetFormatter(&logrus.TextFormatter{
		TimestampFormat: "2006-01-02 15:04:05",
		FullTimestamp:   true,
		DisableColors:   false, // 在控制台显示颜色
		CallerPrettyfier: func(f *runtime.Frame) (string, string) {
			filename := filepath.Base(f.File)
			return "", fmt.Sprintf("%s:%d", filename, f.Line)
		},
	})

	// 启用调用者信息（文件名和行号）
	logger.SetReportCaller(true)

	// 设置日志级别
	level := os.Getenv("LOG_LEVEL")
	switch level {
	case "debug":
		logger.SetLevel(logrus.DebugLevel)
	case "info":
		logger.SetLevel(logrus.InfoLevel)
	case "warn":
		logger.SetLevel(logrus.WarnLevel)
	case "error":
		logger.SetLevel(logrus.ErrorLevel)
	default:
		logger.SetLevel(logrus.InfoLevel)
	}

	// 设置日志输出
	setupLogOutputCompat(logger, logType)
}

func setupLogOutputCompat(logger *logrus.Logger, logType string) {
	// 获取日志目录
	logDir := os.Getenv("LOG_DIR")
	if logDir == "" {
		logDir = "logs"
	}

	// 创建日志目录
	if err := os.MkdirAll(logDir, 0755); err != nil {
		logger.Warn("Failed to create log directory, using stdout")
		logger.SetOutput(os.Stdout)
		return
	}

	if logType == "app" {
		// App日志需要分级输出
		setupAppLogOutputCompat(logger, logDir)
	} else {
		// Gin日志保持原有逻辑，输出到单个文件
		setupSingleLogOutputCompat(logger, logDir, logType)
	}
}

func setupAppLogOutputCompat(logger *logrus.Logger, logDir string) {
	// 设置app.log文件（INFO及以下级别）
	infoLogFile := filepath.Join(logDir, "app.log")
	infoLumberjack := &lumberjack.Logger{
		Filename:   infoLogFile,
		MaxSize:    100,  // MB
		MaxBackups: 10,   // 保留10个备份
		MaxAge:     30,   // 保留30天
		Compress:   true, // 压缩旧日志
		LocalTime:  true, // 使用本地时间
	}

	// 设置app.log.wf文件（WARN及以上级别）
	warnLogFile := filepath.Join(logDir, "app.log.wf")
	warnLumberjack := &lumberjack.Logger{
		Filename:   warnLogFile,
		MaxSize:    100,  // MB
		MaxBackups: 10,   // 保留10个备份
		MaxAge:     30,   // 保留30天
		Compress:   true, // 压缩旧日志
		LocalTime:  true, // 使用本地时间
	}

	// 创建分级Hook
	levelHook := &LogLevelHook{
		infoWriter: infoLumberjack,
		warnWriter: warnLumberjack,
	}

	// 根据环境决定是否同时输出到控制台
	env := os.Getenv("GIN_MODE")
	if env != "release" {
		// 开发环境：同时输出到控制台
		levelHook.infoWriter = io.MultiWriter(os.Stdout, infoLumberjack)
		levelHook.warnWriter = io.MultiWriter(os.Stdout, warnLumberjack)
	}

	// 添加Hook并禁用默认输出
	logger.AddHook(levelHook)
	logger.SetOutput(io.Discard) // 禁用默认输出，使用Hook处理
}

func setupSingleLogOutputCompat(logger *logrus.Logger, logDir string, logType string) {
	// 设置日志文件路径
	logFile := filepath.Join(logDir, logType+".log")

	// 配置日志轮转
	lumberjackLogger := &lumberjack.Logger{
		Filename:   logFile,
		MaxSize:    100,  // MB
		MaxBackups: 10,   // 保留10个备份
		MaxAge:     30,   // 保留30天
		Compress:   true, // 压缩旧日志
		LocalTime:  true, // 使用本地时间
	}

	// 根据环境决定输出方式
	env := os.Getenv("GIN_MODE")
	if env == "release" {
		// 生产环境：只输出到文件
		logger.SetOutput(lumberjackLogger)
	} else {
		// 开发环境：同时输出到控制台和文件
		multiWriter := io.MultiWriter(os.Stdout, lumberjackLogger)
		logger.SetOutput(multiWriter)
	}
}

// WithLogID 创建带LogID的日志条目
func WithLogID(logID string) *logrus.Entry {
	return Logger.WithField("log_id", logID)
}

// WithContext 从上下文中提取LogID创建日志条目
func WithContext(ctx context.Context) *logrus.Entry {
	if logID := ctx.Value("log_id"); logID != nil {
		return Logger.WithField("log_id", logID)
	}
	return Logger.WithFields(logrus.Fields{})
}

// WithFields 创建带字段的日志条目
func WithFields(fields logrus.Fields) *logrus.Entry {
	return Logger.WithFields(fields)
}

// WithField 创建带单个字段的日志条目
func WithField(key string, value interface{}) *logrus.Entry {
	return Logger.WithField(key, value)
}

// WithBusinessContext 创建带业务上下文的日志条目
func WithBusinessContext(logID, userID, operation string) *logrus.Entry {
	return Logger.WithFields(logrus.Fields{
		"log_id":    logID,
		"user_id":   userID,
		"operation": operation,
	})
}

// Debug 记录调试信息
func Debug(args ...interface{}) {
	Logger.Debug(args...)
}

// Debugf 记录格式化调试信息
func Debugf(format string, args ...interface{}) {
	Logger.Debugf(format, args...)
}

// Info 记录信息
func Info(args ...interface{}) {
	Logger.Info(args...)
}

// Infof 记录格式化信息
func Infof(format string, args ...interface{}) {
	Logger.Infof(format, args...)
}

// Warn 记录警告
func Warn(args ...interface{}) {
	Logger.Warn(args...)
}

// Warnf 记录格式化警告
func Warnf(format string, args ...interface{}) {
	Logger.Warnf(format, args...)
}

// Error 记录错误
func Error(args ...interface{}) {
	Logger.Error(args...)
}

// Errorf 记录格式化错误
func Errorf(format string, args ...interface{}) {
	Logger.Errorf(format, args...)
}

// Fatal 记录致命错误并退出
func Fatal(args ...interface{}) {
	Logger.Fatal(args...)
}

// Fatalf 记录格式化致命错误并退出
func Fatalf(format string, args ...interface{}) {
	Logger.Fatalf(format, args...)
}

// Gin日志相关函数
func GinDebug(args ...interface{}) {
	GinLogger.Debug(args...)
}

func GinInfo(args ...interface{}) {
	GinLogger.Info(args...)
}

func GinWarn(args ...interface{}) {
	GinLogger.Warn(args...)
}

func GinError(args ...interface{}) {
	GinLogger.Error(args...)
}

// GetGinWriter 获取Gin日志写入器
func GetGinWriter() io.Writer {
	return GinLogger.Writer()
}

func setupLoggerWithConfig(logger *logrus.Logger, logType string, logConfig *config.LogConfig) error {
	// 设置日志格式
	if logConfig.EnableStructured {
		logger.SetFormatter(&logrus.JSONFormatter{
			TimestampFormat: "2006-01-02 15:04:05",
		})
	} else {
		logger.SetFormatter(&logrus.TextFormatter{
			TimestampFormat: "2006-01-02 15:04:05",
			FullTimestamp:   true,
			DisableColors:   false,
			CallerPrettyfier: func(f *runtime.Frame) (string, string) {
				filename := filepath.Base(f.File)
				return "", fmt.Sprintf("%s:%d", filename, f.Line)
			},
		})
	}

	// 启用调用者信息
	logger.SetReportCaller(true)

	// 设置日志级别 - 从全局配置获取
	level, err := logrus.ParseLevel(os.Getenv("LOG_LEVEL"))
	if err != nil {
		logger.SetLevel(logrus.InfoLevel)
	} else {
		logger.SetLevel(level)
	}

	// 设置日志输出
	return setupLogOutputWithConfig(logger, logType, logConfig)
}

func setupLogOutputWithConfig(logger *logrus.Logger, logType string, logConfig *config.LogConfig) error {
	// 创建日志目录
	if err := os.MkdirAll(logConfig.Dir, 0755); err != nil {
		logger.Warn("Failed to create log directory, using stdout")
		logger.SetOutput(os.Stdout)
		return nil
	}

	if logType == "app" {
		// App日志分级输出
		return setupAppLogOutputWithConfig(logger, logConfig)
	} else {
		// Gin日志单文件输出
		return setupSingleLogOutputWithConfig(logger, logType, logConfig)
	}
}

func setupAppLogOutputWithConfig(logger *logrus.Logger, logConfig *config.LogConfig) error {
	// 设置app.log文件（INFO及以下级别）
	infoLogFile := filepath.Join(logConfig.Dir, "app.log")
	infoLumberjack := &lumberjack.Logger{
		Filename:   infoLogFile,
		MaxSize:    logConfig.MaxSize,
		MaxBackups: logConfig.MaxBackups,
		MaxAge:     logConfig.MaxAge,
		Compress:   logConfig.Compress,
		LocalTime:  true,
	}

	// 设置app.log.wf文件（WARN及以上级别）
	warnLogFile := filepath.Join(logConfig.Dir, "app.log.wf")
	warnLumberjack := &lumberjack.Logger{
		Filename:   warnLogFile,
		MaxSize:    logConfig.MaxSize,
		MaxBackups: logConfig.MaxBackups,
		MaxAge:     logConfig.MaxAge,
		Compress:   logConfig.Compress,
		LocalTime:  true,
	}

	// 创建分级Hook
	levelHook := &LogLevelHook{
		infoWriter: infoLumberjack,
		warnWriter: warnLumberjack,
	}

	// 根据配置决定是否同时输出到控制台
	if logConfig.EnableConsole {
		levelHook.infoWriter = io.MultiWriter(os.Stdout, infoLumberjack)
		levelHook.warnWriter = io.MultiWriter(os.Stdout, warnLumberjack)
	}

	// 添加Hook并禁用默认输出
	logger.AddHook(levelHook)
	logger.SetOutput(io.Discard)

	return nil
}

func setupSingleLogOutputWithConfig(logger *logrus.Logger, logType string, logConfig *config.LogConfig) error {
	// 设置日志文件路径
	logFile := filepath.Join(logConfig.Dir, logType+".log")

	// 配置日志轮转
	lumberjackLogger := &lumberjack.Logger{
		Filename:   logFile,
		MaxSize:    logConfig.MaxSize,
		MaxBackups: logConfig.MaxBackups,
		MaxAge:     logConfig.MaxAge,
		Compress:   logConfig.Compress,
		LocalTime:  true,
	}

	// 根据配置决定输出方式
	if logConfig.EnableConsole {
		multiWriter := io.MultiWriter(os.Stdout, lumberjackLogger)
		logger.SetOutput(multiWriter)
	} else {
		logger.SetOutput(lumberjackLogger)
	}

	return nil
}
