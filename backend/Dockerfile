# 多阶段构建 Dockerfile for Medical Data Platform Server

# 阶段1: 构建阶段
FROM golang:1.21-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装必要的系统依赖
RUN apk add --no-cache git ca-certificates tzdata

# 复制go mod文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
    -ldflags='-w -s -extldflags "-static"' \
    -a -installsuffix cgo \
    -o medical-platform-server \
    cmd/server/main.go

# 阶段2: 运行时阶段
FROM alpine:3.18

# 安装运行时依赖
RUN apk --no-cache add ca-certificates tzdata curl

# 创建非root用户
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

# 设置工作目录
WORKDIR /app

# 从构建阶段复制二进制文件
COPY --from=builder /app/medical-platform-server .

# 复制配置文件和脚本
COPY --from=builder /app/configs ./configs
COPY --from=builder /app/scripts ./scripts
COPY --from=builder /app/migrations ./migrations

# 创建必要的目录
RUN mkdir -p logs exports tmp && \
    chown -R appuser:appgroup /app

# 设置权限
RUN chmod +x medical-platform-server && \
    chmod +x scripts/*.sh

# 切换到非root用户
USER appuser

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# 设置环境变量
ENV GIN_MODE=release
ENV LOG_LEVEL=info
ENV LOG_DIR=/app/logs

# 启动应用
CMD ["./medical-platform-server"] 