package repository

import (
	"context"
	"database/sql"
)

type analyticsRepository struct {
	db *sql.DB
}

func NewAnalyticsRepository(db *sql.DB) AnalyticsRepository {
	return &analyticsRepository{db: db}
}

func (r *analyticsRepository) GetBasicStats(ctx context.Context, sql string, args []interface{}, fields []string) (map[string]interface{}, error) {
	// 这里简化实现，实际应该根据字段类型执行不同的统计查询
	stats := make(map[string]interface{})

	// 执行查询获取数据
	rows, err := r.db.QueryContext(ctx, sql, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	// 获取列信息
	columns, err := rows.Columns()
	if err != nil {
		return nil, err
	}

	// 简化统计计算
	var count int
	for rows.Next() {
		count++
	}

	// 为每个字段生成基础统计信息
	for _, field := range fields {
		fieldStats := map[string]interface{}{
			"count":   count,
			"missing": 0,
		}

		// 检查字段是否存在于查询结果中
		fieldExists := false
		for _, col := range columns {
			if col == field {
				fieldExists = true
				break
			}
		}

		if fieldExists {
			// 这里可以添加更复杂的统计计算
			// 目前简化处理
			fieldStats["mean"] = 0.0
			fieldStats["median"] = 0.0
			fieldStats["std"] = 0.0
			fieldStats["min"] = 0.0
			fieldStats["max"] = 0.0
		}

		stats[field] = fieldStats
	}

	return stats, nil
}

func (r *analyticsRepository) GetAggregatedData(ctx context.Context, sql string, args []interface{}, groupBy string) ([]map[string]interface{}, error) {
	rows, err := r.db.QueryContext(ctx, sql, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	// 获取列信息
	columns, err := rows.Columns()
	if err != nil {
		return nil, err
	}

	// 读取聚合数据
	var results []map[string]interface{}
	for rows.Next() {
		values := make([]interface{}, len(columns))
		valuePtrs := make([]interface{}, len(columns))
		for i := range values {
			valuePtrs[i] = &values[i]
		}

		if err := rows.Scan(valuePtrs...); err != nil {
			return nil, err
		}

		row := make(map[string]interface{})
		for i, col := range columns {
			val := values[i]
			if b, ok := val.([]byte); ok {
				row[col] = string(b)
			} else {
				row[col] = val
			}
		}
		results = append(results, row)
	}

	return results, rows.Err()
}
