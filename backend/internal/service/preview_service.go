package service

import (
	"context"
	"fmt"
	"sort"
	"strings"

	"github.com/chungzy/medical-data-platform/internal/model"
	"github.com/chungzy/medical-data-platform/internal/query"
	"github.com/chungzy/medical-data-platform/pkg/logger"
)

// PreviewService 数据预览服务
type PreviewService struct {
	queryBuilder *query.EnhancedQueryBuilder
}

// NewPreviewService 创建预览服务
func NewPreviewService(queryBuilder *query.EnhancedQueryBuilder) *PreviewService {
	return &PreviewService{
		queryBuilder: queryBuilder,
	}
}

// PreviewTableData 预览表数据
func (s *PreviewService) PreviewTableData(ctx context.Context, req *TablePreviewRequest) (*TablePreviewResponse, error) {
	// 验证参数
	if req.Dataset == "" {
		return nil, fmt.Errorf("dataset is required")
	}
	if req.Table == "" {
		return nil, fmt.Errorf("table is required")
	}

	// 设置默认限制
	limit := req.Limit
	if limit <= 0 || limit > 1000 {
		limit = 100
	}

	// 获取预览数据
	data, err := s.queryBuilder.PreviewData(ctx, req.Dataset, req.Table, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to preview data: %w", err)
	}

	// 获取表的元数据
	metadata, err := s.getTableMetadata(ctx, req.Dataset, req.Table)
	if err != nil {
		logger.Error("Failed to get table metadata", err, map[string]interface{}{
			"dataset": req.Dataset,
			"table":   req.Table,
		})
		// 不阻断预览，继续返回数据
	}

	// 分析数据特征
	analysis := s.analyzeData(data)

	return &TablePreviewResponse{
		Dataset:  req.Dataset,
		Table:    req.Table,
		Data:     data,
		Metadata: metadata,
		Analysis: analysis,
		Count:    len(data),
		Limit:    limit,
	}, nil
}

// PreviewQueryResult 预览查询结果
func (s *PreviewService) PreviewQueryResult(ctx context.Context, req *QueryPreviewRequest) (*QueryPreviewResponse, error) {
	// 验证查询
	validationResult := s.queryBuilder.ValidateQuery(req.QueryDSL)
	if !validationResult.Valid {
		return &QueryPreviewResponse{
			Valid:    false,
			Errors:   validationResult.Errors,
			Warnings: validationResult.Warnings,
		}, nil
	}

	// 限制预览查询的结果数量
	previewDSL := *req.QueryDSL
	previewLimit := 50
	if req.Limit > 0 && req.Limit < 200 {
		previewLimit = req.Limit
	}
	previewDSL.Limit = &previewLimit

	// 执行预览查询
	queryOptions := &query.QueryOptions{
		EnableCache:  true,
		CacheTimeout: 60, // 1分钟缓存
		WithCount:    false,
		Timeout:      30, // 30秒超时
		MaxRows:      200,
	}

	result, err := s.queryBuilder.BuildAndExecute(ctx, &previewDSL, queryOptions)
	if err != nil {
		return &QueryPreviewResponse{
			Valid:   false,
			Errors:  []string{err.Error()},
			QueryID: req.QueryID,
		}, nil
	}

	// 分析查询结果
	analysis := s.analyzeData(result.Data)

	// 获取查询计划
	plan, err := s.queryBuilder.BuildQuery(req.QueryDSL)
	if err != nil {
		logger.Error("Failed to get query plan", err, map[string]interface{}{
			"dataset": req.QueryDSL.Dataset,
		})
	}

	return &QueryPreviewResponse{
		Valid:         true,
		QueryID:       req.QueryID,
		Data:          result.Data,
		Analysis:      analysis,
		Count:         len(result.Data),
		ExecutionTime: result.ExecutionTime,
		SQL:           result.SQL,
		Plan:          plan,
		Warnings:      append(validationResult.Warnings, result.Warnings...),
	}, nil
}

// GetDatasetOverview 获取数据集概览
func (s *PreviewService) GetDatasetOverview(ctx context.Context, dataset string) (*DatasetOverviewResponse, error) {
	// 获取元数据
	metadata, err := s.queryBuilder.GetMetadata(ctx, dataset)
	if err != nil {
		return nil, fmt.Errorf("failed to get metadata: %w", err)
	}

	// 计算统计信息
	stats := s.calculateDatasetStats(metadata)

	// 获取热门表（按记录数排序）
	popularTables := s.getPopularTables(metadata.Tables, 5)

	// 获取字段分布
	fieldDistribution := s.getFieldDistribution(metadata.Fields)

	return &DatasetOverviewResponse{
		Dataset:           dataset,
		Description:       s.getDatasetDescription(metadata),
		Tables:            metadata.Tables,
		PopularTables:     popularTables,
		FieldCategories:   metadata.Categories,
		FieldDistribution: fieldDistribution,
		Statistics:        stats,
		LastUpdated:       metadata.Statistics,
	}, nil
}

// GetSampleData 获取样本数据
func (s *PreviewService) GetSampleData(ctx context.Context, req *SampleDataRequest) (*SampleDataResponse, error) {
	// 验证参数
	if req.Dataset == "" || req.Table == "" {
		return nil, fmt.Errorf("dataset and table are required")
	}

	limit := req.Limit
	if limit <= 0 || limit > 100 {
		limit = 10
	}

	// 获取样本数据
	data, err := s.queryBuilder.GetSampleData(ctx, req.Dataset, req.Table, req.Fields, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get sample data: %w", err)
	}

	// 获取字段信息
	var fieldInfo []model.DataField
	if len(req.Fields) > 0 {
		// 获取指定字段的信息
		allFields, err := s.getTableFields(ctx, req.Dataset, req.Table)
		if err == nil {
			fieldMap := make(map[string]model.DataField)
			for _, field := range allFields {
				fieldMap[field.Name] = field
			}
			for _, fieldName := range req.Fields {
				if field, exists := fieldMap[fieldName]; exists {
					fieldInfo = append(fieldInfo, field)
				}
			}
		}
	}

	return &SampleDataResponse{
		Dataset:   req.Dataset,
		Table:     req.Table,
		Fields:    req.Fields,
		Data:      data,
		FieldInfo: fieldInfo,
		Count:     len(data),
		Limit:     limit,
	}, nil
}

// 辅助方法

func (s *PreviewService) getTableMetadata(ctx context.Context, dataset, table string) (*TableMetadata, error) {
	// 获取字段信息
	fields, err := s.getTableFields(ctx, dataset, table)
	if err != nil {
		return nil, err
	}

	// 计算字段统计
	fieldStats := make(map[string]FieldStatistics)
	for _, field := range fields {
		fieldStats[field.Name] = FieldStatistics{
			DataType:    field.DataType,
			Category:    field.Category,
			Description: field.Description,
			Unit:        field.Unit,
			ValueRange:  field.ValueRange,
		}
	}

	return &TableMetadata{
		Fields:      fields,
		FieldStats:  fieldStats,
		RecordCount: 0, // 需要实际查询获取
	}, nil
}

func (s *PreviewService) getTableFields(ctx context.Context, dataset, table string) ([]model.DataField, error) {
	metadata, err := s.queryBuilder.GetMetadata(ctx, dataset)
	if err != nil {
		return nil, err
	}

	var fields []model.DataField
	for _, field := range metadata.Fields {
		// 简化的字段过滤，实际应该根据表名过滤
		if strings.Contains(field.Name, table) || field.Category != "" {
			fields = append(fields, field)
		}
	}

	return fields, nil
}

func (s *PreviewService) analyzeData(data []map[string]interface{}) *DataAnalysis {
	if len(data) == 0 {
		return &DataAnalysis{
			RowCount:    0,
			ColumnCount: 0,
		}
	}

	// 分析列
	columns := make(map[string]*ColumnAnalysis)
	for _, row := range data {
		for col, val := range row {
			if _, exists := columns[col]; !exists {
				columns[col] = &ColumnAnalysis{
					Name:         col,
					DataType:     s.inferDataType(val),
					NullCount:    0,
					UniqueValues: make(map[interface{}]bool),
				}
			}

			if val == nil {
				columns[col].NullCount++
			} else {
				columns[col].UniqueValues[val] = true
			}
		}
	}

	// 转换为切片
	var columnAnalyses []ColumnAnalysis
	for _, analysis := range columns {
		analysis.UniqueCount = len(analysis.UniqueValues)
		analysis.UniqueValues = nil // 清除详细值，节省内存
		columnAnalyses = append(columnAnalyses, *analysis)
	}

	return &DataAnalysis{
		RowCount:    len(data),
		ColumnCount: len(columns),
		Columns:     columnAnalyses,
	}
}

func (s *PreviewService) inferDataType(val interface{}) string {
	if val == nil {
		return "null"
	}

	switch val.(type) {
	case int, int32, int64:
		return "integer"
	case float32, float64:
		return "float"
	case bool:
		return "boolean"
	case string:
		return "string"
	default:
		return "unknown"
	}
}

func (s *PreviewService) calculateDatasetStats(metadata *query.MetadataInfo) map[string]interface{} {
	stats := make(map[string]interface{})

	stats["total_tables"] = len(metadata.Tables)
	stats["total_fields"] = len(metadata.Fields)

	// 计算字段分类统计
	categoryCount := make(map[string]int)
	for _, field := range metadata.Fields {
		categoryCount[field.Category]++
	}
	stats["field_categories"] = categoryCount

	// 计算总记录数
	totalRecords := 0
	for _, table := range metadata.Tables {
		totalRecords += table.RecordCount
	}
	stats["total_records"] = totalRecords

	return stats
}

func (s *PreviewService) getPopularTables(tables []model.DatasetTable, limit int) []model.DatasetTable {
	// 按记录数排序
	sorted := make([]model.DatasetTable, len(tables))
	copy(sorted, tables)

	sort.Slice(sorted, func(i, j int) bool {
		return sorted[i].RecordCount > sorted[j].RecordCount
	})

	if len(sorted) > limit {
		sorted = sorted[:limit]
	}

	return sorted
}

func (s *PreviewService) getFieldDistribution(fields []model.DataField) map[string]int {
	distribution := make(map[string]int)
	for _, field := range fields {
		distribution[field.Category]++
	}
	return distribution
}

func (s *PreviewService) getDatasetDescription(metadata *query.MetadataInfo) string {
	if adapterInfo, ok := metadata.Statistics["adapter_info"].(map[string]interface{}); ok {
		if desc, ok := adapterInfo["description"].(string); ok {
			return desc
		}
	}
	return "Medical dataset"
}

// 数据结构定义

// TablePreviewRequest 表预览请求
type TablePreviewRequest struct {
	Dataset string `json:"dataset" validate:"required"`
	Table   string `json:"table" validate:"required"`
	Limit   int    `json:"limit,omitempty"`
}

// TablePreviewResponse 表预览响应
type TablePreviewResponse struct {
	Dataset  string                   `json:"dataset"`
	Table    string                   `json:"table"`
	Data     []map[string]interface{} `json:"data"`
	Metadata *TableMetadata           `json:"metadata,omitempty"`
	Analysis *DataAnalysis            `json:"analysis"`
	Count    int                      `json:"count"`
	Limit    int                      `json:"limit"`
}

// QueryPreviewRequest 查询预览请求
type QueryPreviewRequest struct {
	QueryID  string          `json:"query_id,omitempty"`
	QueryDSL *model.QueryDSL `json:"query_dsl" validate:"required"`
	Limit    int             `json:"limit,omitempty"`
}

// QueryPreviewResponse 查询预览响应
type QueryPreviewResponse struct {
	Valid         bool                     `json:"valid"`
	QueryID       string                   `json:"query_id,omitempty"`
	Data          []map[string]interface{} `json:"data,omitempty"`
	Analysis      *DataAnalysis            `json:"analysis,omitempty"`
	Count         int                      `json:"count"`
	ExecutionTime int                      `json:"execution_time"`
	SQL           string                   `json:"sql,omitempty"`
	Plan          *query.QueryPlan         `json:"plan,omitempty"`
	Errors        []string                 `json:"errors,omitempty"`
	Warnings      []string                 `json:"warnings,omitempty"`
}

// DatasetOverviewRequest 数据集概览请求
type DatasetOverviewRequest struct {
	Dataset string `json:"dataset" validate:"required"`
}

// DatasetOverviewResponse 数据集概览响应
type DatasetOverviewResponse struct {
	Dataset           string                 `json:"dataset"`
	Description       string                 `json:"description"`
	Tables            []model.DatasetTable   `json:"tables"`
	PopularTables     []model.DatasetTable   `json:"popular_tables"`
	FieldCategories   []model.FieldCategory  `json:"field_categories"`
	FieldDistribution map[string]int         `json:"field_distribution"`
	Statistics        map[string]interface{} `json:"statistics"`
	LastUpdated       map[string]interface{} `json:"last_updated"`
}

// SampleDataRequest 样本数据请求
type SampleDataRequest struct {
	Dataset string   `json:"dataset" validate:"required"`
	Table   string   `json:"table" validate:"required"`
	Fields  []string `json:"fields,omitempty"`
	Limit   int      `json:"limit,omitempty"`
}

// SampleDataResponse 样本数据响应
type SampleDataResponse struct {
	Dataset   string                   `json:"dataset"`
	Table     string                   `json:"table"`
	Fields    []string                 `json:"fields"`
	Data      []map[string]interface{} `json:"data"`
	FieldInfo []model.DataField        `json:"field_info"`
	Count     int                      `json:"count"`
	Limit     int                      `json:"limit"`
}

// TableMetadata 表元数据
type TableMetadata struct {
	Fields      []model.DataField          `json:"fields"`
	FieldStats  map[string]FieldStatistics `json:"field_stats"`
	RecordCount int                        `json:"record_count"`
}

// FieldStatistics 字段统计
type FieldStatistics struct {
	DataType    string  `json:"data_type"`
	Category    string  `json:"category"`
	Description string  `json:"description"`
	Unit        *string `json:"unit,omitempty"`
	ValueRange  *string `json:"value_range,omitempty"`
	NullCount   int     `json:"null_count"`
	UniqueCount int     `json:"unique_count"`
}

// DataAnalysis 数据分析
type DataAnalysis struct {
	RowCount    int              `json:"row_count"`
	ColumnCount int              `json:"column_count"`
	Columns     []ColumnAnalysis `json:"columns"`
}

// ColumnAnalysis 列分析
type ColumnAnalysis struct {
	Name         string               `json:"name"`
	DataType     string               `json:"data_type"`
	NullCount    int                  `json:"null_count"`
	UniqueCount  int                  `json:"unique_count"`
	UniqueValues map[interface{}]bool `json:"-"` // 内部使用，不序列化
}
