package model

import (
	"encoding/json"
	"time"

	"github.com/google/uuid"
)

// DSL查询结构定义
type QueryDSL struct {
	Dataset string          `json:"dataset" validate:"required"`
	Select  []string        `json:"fields" validate:"required,min=1"`
	From    string          `json:"from"`
	Joins   []JoinClause    `json:"joins,omitempty"`
	Where   *ConditionGroup `json:"conditions,omitempty"`
	GroupBy []string        `json:"groupBy,omitempty"`
	Having  *ConditionGroup `json:"having,omitempty"`
	OrderBy []OrderClause   `json:"orderBy,omitempty"`
	Limit   *int            `json:"limit,omitempty"`
	Offset  *int            `json:"offset,omitempty"`
}

type JoinClause struct {
	Table string `json:"table" validate:"required"`
	On    string `json:"on" validate:"required"`
	Type  string `json:"type,omitempty"` // INNER, LEFT, RIGHT, FULL
}

type ConditionGroup struct {
	Logic      string           `json:"logic" validate:"oneof=AND OR"`
	Conditions []Condition      `json:"conditions"`
	Groups     []ConditionGroup `json:"groups,omitempty"`
}

type Condition struct {
	Field    string      `json:"field" validate:"required"`
	Operator string      `json:"operator" validate:"required,oneof== != > < >= <= LIKE IN NOT_IN IS_NULL IS_NOT_NULL"`
	Value    interface{} `json:"value"`
}

type OrderClause struct {
	Field     string `json:"field" validate:"required"`
	Direction string `json:"direction" validate:"oneof=ASC DESC"`
}

type TimeRange struct {
	Start string `json:"start"`
	End   string `json:"end"`
}

// API请求响应结构
type QueryExecuteRequest struct {
	Dataset    string      `json:"dataset" validate:"required"`
	Fields     []string    `json:"fields" validate:"required,min=1"`
	Conditions []Condition `json:"conditions,omitempty"`
	TimeRange  *TimeRange  `json:"timeRange,omitempty"`
	Limit      *int        `json:"limit,omitempty"`
	Offset     *int        `json:"offset,omitempty"`
}

type QueryExecuteResponse struct {
	QueryID       uuid.UUID                `json:"queryId"`
	Data          []map[string]interface{} `json:"data"`
	Total         int                      `json:"total"`
	ExecutionTime int                      `json:"executionTime"` // milliseconds
	Status        string                   `json:"status"`
}

type QueryStatusResponse struct {
	QueryID  uuid.UUID `json:"queryId"`
	Status   string    `json:"status"`
	Progress *int      `json:"progress,omitempty"`
	Error    *string   `json:"error,omitempty"`
}

type QueryResultsResponse struct {
	Data       []map[string]interface{} `json:"data"`
	Pagination PaginationInfo           `json:"pagination"`
}

type PaginationInfo struct {
	Page       int `json:"page"`
	Limit      int `json:"limit"`
	Total      int `json:"total"`
	TotalPages int `json:"totalPages"`
}

// 查询历史
type QueryHistory struct {
	ID            uuid.UUID       `json:"id" db:"id"`
	UserID        uuid.UUID       `json:"user_id" db:"user_id"`
	Name          *string         `json:"name,omitempty" db:"name"`
	DatasetID     string          `json:"dataset" db:"dataset_id"`
	QueryConfig   json.RawMessage `json:"query_config" db:"query_config"`
	Status        string          `json:"status" db:"status"`
	RecordCount   *int            `json:"record_count,omitempty" db:"record_count"`
	ExecutionTime *int            `json:"execution_time,omitempty" db:"execution_time"`
	CreatedAt     time.Time       `json:"created_at" db:"created_at"`
}

type QueryHistoryResponse struct {
	Queries    []QueryHistoryItem `json:"queries"`
	Pagination PaginationInfo     `json:"pagination"`
}

type QueryHistoryItem struct {
	ID          uuid.UUID `json:"id"`
	Name        *string   `json:"name,omitempty"`
	Dataset     string    `json:"dataset"`
	ExecutedAt  time.Time `json:"executed_at"`
	Status      string    `json:"status"`
	RecordCount *int      `json:"record_count,omitempty"`
}
