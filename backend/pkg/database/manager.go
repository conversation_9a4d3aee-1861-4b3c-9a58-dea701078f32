package database

import (
	"database/sql"
	"fmt"
	"sync"
	"time"

	"github.com/chungzy/medical-data-platform/internal/config"
	"github.com/chungzy/medical-data-platform/pkg/logger"

	_ "github.com/lib/pq"
)

// ConnectionManager 多数据库连接管理器
type ConnectionManager struct {
	mainDB      *sql.DB                    // 主数据库（平台元数据）
	datasources map[string]*sql.DB         // 数据源连接池
	configs     map[string]config.DatasourceConfig // 数据源配置
	mu          sync.RWMutex               // 读写锁
}

// NewConnectionManager 创建连接管理器
func NewConnectionManager(cfg *config.Config) (*ConnectionManager, error) {
	manager := &ConnectionManager{
		datasources: make(map[string]*sql.DB),
		configs:     make(map[string]config.DatasourceConfig),
	}

	// 连接主数据库
	mainDB, err := connectDatabase(cfg.Database.URL, "main")
	if err != nil {
		return nil, fmt.Errorf("failed to connect main database: %w", err)
	}
	manager.mainDB = mainDB

	// 连接启用的数据源
	for name, datasourceConfig := range cfg.Datasources {
		if datasourceConfig.Enabled {
			db, err := connectDatasource(datasourceConfig, name)
			if err != nil {
				logger.Error("Failed to connect datasource", err, map[string]interface{}{
					"datasource": name,
				})
				continue
			}
			manager.datasources[name] = db
			manager.configs[name] = datasourceConfig
			logger.Info("Connected to datasource", map[string]interface{}{
				"datasource": name,
				"host":       datasourceConfig.Host,
				"database":   datasourceConfig.DBName,
			})
		}
	}

	return manager, nil
}

// connectDatabase 连接数据库
func connectDatabase(dsn, name string) (*sql.DB, error) {
	db, err := sql.Open("postgres", dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to open %s database: %w", name, err)
	}

	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping %s database: %w", name, err)
	}

	// 配置连接池
	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(5)
	db.SetConnMaxLifetime(time.Hour)

	return db, nil
}

// connectDatasource 连接数据源
func connectDatasource(cfg config.DatasourceConfig, name string) (*sql.DB, error) {
	db, err := sql.Open("postgres", cfg.URL)
	if err != nil {
		return nil, fmt.Errorf("failed to open datasource %s: %w", name, err)
	}

	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping datasource %s: %w", name, err)
	}

	// 根据配置设置连接池
	maxConns := cfg.MaxConnections
	if maxConns <= 0 {
		maxConns = 10
	}
	db.SetMaxOpenConns(maxConns)
	db.SetMaxIdleConns(maxConns / 2)
	db.SetConnMaxLifetime(time.Hour)

	// 设置连接超时
	if cfg.ConnectionTimeout > 0 {
		db.SetConnMaxLifetime(time.Duration(cfg.ConnectionTimeout) * time.Second)
	}

	return db, nil
}

// GetMainDB 获取主数据库连接
func (cm *ConnectionManager) GetMainDB() *sql.DB {
	return cm.mainDB
}

// GetDatasource 获取数据源连接
func (cm *ConnectionManager) GetDatasource(name string) (*sql.DB, error) {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	db, exists := cm.datasources[name]
	if !exists {
		return nil, fmt.Errorf("datasource %s not found or not enabled", name)
	}

	return db, nil
}

// GetDatasourceConfig 获取数据源配置
func (cm *ConnectionManager) GetDatasourceConfig(name string) (config.DatasourceConfig, error) {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	cfg, exists := cm.configs[name]
	if !exists {
		return config.DatasourceConfig{}, fmt.Errorf("datasource config %s not found", name)
	}

	return cfg, nil
}

// ListEnabledDatasources 列出启用的数据源
func (cm *ConnectionManager) ListEnabledDatasources() []string {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	var names []string
	for name := range cm.datasources {
		names = append(names, name)
	}
	return names
}

// AddDatasource 动态添加数据源
func (cm *ConnectionManager) AddDatasource(name string, cfg config.DatasourceConfig) error {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	// 检查是否已存在
	if _, exists := cm.datasources[name]; exists {
		return fmt.Errorf("datasource %s already exists", name)
	}

	// 连接数据源
	db, err := connectDatasource(cfg, name)
	if err != nil {
		return err
	}

	cm.datasources[name] = db
	cm.configs[name] = cfg

	logger.Info("Added new datasource", map[string]interface{}{
		"datasource": name,
		"host":       cfg.Host,
		"database":   cfg.DBName,
	})

	return nil
}

// RemoveDatasource 移除数据源
func (cm *ConnectionManager) RemoveDatasource(name string) error {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	db, exists := cm.datasources[name]
	if !exists {
		return fmt.Errorf("datasource %s not found", name)
	}

	// 关闭连接
	if err := db.Close(); err != nil {
		logger.Error("Failed to close datasource connection", err, map[string]interface{}{
			"datasource": name,
		})
	}

	delete(cm.datasources, name)
	delete(cm.configs, name)

	logger.Info("Removed datasource", map[string]interface{}{
		"datasource": name,
	})

	return nil
}

// HealthCheck 健康检查
func (cm *ConnectionManager) HealthCheck() map[string]bool {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	status := make(map[string]bool)

	// 检查主数据库
	status["main"] = cm.mainDB.Ping() == nil

	// 检查数据源
	for name, db := range cm.datasources {
		status[name] = db.Ping() == nil
	}

	return status
}

// Close 关闭所有连接
func (cm *ConnectionManager) Close() error {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	var errors []error

	// 关闭主数据库
	if err := cm.mainDB.Close(); err != nil {
		errors = append(errors, fmt.Errorf("failed to close main database: %w", err))
	}

	// 关闭数据源连接
	for name, db := range cm.datasources {
		if err := db.Close(); err != nil {
			errors = append(errors, fmt.Errorf("failed to close datasource %s: %w", name, err))
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("errors closing connections: %v", errors)
	}

	return nil
}
