package service

import (
	"context"
	"fmt"
	"strings"

	"github.com/chungzy/medical-data-platform/internal/model"
	"github.com/chungzy/medical-data-platform/internal/repository"
	"github.com/chungzy/medical-data-platform/pkg/logger"
)

// MedicalFieldService 医学字段服务
type MedicalFieldService struct {
	schemaRepo repository.SchemaRepository
}

// NewMedicalFieldService 创建医学字段服务
func NewMedicalFieldService(schemaRepo repository.SchemaRepository) *MedicalFieldService {
	return &MedicalFieldService{
		schemaRepo: schemaRepo,
	}
}

// GetMedicalFieldCategories 获取医学字段分类
func (mfs *MedicalFieldService) GetMedicalFieldCategories(ctx context.Context, datasetID string) ([]model.MedicalFieldCategory, error) {
	// 获取数据集的所有表
	tables, err := mfs.schemaRepo.GetDatasetTables(ctx, datasetID)
	if err != nil {
		logger.Error("Failed to get dataset tables", err, map[string]interface{}{
			"dataset_id": datasetID,
		})
		return mfs.getDefaultCategories(), nil
	}

	// 构建分类映射
	categoryMap := make(map[string]*model.MedicalFieldCategory)
	
	// 基于MIMIC-IV的医学字段分类体系
	categories := []model.MedicalFieldCategory{
		{
			ID:          "demographics",
			Name:        "人口统计学",
			Description: "患者基本人口学信息和标识符",
			Icon:        "users",
			Fields:      []model.MedicalField{},
		},
		{
			ID:          "temporal",
			Name:        "时间信息",
			Description: "入院、出院、检查等时间相关信息",
			Icon:        "clock",
			Fields:      []model.MedicalField{},
		},
		{
			ID:          "clinical_measurements",
			Name:        "临床测量",
			Description: "生命体征、实验室检查等数值测量",
			Icon:        "activity",
			Fields:      []model.MedicalField{},
		},
		{
			ID:          "diagnoses",
			Name:        "诊断信息",
			Description: "疾病诊断、ICD编码等临床诊断",
			Icon:        "stethoscope",
			Fields:      []model.MedicalField{},
		},
		{
			ID:          "medications",
			Name:        "药物治疗",
			Description: "处方药物、剂量、给药途径等",
			Icon:        "pill",
			Fields:      []model.MedicalField{},
		},
		{
			ID:          "procedures",
			Name:        "医疗程序",
			Description: "手术、操作、医疗程序等",
			Icon:        "scissors",
			Fields:      []model.MedicalField{},
		},
		{
			ID:          "administrative",
			Name:        "管理信息",
			Description: "保险、入院类型、出院去向等管理信息",
			Icon:        "clipboard",
			Fields:      []model.MedicalField{},
		},
	}

	// 初始化分类映射
	for i := range categories {
		categoryMap[categories[i].ID] = &categories[i]
	}

	// 遍历每个表获取字段信息并分类
	for _, table := range tables {
		fields, err := mfs.schemaRepo.GetDatasetFields(ctx, datasetID, table.Name)
		if err != nil {
			logger.Warn("Failed to get fields for table", map[string]interface{}{
				"dataset_id": datasetID,
				"table_name": table.Name,
				"error":      err,
			})
			continue
		}

		// 将字段分配到相应分类
		for _, field := range fields {
			categoryID := mfs.classifyMedicalField(table.Name, field.Name, field.DataType)
			if category, exists := categoryMap[categoryID]; exists {
				medicalField := model.MedicalField{
					ID:          field.ID,
					Name:        mfs.getFieldDisplayName(field.Name),
					NameEn:      field.NameEn,
					Type:        mfs.mapDataTypeToMedical(field.DataType),
					Description: mfs.getFieldDescription(table.Name, field.Name),
					Table:       table.Name,
					Category:    categoryID,
					Examples:    mfs.getFieldExamples(table.Name, field.Name),
					IsRequired:  mfs.isRequiredField(table.Name, field.Name),
				}
				category.Fields = append(category.Fields, medicalField)
			}
		}
	}

	// 过滤掉没有字段的分类
	var result []model.MedicalFieldCategory
	for _, category := range categories {
		if len(category.Fields) > 0 {
			result = append(result, category)
		}
	}

	logger.Info("Generated medical field categories", map[string]interface{}{
		"dataset_id":       datasetID,
		"category_count":   len(result),
		"total_fields":     mfs.countTotalFields(result),
	})

	return result, nil
}

// classifyMedicalField 基于MIMIC-IV结构对字段进行医学分类
func (mfs *MedicalFieldService) classifyMedicalField(tableName, fieldName, dataType string) string {
	tableName = strings.ToLower(tableName)
	fieldName = strings.ToLower(fieldName)

	// 基于表名的分类规则
	if strings.Contains(tableName, "patients") {
		if strings.Contains(fieldName, "subject_id") {
			return "demographics"
		}
		if strings.Contains(fieldName, "gender") || strings.Contains(fieldName, "anchor_age") {
			return "demographics"
		}
		if strings.Contains(fieldName, "dod") || strings.Contains(fieldName, "anchor_year") {
			return "temporal"
		}
	}

	if strings.Contains(tableName, "admissions") {
		if strings.Contains(fieldName, "time") {
			return "temporal"
		}
		if strings.Contains(fieldName, "insurance") || strings.Contains(fieldName, "admission_type") ||
		   strings.Contains(fieldName, "admission_location") || strings.Contains(fieldName, "discharge_location") ||
		   strings.Contains(fieldName, "marital_status") || strings.Contains(fieldName, "language") ||
		   strings.Contains(fieldName, "race") {
			return "administrative"
		}
		if strings.Contains(fieldName, "hadm_id") || strings.Contains(fieldName, "subject_id") {
			return "demographics"
		}
	}

	if strings.Contains(tableName, "labevents") {
		if strings.Contains(fieldName, "time") {
			return "temporal"
		}
		if strings.Contains(fieldName, "value") || strings.Contains(fieldName, "itemid") ||
		   strings.Contains(fieldName, "ref_range") || strings.Contains(fieldName, "flag") {
			return "clinical_measurements"
		}
		if strings.Contains(fieldName, "specimen_id") || strings.Contains(fieldName, "subject_id") ||
		   strings.Contains(fieldName, "hadm_id") {
			return "demographics"
		}
	}

	if strings.Contains(tableName, "diagnoses") {
		if strings.Contains(fieldName, "icd") || strings.Contains(fieldName, "seq_num") {
			return "diagnoses"
		}
	}

	if strings.Contains(tableName, "prescriptions") {
		if strings.Contains(fieldName, "time") {
			return "temporal"
		}
		if strings.Contains(fieldName, "drug") || strings.Contains(fieldName, "dose") ||
		   strings.Contains(fieldName, "route") || strings.Contains(fieldName, "form") ||
		   strings.Contains(fieldName, "gsn") || strings.Contains(fieldName, "ndc") {
			return "medications"
		}
	}

	if strings.Contains(tableName, "procedures") {
		if strings.Contains(fieldName, "icd") || strings.Contains(fieldName, "seq_num") {
			return "procedures"
		}
		if strings.Contains(fieldName, "time") {
			return "temporal"
		}
	}

	if strings.Contains(tableName, "chartevents") || strings.Contains(tableName, "inputevents") ||
	   strings.Contains(tableName, "outputevents") {
		if strings.Contains(fieldName, "time") {
			return "temporal"
		}
		if strings.Contains(fieldName, "value") || strings.Contains(fieldName, "itemid") ||
		   strings.Contains(fieldName, "amount") || strings.Contains(fieldName, "rate") {
			return "clinical_measurements"
		}
	}

	// 基于数据类型的默认分类
	switch strings.ToUpper(dataType) {
	case "TIMESTAMP", "DATE", "TIME":
		return "temporal"
	case "NUMERIC", "INTEGER", "BIGINT", "REAL", "DOUBLE PRECISION":
		return "clinical_measurements"
	default:
		return "administrative"
	}
}

// getFieldDisplayName 获取字段的显示名称
func (mfs *MedicalFieldService) getFieldDisplayName(fieldName string) string {
	displayNames := map[string]string{
		"subject_id":        "患者ID",
		"hadm_id":          "住院ID",
		"gender":           "性别",
		"anchor_age":       "年龄",
		"anchor_year":      "锚定年份",
		"dod":              "死亡日期",
		"admittime":        "入院时间",
		"dischtime":        "出院时间",
		"deathtime":        "死亡时间",
		"admission_type":   "入院类型",
		"admission_location": "入院来源",
		"discharge_location": "出院去向",
		"insurance":        "保险类型",
		"language":         "语言",
		"marital_status":   "婚姻状况",
		"race":             "种族",
		"itemid":           "项目ID",
		"charttime":        "记录时间",
		"storetime":        "存储时间",
		"value":            "测量值",
		"valuenum":         "数值",
		"valueuom":         "单位",
		"ref_range_lower":  "参考下限",
		"ref_range_upper":  "参考上限",
		"flag":             "异常标志",
		"priority":         "优先级",
		"icd_code":         "ICD代码",
		"icd_version":      "ICD版本",
		"seq_num":          "序号",
		"drug":             "药物名称",
		"dose_val_rx":      "剂量值",
		"dose_unit_rx":     "剂量单位",
		"route":            "给药途径",
		"form_rx":          "剂型",
	}

	if displayName, exists := displayNames[strings.ToLower(fieldName)]; exists {
		return displayName
	}
	return fieldName
}

// getFieldDescription 获取字段描述
func (mfs *MedicalFieldService) getFieldDescription(tableName, fieldName string) string {
	key := fmt.Sprintf("%s.%s", strings.ToLower(tableName), strings.ToLower(fieldName))
	
	descriptions := map[string]string{
		"patients.subject_id":        "患者唯一标识符",
		"patients.gender":           "患者性别 (M/F)",
		"patients.anchor_age":       "患者在锚定年份的年龄",
		"patients.dod":              "患者死亡日期",
		"admissions.hadm_id":        "住院唯一标识符",
		"admissions.admittime":      "患者入院时间",
		"admissions.dischtime":      "患者出院时间",
		"admissions.admission_type": "入院类型分类",
		"labevents.itemid":          "检验项目标识符",
		"labevents.value":           "检验结果值",
		"labevents.valuenum":        "数值型检验结果",
		"labevents.valueuom":        "检验结果单位",
		"diagnoses_icd.icd_code":    "ICD诊断代码",
		"prescriptions.drug":        "药物通用名称",
		"prescriptions.dose_val_rx": "处方剂量值",
	}

	if description, exists := descriptions[key]; exists {
		return description
	}
	return fmt.Sprintf("%s表的%s字段", tableName, fieldName)
}

// getFieldExamples 获取字段示例
func (mfs *MedicalFieldService) getFieldExamples(tableName, fieldName string) []string {
	key := fmt.Sprintf("%s.%s", strings.ToLower(tableName), strings.ToLower(fieldName))
	
	examples := map[string][]string{
		"patients.subject_id":        {"10000032", "10000980", "10001217"},
		"patients.gender":           {"M", "F"},
		"patients.anchor_age":       {"65", "72", "45"},
		"admissions.admission_type": {"EMERGENCY", "ELECTIVE", "URGENT"},
		"labevents.value":           {"7.4", "140", "NEGATIVE"},
		"labevents.valueuom":        {"mEq/L", "mg/dL", "mmHg"},
		"diagnoses_icd.icd_code":    {"I50.9", "N18.6", "E11.9"},
		"prescriptions.drug":        {"Metoprolol", "Furosemide", "Insulin"},
	}

	if example, exists := examples[key]; exists {
		return example
	}
	return []string{}
}

// isRequiredField 判断字段是否为必需字段
func (mfs *MedicalFieldService) isRequiredField(tableName, fieldName string) bool {
	requiredFields := map[string][]string{
		"patients":     {"subject_id"},
		"admissions":   {"subject_id", "hadm_id"},
		"labevents":    {"subject_id", "itemid"},
		"diagnoses_icd": {"subject_id", "hadm_id", "icd_code"},
		"prescriptions": {"subject_id", "hadm_id"},
	}

	if fields, exists := requiredFields[strings.ToLower(tableName)]; exists {
		for _, field := range fields {
			if strings.ToLower(fieldName) == field {
				return true
			}
		}
	}
	return false
}

// mapDataTypeToMedical 映射数据类型到医学类型
func (mfs *MedicalFieldService) mapDataTypeToMedical(dataType string) string {
	typeMap := map[string]string{
		"INTEGER":   "number",
		"BIGINT":    "number",
		"NUMERIC":   "number",
		"VARCHAR":   "string",
		"TEXT":      "string",
		"TIMESTAMP": "datetime",
		"DATE":      "date",
		"BOOLEAN":   "boolean",
	}

	if medicalType, exists := typeMap[strings.ToUpper(dataType)]; exists {
		return medicalType
	}
	return "string"
}

// countTotalFields 统计总字段数
func (mfs *MedicalFieldService) countTotalFields(categories []model.MedicalFieldCategory) int {
	total := 0
	for _, category := range categories {
		total += len(category.Fields)
	}
	return total
}

// getDefaultCategories 获取默认分类（降级方案）
func (mfs *MedicalFieldService) getDefaultCategories() []model.MedicalFieldCategory {
	return []model.MedicalFieldCategory{
		{
			ID:          "demographics",
			Name:        "人口统计学",
			Description: "患者基本信息",
			Icon:        "users",
			Fields:      []model.MedicalField{},
		},
		{
			ID:          "clinical_measurements",
			Name:        "临床测量",
			Description: "生命体征和检验结果",
			Icon:        "activity",
			Fields:      []model.MedicalField{},
		},
	}
}
