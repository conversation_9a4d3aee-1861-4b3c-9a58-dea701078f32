"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { History, Search, Play, Download, Trash2, Clock, Database, FileText } from "lucide-react"
import { DashboardLayout } from "@/components/dashboard-layout"
import { formatNumber } from "@/lib/utils"

interface QueryHistory {
  id: string
  name: string
  dataset: string
  executedAt: string
  status: "completed" | "failed" | "running"
  recordCount?: number
  executionTime?: number
  description?: string
}

export default function HistoryPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [datasetFilter, setDatasetFilter] = useState("all")

  const queryHistory: QueryHistory[] = [
    {
      id: "1",
      name: "ICU患者首次入院指标提取",
      dataset: "MIMIC-IV",
      executedAt: "2024-01-15 14:30:25",
      status: "completed",
      recordCount: 1250,
      executionTime: 3.2,
      description: "提取ICU患者首次入院的基本生命体征和实验室指标",
    },
    {
      id: "2",
      name: "机械通气患者死亡率分析",
      dataset: "EICU",
      executedAt: "2024-01-14 09:15:10",
      status: "completed",
      recordCount: 890,
      executionTime: 5.8,
      description: "分析接受机械通气治疗患者的死亡率及相关风险因素",
    },
    {
      id: "3",
      name: "心血管疾病风险因素评估",
      dataset: "NHANES",
      executedAt: "2024-01-13 16:45:33",
      status: "completed",
      recordCount: 2150,
      executionTime: 2.1,
      description: "评估心血管疾病患者的主要风险因素",
    },
    {
      id: "4",
      name: "感染患者抗生素使用模式",
      dataset: "MIMIC-IV",
      executedAt: "2024-01-12 11:20:15",
      status: "failed",
      description: "分析感染患者的抗生素使用模式和治疗效果",
    },
    {
      id: "5",
      name: "儿科重症患者营养评估",
      dataset: "PIC",
      executedAt: "2024-01-11 13:55:42",
      status: "completed",
      recordCount: 420,
      executionTime: 1.8,
      description: "评估儿科重症患者的营养状况和生长发育指标",
    },
    {
      id: "6",
      name: "急性肾损伤预测模型数据",
      dataset: "MIMIC-IV",
      executedAt: "2024-01-10 10:30:18",
      status: "running",
      description: "构建急性肾损伤预测模型的训练数据集",
    },
  ]

  const datasets = [
    { value: "all", label: "全部数据集" },
    { value: "MIMIC-IV", label: "MIMIC-IV" },
    { value: "EICU", label: "EICU" },
    { value: "NHANES", label: "NHANES" },
    { value: "PIC", label: "PIC" },
  ]

  const statusOptions = [
    { value: "all", label: "全部状态" },
    { value: "completed", label: "已完成" },
    { value: "running", label: "运行中" },
    { value: "failed", label: "失败" },
  ]

  const filteredHistory = queryHistory.filter((query) => {
    const matchesSearch =
      searchTerm === "" ||
      query.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (query.description && query.description.toLowerCase().includes(searchTerm.toLowerCase()))

    const matchesStatus = statusFilter === "all" || query.status === statusFilter
    const matchesDataset = datasetFilter === "all" || query.dataset === datasetFilter

    return matchesSearch && matchesStatus && matchesDataset
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800"
      case "running":
        return "bg-blue-100 text-blue-800"
      case "failed":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case "completed":
        return "已完成"
      case "running":
        return "运行中"
      case "failed":
        return "失败"
      default:
        return "未知"
    }
  }

  const rerunQuery = (queryId: string) => {
    console.log("重新运行查询:", queryId)
    // TODO: 实现重新运行查询逻辑
  }

  const downloadResults = (queryId: string) => {
    console.log("下载结果:", queryId)
    // TODO: 实现下载结果逻辑
  }

  const deleteQuery = (queryId: string) => {
    console.log("删除查询:", queryId)
    // TODO: 实现删除查询逻辑
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">查询历史</h1>
            <p className="text-gray-600 mt-1">查看和管理您的历史查询记录</p>
          </div>
        </div>

        {/* Search and Filter */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Search className="h-5 w-5" />
              <span>搜索和筛选</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">搜索查询</label>
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="搜索查询名称或描述..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">状态</label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {statusOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">数据集</label>
                <Select value={datasetFilter} onValueChange={setDatasetFilter}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {datasets.map((dataset) => (
                      <SelectItem key={dataset.value} value={dataset.value}>
                        {dataset.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="pt-4">
              <div className="text-2xl font-bold text-blue-600">{queryHistory.length}</div>
              <p className="text-sm text-gray-600">总查询数</p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-4">
              <div className="text-2xl font-bold text-green-600">
                {queryHistory.filter((q) => q.status === "completed").length}
              </div>
              <p className="text-sm text-gray-600">成功完成</p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-4">
              <div className="text-2xl font-bold text-blue-600">
                {queryHistory.filter((q) => q.status === "running").length}
              </div>
              <p className="text-sm text-gray-600">运行中</p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-4">
              <div className="text-2xl font-bold text-red-600">
                {queryHistory.filter((q) => q.status === "failed").length}
              </div>
              <p className="text-sm text-gray-600">执行失败</p>
            </CardContent>
          </Card>
        </div>

        {/* Query History List */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <History className="h-5 w-5" />
                <span>查询记录</span>
              </div>
              <Badge variant="secondary">{filteredHistory.length} 条记录</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {filteredHistory.map((query) => (
                <Card key={query.id} className="border-l-4 border-l-blue-500">
                  <CardContent className="pt-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="font-semibold text-lg">{query.name}</h3>
                          <Badge variant="secondary">{query.dataset}</Badge>
                          <Badge className={getStatusColor(query.status)}>{getStatusText(query.status)}</Badge>
                        </div>

                        {query.description && <p className="text-sm text-gray-600 mb-3">{query.description}</p>}

                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div className="flex items-center space-x-2">
                            <Clock className="h-4 w-4 text-gray-500" />
                            <span className="text-gray-600">执行时间: {query.executedAt}</span>
                          </div>

                          {query.recordCount && (
                            <div className="flex items-center space-x-2">
                              <Database className="h-4 w-4 text-gray-500" />
                              <span className="text-gray-600">记录数: {formatNumber(query.recordCount)}</span>
                            </div>
                          )}

                          {query.executionTime && (
                            <div className="flex items-center space-x-2">
                              <Clock className="h-4 w-4 text-gray-500" />
                              <span className="text-gray-600">耗时: {query.executionTime}秒</span>
                            </div>
                          )}

                          <div className="flex items-center space-x-2">
                            <FileText className="h-4 w-4 text-gray-500" />
                            <span className="text-gray-600">ID: {query.id}</span>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2 ml-4">
                        {query.status === "completed" && (
                          <>
                            <Button variant="outline" size="sm" onClick={() => rerunQuery(query.id)}>
                              <Play className="h-3 w-3 mr-1" />
                              重新运行
                            </Button>
                            <Button variant="outline" size="sm" onClick={() => downloadResults(query.id)}>
                              <Download className="h-3 w-3 mr-1" />
                              下载
                            </Button>
                          </>
                        )}

                        {query.status === "failed" && (
                          <Button variant="outline" size="sm" onClick={() => rerunQuery(query.id)}>
                            <Play className="h-3 w-3 mr-1" />
                            重试
                          </Button>
                        )}

                        <Button variant="outline" size="sm" onClick={() => deleteQuery(query.id)}>
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}

              {filteredHistory.length === 0 && (
                <div className="text-center py-12">
                  <History className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">暂无查询记录</h3>
                  <p className="text-gray-500 mb-4">您还没有执行过任何查询</p>
                  <Button>
                    <Search className="h-4 w-4 mr-2" />
                    开始查询
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
