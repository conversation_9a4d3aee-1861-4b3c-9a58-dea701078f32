package database

import (
	"database/sql"
	"fmt"

	"github.com/chungzy/medical-data-platform/internal/config"

	_ "github.com/lib/pq"
)

// Connect 连接数据库
func Connect(databaseURL string) (*sql.DB, error) {
	db, err := sql.Open("postgres", databaseURL)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	// 配置连接池
	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(5)

	return db, nil
}

// ConnectWithConfig 使用配置连接数据库
func ConnectWithConfig(cfg *config.Config) (*sql.DB, error) {
	dsn := fmt.Sprintf(
		"host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		cfg.Database.Host,
		cfg.Database.Port,
		cfg.Database.User,
		cfg.Database.Password,
		cfg.Database.DBName,
		cfg.Database.SSLMode,
	)

	return Connect(dsn)
}

// RunMigrations 运行数据库迁移
func RunMigrations(databaseURL string) error {
	// 这里简化处理，实际项目中应该使用专门的迁移工具
	// 如 golang-migrate/migrate

	db, err := Connect(databaseURL)
	if err != nil {
		return err
	}
	defer db.Close()

	// 检查迁移表是否存在
	_, err = db.Exec(`
		CREATE TABLE IF NOT EXISTS schema_migrations (
			version VARCHAR(255) PRIMARY KEY,
			applied_at TIMESTAMP DEFAULT NOW()
		)
	`)
	if err != nil {
		return fmt.Errorf("failed to create migrations table: %w", err)
	}

	// 检查是否已经运行过初始迁移
	var count int
	err = db.QueryRow("SELECT COUNT(*) FROM schema_migrations WHERE version = '001_initial_schema'").Scan(&count)
	if err != nil {
		return fmt.Errorf("failed to check migration status: %w", err)
	}

	if count == 0 {
		// 运行初始迁移
		if err := runInitialMigration(db); err != nil {
			return fmt.Errorf("failed to run initial migration: %w", err)
		}

		// 记录迁移
		_, err = db.Exec("INSERT INTO schema_migrations (version) VALUES ('001_initial_schema')")
		if err != nil {
			return fmt.Errorf("failed to record migration: %w", err)
		}
	}

	return nil
}

// runInitialMigration 运行初始数据库迁移
func runInitialMigration(db *sql.DB) error {
	migrationSQL := `
		-- 用户表
		CREATE TABLE IF NOT EXISTS users (
			id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
			name VARCHAR(100) NOT NULL,
			email VARCHAR(255) UNIQUE NOT NULL,
			phone VARCHAR(20),
			password_hash VARCHAR(255) NOT NULL,
			role VARCHAR(20) NOT NULL CHECK (role IN ('student', 'doctor', 'researcher', 'teacher', 'analyst')),
			created_at TIMESTAMP DEFAULT NOW(),
			updated_at TIMESTAMP DEFAULT NOW()
		);

		-- 数据集表
		CREATE TABLE IF NOT EXISTS datasets (
			id VARCHAR(50) PRIMARY KEY,
			name VARCHAR(100) NOT NULL,
			description TEXT,
			version VARCHAR(20),
			patient_count INTEGER DEFAULT 0,
			status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'maintenance', 'inactive')),
			last_updated TIMESTAMP DEFAULT NOW()
		);

		-- 数据字段表
		CREATE TABLE IF NOT EXISTS data_fields (
			id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
			dataset_id VARCHAR(50) REFERENCES datasets(id) ON DELETE CASCADE,
			name VARCHAR(100) NOT NULL,
			name_en VARCHAR(100),
			code VARCHAR(50),
			data_type VARCHAR(20),
			category VARCHAR(50),
			description TEXT,
			unit VARCHAR(20),
			value_range VARCHAR(100),
			examples JSONB
		);

		-- 查询历史表
		CREATE TABLE IF NOT EXISTS query_history (
			id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
			user_id UUID REFERENCES users(id) ON DELETE CASCADE,
			name VARCHAR(200),
			dataset_id VARCHAR(50) REFERENCES datasets(id),
			query_config JSONB NOT NULL,
			status VARCHAR(20) DEFAULT 'completed' CHECK (status IN ('completed', 'running', 'failed')),
			record_count INTEGER,
			execution_time INTEGER,
			created_at TIMESTAMP DEFAULT NOW()
		);

		-- 查询模板表
		CREATE TABLE IF NOT EXISTS query_templates (
			id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
			user_id UUID REFERENCES users(id) ON DELETE CASCADE,
			name VARCHAR(200) NOT NULL,
			description TEXT,
			category VARCHAR(50),
			dataset_id VARCHAR(50) REFERENCES datasets(id),
			template_config JSONB NOT NULL,
			is_public BOOLEAN DEFAULT false,
			usage_count INTEGER DEFAULT 0,
			rating DECIMAL(2,1) DEFAULT 0,
			author VARCHAR(100),
			created_at TIMESTAMP DEFAULT NOW(),
			updated_at TIMESTAMP DEFAULT NOW()
		);

		-- 创建索引
		CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
		CREATE INDEX IF NOT EXISTS idx_data_fields_dataset_id ON data_fields(dataset_id);
		CREATE INDEX IF NOT EXISTS idx_data_fields_category ON data_fields(category);
		CREATE INDEX IF NOT EXISTS idx_query_history_user_id ON query_history(user_id);
		CREATE INDEX IF NOT EXISTS idx_query_history_dataset_id ON query_history(dataset_id);
		CREATE INDEX IF NOT EXISTS idx_query_history_created_at ON query_history(created_at DESC);
		CREATE INDEX IF NOT EXISTS idx_query_templates_user_id ON query_templates(user_id);
		CREATE INDEX IF NOT EXISTS idx_query_templates_dataset_id ON query_templates(dataset_id);
		CREATE INDEX IF NOT EXISTS idx_query_templates_category ON query_templates(category);
		CREATE INDEX IF NOT EXISTS idx_query_templates_is_public ON query_templates(is_public);
	`

	_, err := db.Exec(migrationSQL)
	if err != nil {
		return err
	}

	// 插入初始数据
	return insertInitialData(db)
}

// insertInitialData 插入初始数据
func insertInitialData(db *sql.DB) error {
	// 插入数据集
	_, err := db.Exec(`
		INSERT INTO datasets (id, name, description, version, patient_count, status) VALUES
		('mimic_iv', 'MIMIC-IV', 'Medical Information Mart for Intensive Care IV', '2.2', 382278, 'active'),
		('eicu', 'eICU Collaborative Research Database', 'Multi-center ICU database', '2.0', 139367, 'active'),
		('nhanes', 'National Health and Nutrition Examination Survey', 'US health and nutrition survey data', '2017-2020', 15560, 'active'),
		('pic', 'Pediatric Intensive Care', 'Pediatric critical care database', '1.0', 12881, 'active')
		ON CONFLICT (id) DO NOTHING
	`)
	if err != nil {
		return fmt.Errorf("failed to insert datasets: %w", err)
	}

	// 插入示例数据字段
	_, err = db.Exec(`
		INSERT INTO data_fields (dataset_id, name, name_en, code, data_type, category, description, unit, examples) VALUES
		('mimic_iv', '患者ID', 'Patient ID', 'subject_id', 'integer', 'identifier', '患者唯一标识符', NULL, '["10000032", "10000980", "10001217"]'),
		('mimic_iv', '年龄', 'Age', 'anchor_age', 'integer', 'demographics', '患者年龄', 'years', '["65", "76", "89"]'),
		('mimic_iv', '性别', 'Gender', 'gender', 'string', 'demographics', '患者性别', NULL, '["M", "F"]'),
		('mimic_iv', '入院时间', 'Admission Time', 'admittime', 'timestamp', 'temporal', '入院时间', NULL, '["2180-07-23 16:15:00", "2180-07-23 16:00:00"]'),
		('mimic_iv', '出院时间', 'Discharge Time', 'dischtime', 'timestamp', 'temporal', '出院时间', NULL, '["2180-07-30 20:00:00", "2180-07-25 14:00:00"]')
		ON CONFLICT DO NOTHING
	`)
	if err != nil {
		return fmt.Errorf("failed to insert data fields: %w", err)
	}

	return nil
}
