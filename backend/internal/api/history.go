package api

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"

	"github.com/chungzy/medical-data-platform/internal/model"
	"github.com/chungzy/medical-data-platform/internal/service"
	"github.com/chungzy/medical-data-platform/pkg/logger"
)

type HistoryController struct {
	historyService *service.HistoryService
}

func NewHistoryController(historyService *service.HistoryService) *HistoryController {
	return &HistoryController{
		historyService: historyService,
	}
}

// GetQueryHistory 获取查询历史
func (hc *HistoryController) GetQueryHistory(c *gin.Context) {
	c.Set("business_operation", "get_query_history")

	logger.InfoWithGinContext(c, "Getting query history")

	userID, exists := c.Get("user_id")
	if !exists {
		logger.WarnWithGinContext(c, "User ID not found in context")
		c.<PERSON>(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// 获取分页参数
	limitStr := c.<PERSON>fault<PERSON>("limit", "20")
	offsetStr := c.DefaultQuery("offset", "0")

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 20
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		offset = 0
	}

	logger.InfoWithGinContext(c, "Pagination parameters parsed", map[string]interface{}{
		"user_id": userID,
		"limit":   limit,
		"offset":  offset,
	})

	histories, total, err := hc.historyService.GetUserQueryHistory(c.Request.Context(), userID.(uuid.UUID), limit, offset)
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to get query history", map[string]interface{}{
			"user_id": userID,
			"limit":   limit,
			"offset":  offset,
		})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get query history", "details": err.Error()})
		return
	}

	logger.InfoWithGinContext(c, "Query history retrieved successfully", map[string]interface{}{
		"user_id":       userID,
		"total_records": total,
		"limit":         limit,
		"offset":        offset,
	})

	c.JSON(http.StatusOK, gin.H{
		"histories": histories,
		"total":     total,
		"limit":     limit,
		"offset":    offset,
	})
}

// SaveAsTemplate 将查询历史保存为模板
func (hc *HistoryController) SaveAsTemplate(c *gin.Context) {
	c.Set("business_operation", "save_as_template")

	logger.InfoWithGinContext(c, "Saving query as template")

	userID, exists := c.Get("user_id")
	if !exists {
		logger.WarnWithGinContext(c, "User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	queryID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Invalid query ID format", map[string]interface{}{
			"query_id_param": c.Param("id"),
		})
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query ID"})
		return
	}

	var req model.SaveAsTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to parse save template request")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format", "details": err.Error()})
		return
	}

	logger.InfoWithGinContext(c, "Save template request parsed", map[string]interface{}{
		"query_id":      queryID,
		"template_name": req.Name,
		"user_id":       userID,
	})

	template, err := hc.historyService.SaveAsTemplate(c.Request.Context(), queryID, userID.(uuid.UUID), &req)
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to save as template", map[string]interface{}{
			"query_id":      queryID,
			"template_name": req.Name,
			"user_id":       userID,
		})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save as template", "details": err.Error()})
		return
	}

	logger.InfoWithGinContext(c, "Template saved successfully", map[string]interface{}{
		"query_id":      queryID,
		"template_id":   template.ID,
		"template_name": template.Name,
		"user_id":       userID,
	})

	c.JSON(http.StatusCreated, template)
}

// DeleteHistoryItem 删除历史记录
func (hc *HistoryController) DeleteHistoryItem(c *gin.Context) {
	c.Set("business_operation", "delete_history_item")

	logger.InfoWithGinContext(c, "Deleting history item")

	userID, exists := c.Get("user_id")
	if !exists {
		logger.WarnWithGinContext(c, "User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	queryID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Invalid query ID format", map[string]interface{}{
			"query_id_param": c.Param("id"),
		})
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query ID"})
		return
	}

	logger.InfoWithGinContext(c, "Deleting history item", map[string]interface{}{
		"query_id": queryID,
		"user_id":  userID,
	})

	if err := hc.historyService.DeleteHistoryItem(c.Request.Context(), queryID, userID.(uuid.UUID)); err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to delete history item", map[string]interface{}{
			"query_id": queryID,
			"user_id":  userID,
		})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete history item", "details": err.Error()})
		return
	}

	logger.InfoWithGinContext(c, "History item deleted successfully", map[string]interface{}{
		"query_id": queryID,
		"user_id":  userID,
	})

	c.JSON(http.StatusOK, gin.H{"message": "History item deleted successfully"})
}
