"use client"

import React, { use<PERSON><PERSON>, use<PERSON>ffe<PERSON> } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Checkbox } from "@/components/ui/checkbox"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Progress } from "@/components/ui/progress"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { 
  Search, 
  Users, 
  Database, 
  FileSpreadsheet, 
  TestTube,
  Heart,
  Activity,
  Pill,
  Plus, 
  Trash2, 
  Download,
  Play,
  Eye,
  Info,
  CheckCircle,
  AlertCircle,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>
} from "lucide-react"
import { toast } from "sonner"

// MIMIC数据分析工作流接口
interface MimicIndicator {
  id: string
  name: string
  nameEn: string
  code: string
  category: string
  description: string
  patientCount: number
  unit?: string
  table: string
}

interface CohortCondition {
  id: string
  category: string
  field: string
  operator: string
  value: string | number
  description: string
}

interface IndicatorExtraction {
  category: string
  type: string
  indicators: string[]
  timeRange: string
  aggregation: string
}

interface MimicQueryConfig {
  studyName: string
  cohortConditions: CohortCondition[]
  indicatorExtractions: IndicatorExtraction[]
  outputFormat: string
}

export default function MimicQueryBuilder() {
  const [activeTab, setActiveTab] = useState("indicator-search")
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [queryConfig, setQueryConfig] = useState<MimicQueryConfig>({
    studyName: "",
    cohortConditions: [],
    indicatorExtractions: [],
    outputFormat: "csv"
  })
  const [estimatedPatients, setEstimatedPatients] = useState<number | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  // 模拟MIMIC指标数据
  const mockIndicators: MimicIndicator[] = [
    {
      id: "lab_glucose",
      name: "血糖",
      nameEn: "Glucose",
      code: "50809",
      category: "laboratory",
      description: "血清葡萄糖浓度",
      patientCount: 45678,
      unit: "mg/dL",
      table: "labevents"
    },
    {
      id: "vital_hr",
      name: "心率",
      nameEn: "Heart Rate",
      code: "220045",
      category: "vitals",
      description: "每分钟心跳次数",
      patientCount: 78234,
      unit: "bpm",
      table: "chartevents"
    },
    {
      id: "lab_creatinine",
      name: "肌酐",
      nameEn: "Creatinine",
      code: "50912",
      category: "laboratory",
      description: "血清肌酐浓度",
      patientCount: 42156,
      unit: "mg/dL",
      table: "labevents"
    },
    {
      id: "vital_bp_sys",
      name: "收缩压",
      nameEn: "Systolic BP",
      code: "220050",
      category: "vitals",
      description: "收缩期血压",
      patientCount: 76543,
      unit: "mmHg",
      table: "chartevents"
    }
  ]

  const categories = [
    { id: "all", name: "全部指标", icon: Database },
    { id: "laboratory", name: "实验室指标", icon: TestTube },
    { id: "vitals", name: "生命体征", icon: Heart },
    { id: "medications", name: "药物治疗", icon: Pill },
    { id: "procedures", name: "操作手术", icon: Activity }
  ]

  // 过滤指标
  const filteredIndicators = mockIndicators.filter(indicator => {
    const matchesSearch = searchTerm === "" || 
      indicator.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      indicator.nameEn.toLowerCase().includes(searchTerm.toLowerCase()) ||
      indicator.code.includes(searchTerm)
    
    const matchesCategory = selectedCategory === "all" || indicator.category === selectedCategory
    
    return matchesSearch && matchesCategory
  })

  // 估算患者数量
  const estimatePatientCount = async () => {
    setIsLoading(true)
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      const estimated = Math.floor(Math.random() * 50000) + 10000
      setEstimatedPatients(estimated)
      toast.success(`估算完成：约 ${estimated.toLocaleString()} 名患者`)
    } catch (error) {
      toast.error("估算失败")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* 研究配置 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span>MIMIC数据分析配置</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="study-name">研究名称</Label>
              <Input
                id="study-name"
                placeholder="输入研究名称"
                value={queryConfig.studyName}
                onChange={(e) => setQueryConfig({
                  ...queryConfig,
                  studyName: e.target.value
                })}
              />
            </div>
            <div>
              <Label htmlFor="output-format">输出格式</Label>
              <Select
                value={queryConfig.outputFormat}
                onValueChange={(value) => setQueryConfig({
                  ...queryConfig,
                  outputFormat: value
                })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="csv">CSV文件</SelectItem>
                  <SelectItem value="excel">Excel文件</SelectItem>
                  <SelectItem value="json">JSON文件</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 功能选择 */}
      <Card>
        <CardHeader>
          <CardTitle>选择分析功能</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card
              className={`cursor-pointer transition-all ${activeTab === "indicator-search" ? "ring-2 ring-blue-500" : "hover:shadow-md"}`}
              onClick={() => setActiveTab("indicator-search")}
            >
              <CardContent className="p-4 text-center">
                <Search className="h-8 w-8 mx-auto mb-2 text-blue-600" />
                <h3 className="font-medium">指标查询</h3>
                <p className="text-sm text-gray-500">搜索MIMIC指标</p>
              </CardContent>
            </Card>

            <Card
              className={`cursor-pointer transition-all ${activeTab === "cohort-definition" ? "ring-2 ring-blue-500" : "hover:shadow-md"}`}
              onClick={() => setActiveTab("cohort-definition")}
            >
              <CardContent className="p-4 text-center">
                <Filter className="h-8 w-8 mx-auto mb-2 text-green-600" />
                <h3 className="font-medium">纳排条件</h3>
                <p className="text-sm text-gray-500">定义患者队列</p>
              </CardContent>
            </Card>

            <Card
              className={`cursor-pointer transition-all ${activeTab === "indicator-extraction" ? "ring-2 ring-blue-500" : "hover:shadow-md"}`}
              onClick={() => setActiveTab("indicator-extraction")}
            >
              <CardContent className="p-4 text-center">
                <Database className="h-8 w-8 mx-auto mb-2 text-purple-600" />
                <h3 className="font-medium">指标提取</h3>
                <p className="text-sm text-gray-500">提取医学数据</p>
              </CardContent>
            </Card>

            <Card
              className={`cursor-pointer transition-all ${activeTab === "data-summary" ? "ring-2 ring-blue-500" : "hover:shadow-md"}`}
              onClick={() => setActiveTab("data-summary")}
            >
              <CardContent className="p-4 text-center">
                <FileSpreadsheet className="h-8 w-8 mx-auto mb-2 text-orange-600" />
                <h3 className="font-medium">数据汇总</h3>
                <p className="text-sm text-gray-500">合并数据文件</p>
              </CardContent>
            </Card>

            <Card
              className={`cursor-pointer transition-all ${activeTab === "patient-count" ? "ring-2 ring-blue-500" : "hover:shadow-md"}`}
              onClick={() => setActiveTab("patient-count")}
            >
              <CardContent className="p-4 text-center">
                <Users className="h-8 w-8 mx-auto mb-2 text-red-600" />
                <h3 className="font-medium">病人数查询</h3>
                <p className="text-sm text-gray-500">统计患者数量</p>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>

      {/* 功能内容区域 */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>

        {/* 指标查询 */}
        <TabsContent value="indicator-search" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>指标搜索</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex space-x-4">
                <Input
                  placeholder="搜索指标名称、编码..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="flex-1"
                />
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className="w-48">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map(category => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <ScrollArea className="h-96">
                <div className="space-y-2">
                  {filteredIndicators.map(indicator => (
                    <div key={indicator.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <span className="font-medium">{indicator.name}</span>
                          <Badge variant="outline" className="text-xs">{indicator.code}</Badge>
                        </div>
                        <p className="text-sm text-gray-500">{indicator.nameEn}</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-gray-500">{indicator.patientCount.toLocaleString()} 患者</p>
                        {indicator.unit && <Badge variant="secondary" className="text-xs">{indicator.unit}</Badge>}
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 纳排条件设置 */}
        <TabsContent value="cohort-definition" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>患者队列条件</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label>年龄范围</Label>
                  <div className="flex space-x-2">
                    <Input type="number" placeholder="最小" />
                    <Input type="number" placeholder="最大" />
                  </div>
                </div>
                <div>
                  <Label>性别</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="选择性别" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">不限</SelectItem>
                      <SelectItem value="M">男性</SelectItem>
                      <SelectItem value="F">女性</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>ICD编码</Label>
                  <Input placeholder="疾病编码" />
                </div>
              </div>

              <div className="flex items-center justify-between">
                <Button variant="outline" onClick={estimatePatientCount} disabled={isLoading}>
                  {isLoading ? "估算中..." : "估算患者数"}
                </Button>
                {estimatedPatients && (
                  <span className="text-sm text-gray-600">
                    预估患者数：<strong>{estimatedPatients.toLocaleString()}</strong>
                  </span>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 指标提取 */}
        <TabsContent value="indicator-extraction" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>指标提取</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label>数据类型</Label>
                  <Select defaultValue="laboratory">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="laboratory">实验室指标</SelectItem>
                      <SelectItem value="vitals">生命体征</SelectItem>
                      <SelectItem value="medications">药物记录</SelectItem>
                      <SelectItem value="diagnoses">诊断编码</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>时间范围</Label>
                  <Select defaultValue="icu_24h">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="icu_first">入ICU首次</SelectItem>
                      <SelectItem value="icu_24h">入ICU24小时</SelectItem>
                      <SelectItem value="icu_all">ICU期间全部</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label>指标编码</Label>
                <Input placeholder="输入MIMIC指标编码，用逗号分隔" />
              </div>

              <div className="flex justify-end space-x-2">
                <Button variant="outline">预览</Button>
                <Button>开始提取</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 数据汇总 */}
        <TabsContent value="data-summary" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>数据汇总</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <FileSpreadsheet className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                <p className="text-gray-600 mb-4">上传CSV文件进行合并</p>
                <Button variant="outline">选择文件</Button>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center space-x-2">
                  <Checkbox id="remove-duplicates" defaultChecked />
                  <Label htmlFor="remove-duplicates">移除重复</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="sort-by-id" defaultChecked />
                  <Label htmlFor="sort-by-id">按ID排序</Label>
                </div>
              </div>

              <div className="flex justify-end">
                <Button>
                  <Download className="h-4 w-4 mr-2" />
                  下载汇总结果
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="patient-count">
          <Card>
            <CardHeader>
              <CardTitle>患者数量查询</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center">
                <Button onClick={estimatePatientCount} disabled={isLoading} size="lg">
                  {isLoading ? "查询中..." : "查询患者数量"}
                </Button>
                {isLoading && <Progress value={33} className="w-full mt-4" />}
                {estimatedPatients && (
                  <div className="mt-4 p-4 bg-green-50 rounded-lg">
                    <p className="text-lg font-medium text-green-800">
                      符合条件的患者数：{estimatedPatients.toLocaleString()}
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
