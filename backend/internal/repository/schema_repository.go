package repository

import (
	"context"
	"fmt"
	"strings"

	"github.com/chungzy/medical-data-platform/internal/model"
	"github.com/chungzy/medical-data-platform/pkg/database"
	"github.com/chungzy/medical-data-platform/pkg/logger"
)

// SchemaRepository schema信息查询接口
type SchemaRepository interface {
	GetDatasetTables(ctx context.Context, datasetID string) ([]model.DatasetTable, error)
	GetDatasetFields(ctx context.Context, datasetID string, tableName string) ([]model.DataField, error)
	GetDatasetSchemas(ctx context.Context, datasetID string) ([]string, error)
	ValidateDataset(ctx context.Context, datasetID string) error
}

type schemaRepository struct {
	schemaManager *database.SchemaManager
}

// NewSchemaRepository 创建schema repository
func NewSchemaRepository(schemaManager *database.SchemaManager) SchemaRepository {
	return &schemaRepository{
		schemaManager: schemaManager,
	}
}

// GetDatasetTables 获取数据集的表信息
func (r *schemaRepository) GetDatasetTables(ctx context.Context, datasetID string) ([]model.DatasetTable, error) {
	// 获取数据集对应的schemas
	schemas, err := r.getDatasetSchemaNames(datasetID)
	if err != nil {
		return nil, fmt.Errorf("failed to get schemas for dataset %s: %w", datasetID, err)
	}

	var allTables []model.DatasetTable

	// 遍历每个schema获取表信息
	for _, schemaName := range schemas {
		schemaInfo, err := r.schemaManager.GetSchemaInfo(ctx, datasetID, schemaName)
		if err != nil {
			logger.Warn("Failed to get schema info", map[string]interface{}{
				"dataset_id":  datasetID,
				"schema_name": schemaName,
				"error":       err,
			})
			continue
		}

		// 转换为DatasetTable格式
		for _, table := range schemaInfo.Tables {
			datasetTable := model.DatasetTable{
				Name:        table.TableName,
				Description: table.TableComment,
				RecordCount: int(table.RecordCount),
			}
			allTables = append(allTables, datasetTable)
		}
	}

	logger.Info("Retrieved dataset tables", map[string]interface{}{
		"dataset_id":  datasetID,
		"table_count": len(allTables),
	})

	return allTables, nil
}

// GetDatasetFields 获取数据集表的字段信息
func (r *schemaRepository) GetDatasetFields(ctx context.Context, datasetID string, tableName string) ([]model.DataField, error) {
	// 获取数据集对应的schemas
	schemas, err := r.getDatasetSchemaNames(datasetID)
	if err != nil {
		return nil, fmt.Errorf("failed to get schemas for dataset %s: %w", datasetID, err)
	}

	// 在所有schema中查找表
	for _, schemaName := range schemas {
		columns, err := r.schemaManager.GetTableColumns(ctx, datasetID, schemaName, tableName)
		if err != nil {
			logger.Warn("Failed to get table columns", map[string]interface{}{
				"dataset_id":  datasetID,
				"schema_name": schemaName,
				"table_name":  tableName,
				"error":       err,
			})
			continue
		}

		if len(columns) == 0 {
			continue // 表不在这个schema中
		}

		// 转换为DataField格式
		var fields []model.DataField
		for _, col := range columns {
			field := model.DataField{
				ID:          fmt.Sprintf("%s.%s.%s", schemaName, tableName, col.ColumnName),
				Name:        col.ColumnName,
				NameEn:      col.ColumnName,
				DataType:    r.mapPostgreSQLType(col.DataType),
				Description: col.ColumnComment,
				Category:    r.categorizeField(col.ColumnName, col.DataType),
				Examples:    nil, // 可以后续从实际数据中获取
			}
			fields = append(fields, field)
		}

		logger.Info("Retrieved table fields", map[string]interface{}{
			"dataset_id":  datasetID,
			"schema_name": schemaName,
			"table_name":  tableName,
			"field_count": len(fields),
		})

		return fields, nil
	}

	return nil, fmt.Errorf("table %s not found in dataset %s", tableName, datasetID)
}

// GetDatasetSchemas 获取数据集的schema列表
func (r *schemaRepository) GetDatasetSchemas(ctx context.Context, datasetID string) ([]string, error) {
	return r.schemaManager.GetMIMICSchemas(ctx, datasetID)
}

// ValidateDataset 验证数据集连接
func (r *schemaRepository) ValidateDataset(ctx context.Context, datasetID string) error {
	return r.schemaManager.ValidateConnection(ctx, datasetID)
}

// getDatasetSchemaNames 获取数据集对应的schema名称列表
func (r *schemaRepository) getDatasetSchemaNames(datasetID string) ([]string, error) {
	// 根据数据集ID映射到对应的schemas
	schemaMap := map[string][]string{
		"mimic-iv": {"mimiciv_hosp", "mimiciv_icu", "mimiciv_derived"},
		"mimic_iv": {"mimiciv_hosp", "mimiciv_icu", "mimiciv_derived"},
		"eicu":     {"eicu_crd"},
		"nhanes":   {"nhanes"},
		"pic":      {"pic"},
	}

	if schemas, exists := schemaMap[datasetID]; exists {
		return schemas, nil
	}

	// 默认返回所有MIMIC schemas
	return []string{"mimiciv_hosp", "mimiciv_icu", "mimiciv_derived"}, nil
}

// mapPostgreSQLType 映射PostgreSQL数据类型到标准类型
func (r *schemaRepository) mapPostgreSQLType(pgType string) string {
	typeMap := map[string]string{
		"bigint":                      "INTEGER",
		"integer":                     "INTEGER",
		"smallint":                    "INTEGER",
		"numeric":                     "NUMERIC",
		"double precision":            "NUMERIC",
		"real":                        "NUMERIC",
		"character varying":           "VARCHAR",
		"varchar":                     "VARCHAR",
		"text":                        "TEXT",
		"timestamp without time zone": "TIMESTAMP",
		"timestamp with time zone":    "TIMESTAMP",
		"date":                        "DATE",
		"time":                        "TIME",
		"boolean":                     "BOOLEAN",
		"json":                        "JSON",
		"jsonb":                       "JSON",
	}

	if mapped, exists := typeMap[pgType]; exists {
		return mapped
	}
	return "VARCHAR" // 默认类型
}

// categorizeField 根据字段名和类型进行分类
func (r *schemaRepository) categorizeField(fieldName string, dataType string) string {
	fieldName = strings.ToLower(fieldName)

	// 基于字段名的分类规则
	if strings.Contains(fieldName, "subject_id") || strings.Contains(fieldName, "gender") ||
		strings.Contains(fieldName, "age") || strings.Contains(fieldName, "anchor") {
		return "人口统计学"
	}

	if strings.Contains(fieldName, "hadm_id") || strings.Contains(fieldName, "admittime") ||
		strings.Contains(fieldName, "dischtime") || strings.Contains(fieldName, "admission") {
		return "时间"
	}

	if strings.Contains(fieldName, "itemid") || strings.Contains(fieldName, "value") ||
		strings.Contains(fieldName, "charttime") {
		return "数值"
	}

	if strings.Contains(fieldName, "icd") || strings.Contains(fieldName, "diagnosis") ||
		strings.Contains(fieldName, "procedure") {
		return "临床"
	}

	if strings.Contains(fieldName, "drug") || strings.Contains(fieldName, "dose") ||
		strings.Contains(fieldName, "medication") {
		return "临床"
	}

	// 基于数据类型的默认分类
	switch dataType {
	case "TIMESTAMP", "DATE", "TIME":
		return "时间"
	case "NUMERIC", "INTEGER":
		return "数值"
	case "BOOLEAN":
		return "标志"
	default:
		return "分类"
	}
}
