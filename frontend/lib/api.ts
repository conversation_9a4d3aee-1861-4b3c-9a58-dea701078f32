// API 客户端配置和通用请求函数
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8088/api"

interface ApiResponse<T> {
  data?: T
  error?: string
  message?: string
}

class ApiClient {
  private baseURL: string
  private token: string | null = null

  constructor(baseURL: string) {
    this.baseURL = baseURL
    // 从localStorage获取token
    if (typeof window !== "undefined") {
      this.token = localStorage.getItem("auth_token")
    }
  }

  setToken(token: string) {
    this.token = token
    if (typeof window !== "undefined") {
      localStorage.setItem("auth_token", token)
    }
  }

  clearToken() {
    this.token = null
    if (typeof window !== "undefined") {
      localStorage.removeItem("auth_token")
    }
  }

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
      ...(options.headers as Record<string, string> || {}),
    }

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      })

      const data = await response.json()

      if (!response.ok) {
        return { error: data.message || "Request failed" }
      }

      return { data }
    } catch (error) {
      return { error: "Network error" }
    }
  }

  // 认证相关
  async register(userData: {
    name: string
    email: string
    phone?: string
    password: string
    role: string
  }) {
    return this.request<{ token: string; user: any }>("/auth/register", {
      method: "POST",
      body: JSON.stringify(userData),
    })
  }

  async login(credentials: { email: string; password: string }) {
    return this.request<{ token: string; user: any }>("/auth/login", {
      method: "POST",
      body: JSON.stringify(credentials),
    })
  }

  async getProfile() {
    // 临时解决方案：如果没有token，返回默认用户
    if (!this.token) {
      return {
        data: {
          id: "temp-user",
          name: "临时用户",
          email: "<EMAIL>",
          role: "researcher"
        }
      }
    }
    return this.request<any>("/auth/me")
  }

  // 数据集相关
  async getDatasets() {
    return this.request<any[]>("/datasets")
  }

  // 医学查询相关
  async getFieldCategories(datasetId?: string) {
    const params = datasetId ? `?dataset=${datasetId}` : ""
    return this.request<any>(`/medical-query/categories${params}`)
  }

  async getTableFields(datasetId: string, tableName: string) {
    return this.request<any>(`/medical-query/datasets/${datasetId}/tables/${tableName}/fields`)
  }

  async executeMedicalQuery(queryDSL: any) {
    return this.request<any>("/medical-query/execute", {
      method: "POST",
      body: JSON.stringify(queryDSL)
    })
  }

  async saveMedicalQuery(queryDSL: any) {
    return this.request<any>("/medical-query/save", {
      method: "POST",
      body: JSON.stringify(queryDSL)
    })
  }

  async getMedicalQueryHistory(limit = 10, offset = 0) {
    return this.request<any>(`/medical-query/history?limit=${limit}&offset=${offset}`)
  }

  async getMedicalQueryResult(queryId: string) {
    return this.request<any>(`/medical-query/results/${queryId}`)
  }

  async getDataset(id: string) {
    return this.request<any>(`/datasets/${id}`)
  }

  // 数据字典相关
  async searchFields(params: {
    q?: string
    dataset?: string
    category?: string
  }) {
    const searchParams = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value) searchParams.append(key, value)
    })

    return this.request<{ fields: any[]; total: number }>(`/dictionary/search?${searchParams.toString()}`)
  }

  async getCategories() {
    return this.request<{ categories: any[] }>("/dictionary/categories")
  }

  // 查询相关
  async executeQuery(queryConfig: any) {
    return this.request<any>("/queries/execute", {
      method: "POST",
      body: JSON.stringify(queryConfig),
    })
  }

  async getQueryStatus(queryId: string) {
    return this.request<any>(`/queries/${queryId}/status`)
  }

  async getQueryResults(queryId: string, page = 1, limit = 50) {
    return this.request<any>(`/queries/${queryId}/results?page=${page}&limit=${limit}`)
  }

  // 模板相关
  async getTemplates(params?: { category?: string; dataset?: string }) {
    const searchParams = new URLSearchParams()
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value) searchParams.append(key, value)
      })
    }

    return this.request<{ templates: any[] }>(
      `/templates${searchParams.toString() ? "?" + searchParams.toString() : ""}`,
    )
  }

  async createTemplate(template: any) {
    return this.request<any>("/templates", {
      method: "POST",
      body: JSON.stringify(template),
    })
  }

  async useTemplate(templateId: string) {
    return this.request<any>(`/templates/${templateId}/use`, {
      method: "POST",
    })
  }

  // 历史记录
  async getQueryHistory(page = 1, limit = 20) {
    return this.request<any>(`/history/queries?page=${page}&limit=${limit}`)
  }

  // 导出
  async exportData(params: {
    queryId: string
    format: "csv" | "excel" | "json"
    fields?: string[]
  }) {
    return this.request<{ downloadUrl: string; expiresAt: string }>("/export", {
      method: "POST",
      body: JSON.stringify(params),
    })
  }

  // 统计分析
  async getBasicStats(params: { queryId: string; fields: string[] }) {
    return this.request<any>("/analytics/basic-stats", {
      method: "POST",
      body: JSON.stringify(params),
    })
  }

  async getVisualizationData(params: {
    queryId: string
    chartType: string
    xField: string
    yField?: string
    groupBy?: string
  }) {
    return this.request<any>("/analytics/visualization", {
      method: "POST",
      body: JSON.stringify(params),
    })
  }

  // 医学查询相关方法
  async get(endpoint: string) {
    return this.request<any>(endpoint)
  }

  async post(endpoint: string, data?: any) {
    return this.request<any>(endpoint, {
      method: "POST",
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async put(endpoint: string, data?: any) {
    return this.request<any>(endpoint, {
      method: "PUT",
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async delete(endpoint: string) {
    return this.request<any>(endpoint, {
      method: "DELETE",
    })
  }
}

export const apiClient = new ApiClient(API_BASE_URL)
