"use client"

import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Shield, ArrowLeft, Home } from "lucide-react"
import { useAuth } from "@/lib/hooks/use-auth"
import { usePermissions, RoleBadge } from "@/lib/hooks/use-permissions"

export default function UnauthorizedPage() {
  const { user } = useAuth()
  const { permissions } = usePermissions()

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4">
            <Shield className="h-6 w-6 text-red-600" />
          </div>
          <CardTitle className="text-2xl font-bold text-gray-900">访问被拒绝</CardTitle>
          <CardDescription>
            您没有访问此页面的权限
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {user && (
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-medium text-gray-900 mb-2">当前用户信息</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">姓名:</span>
                  <span className="font-medium">{user.name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">邮箱:</span>
                  <span className="font-medium">{user.email}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">角色:</span>
                  <RoleBadge role={user.role as any} />
                </div>
              </div>
            </div>
          )}

          {permissions && (
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-medium text-gray-900 mb-2">您的权限</h3>
              <div className="space-y-1">
                {permissions.permissions.length > 0 ? (
                  permissions.permissions.map((permission, index) => (
                    <div key={index} className="text-sm text-gray-600">
                      • {getPermissionDisplayName(permission)}
                    </div>
                  ))
                ) : (
                  <p className="text-sm text-gray-500">暂无特殊权限</p>
                )}
              </div>
            </div>
          )}

          <div className="space-y-3">
            <p className="text-sm text-gray-600 text-center">
              如果您认为这是一个错误，请联系系统管理员获取帮助。
            </p>
            
            <div className="flex flex-col space-y-2">
              <Button asChild className="w-full">
                <Link href="/dashboard">
                  <Home className="h-4 w-4 mr-2" />
                  返回仪表板
                </Link>
              </Button>
              
              <Button variant="outline" asChild className="w-full">
                <Link href="javascript:history.back()">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  返回上一页
                </Link>
              </Button>
            </div>
          </div>

          <div className="text-center">
            <p className="text-xs text-gray-500">
              需要更高权限？
              <Link href="/contact" className="text-blue-600 hover:text-blue-700 ml-1">
                联系管理员
              </Link>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

// 权限显示名称映射
function getPermissionDisplayName(permission: string): string {
  const permissionNames: Record<string, string> = {
    "dataset:read": "数据集读取",
    "dataset:write": "数据集写入",
    "dataset:admin": "数据集管理",
    "query:execute": "执行查询",
    "query:save": "保存查询",
    "query:delete": "删除查询",
    "query:share": "分享查询",
    "export:create": "创建导出",
    "export:download": "下载导出",
    "user:read": "用户查看",
    "user:write": "用户管理",
    "user:delete": "用户删除",
    "system:admin": "系统管理",
    "system:config": "系统配置",
  }
  
  return permissionNames[permission] || permission
}
