# 医学数据分析平台后端 - 环境变量配置示例
# 复制此文件为 .env 并根据实际情况修改配置

# ===========================================
# 数据库配置
# ===========================================
DATABASE_URL=postgres://medical_w:medical_password_2024@localhost:5432/medical_data_platform?sslmode=disable
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=medical_w
POSTGRES_PASSWORD=medical_password_2024
POSTGRES_DB=medical_data_platform

# ===========================================
# 服务器配置
# ===========================================
SERVER_HOST=localhost
SERVER_PORT=8080
SERVER_MODE=debug
# 可选值: debug, release

# ===========================================
# JWT配置
# ===========================================
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRATION=24h
# 可选值: 1h, 24h, 168h (7天)

# ===========================================
# 日志配置
# ===========================================
LOG_LEVEL=info
# 可选值: debug, info, warn, error

LOG_DIR=logs
# 日志文件存储目录

# ===========================================
# Gin框架配置
# ===========================================
GIN_MODE=debug
# 可选值: debug, release, test

# ===========================================
# 文件存储配置
# ===========================================
EXPORT_DIR=exports
# 导出文件存储目录

UPLOAD_DIR=uploads
# 上传文件存储目录

MAX_FILE_SIZE=100MB
# 最大文件上传大小

# ===========================================
# 缓存配置 (可选)
# ===========================================
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
CACHE_TTL=3600
# 缓存过期时间（秒）

# ===========================================
# 邮件配置 (可选)
# ===========================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
FROM_EMAIL=<EMAIL>

# ===========================================
# 外部API配置 (可选)
# ===========================================
EXTERNAL_API_KEY=your-external-api-key
EXTERNAL_API_URL=https://api.external-service.com

# ===========================================
# 监控和追踪配置 (可选)
# ===========================================
JAEGER_ENDPOINT=http://localhost:14268/api/traces
PROMETHEUS_PORT=9090
METRICS_ENABLED=true

# ===========================================
# 安全配置
# ===========================================
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-Request-ID

# 请求限制
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=1m
# 每分钟最多100个请求

# ===========================================
# 开发环境特定配置
# ===========================================
# 开发模式下的特殊配置
DEV_MODE=true
DEBUG_SQL=false
MOCK_EXTERNAL_APIS=true

# ===========================================
# 生产环境特定配置
# ===========================================
# 生产环境下取消注释并配置以下选项
# SSL_CERT_PATH=/path/to/cert.pem
# SSL_KEY_PATH=/path/to/key.pem
# ENABLE_HTTPS=true
# TRUSTED_PROXIES=10.0.0.0/8,**********/12,***********/16

# ===========================================
# 数据库连接池配置
# ===========================================
DB_MAX_OPEN_CONNS=25
DB_MAX_IDLE_CONNS=5
DB_CONN_MAX_LIFETIME=5m

# ===========================================
# 应用特定配置
# ===========================================
# 查询超时时间
QUERY_TIMEOUT=30s

# 导出任务超时时间
EXPORT_TIMEOUT=300s

# 最大并发查询数
MAX_CONCURRENT_QUERIES=10

# 数据集配置
DATASET_CACHE_TTL=3600
ENABLE_DATASET_CACHE=true 