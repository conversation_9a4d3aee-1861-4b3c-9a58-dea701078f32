package main

import (
	"context"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/chungzy/medical-data-platform/internal/app"
	"github.com/chungzy/medical-data-platform/internal/config"
	"github.com/chungzy/medical-data-platform/pkg/database"
	"github.com/chungzy/medical-data-platform/pkg/jwt"
	"github.com/chungzy/medical-data-platform/pkg/logger"
)

func main() {
	// 加载配置
	cfg := config.Load()

	logger.Info("Starting medical data platform server")
	logger.WithField("config", cfg.Server).Info("Server configuration loaded")

	// 初始化数据库
	db, err := database.ConnectWithConfig(cfg)
	if err != nil {
		logger.WithField("error", err).Fatal("Failed to initialize database")
	}
	defer db.Close()
	logger.Info("Database connection established")

	// 初始化JWT管理器
	jwtManager := jwt.NewJWTManager(cfg.JWT.SecretKey, cfg.JWT.ExpirationTime)
	logger.Info("JWT manager initialized")

	// 初始化应用程序
	application := app.Initialize(cfg, db, jwtManager)

	// 启动应用程序
	if err := application.Start(); err != nil {
		logger.WithField("error", err).Fatal("Failed to start application")
	}

	// 设置优雅关闭
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 等待关闭信号
	<-sigChan
	logger.Info("Received shutdown signal, shutting down application")

	// 创建关闭上下文，30秒超时
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 优雅关闭应用程序
	if err := application.Shutdown(ctx); err != nil {
		logger.WithField("error", err).Error("Failed to shutdown application gracefully")
		os.Exit(1)
	}

	logger.Info("Application shutdown completed successfully")
}
