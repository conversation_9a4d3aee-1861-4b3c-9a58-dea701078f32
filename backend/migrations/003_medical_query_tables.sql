-- 医学查询相关表结构
-- 创建时间: 2025-01-26

-- 1. 医学查询历史表
CREATE TABLE IF NOT EXISTS medical_query_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    study_name VARCHAR(200) NOT NULL,
    query_config JSONB NOT NULL,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed')),
    record_count INTEGER DEFAULT 0,
    execution_time INTEGER DEFAULT 0, -- 执行时间(毫秒)
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 2. 医学查询模板表
CREATE TABLE IF NOT EXISTS medical_query_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    category VARCHAR(50) NOT NULL,
    query_config J<PERSON>NB NOT NULL,
    created_by UUID REFERENCES users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_public BOOLEAN DEFAULT false,
    usage_count INTEGER DEFAULT 0,
    tags TEXT[] DEFAULT '{}'
);

-- 3. 医学字段分类表（扩展现有的data_fields表）
-- 为data_fields表添加医学相关字段
ALTER TABLE data_fields ADD COLUMN IF NOT EXISTS table_name VARCHAR(100);
ALTER TABLE data_fields ADD COLUMN IF NOT EXISTS is_medical_field BOOLEAN DEFAULT false;
ALTER TABLE data_fields ADD COLUMN IF NOT EXISTS medical_category VARCHAR(50);
ALTER TABLE data_fields ADD COLUMN IF NOT EXISTS display_order INTEGER DEFAULT 0;

-- 4. 查询执行日志表
CREATE TABLE IF NOT EXISTS query_execution_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    query_id UUID,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    query_type VARCHAR(20) DEFAULT 'medical' CHECK (query_type IN ('medical', 'standard')),
    sql_query TEXT NOT NULL,
    execution_time INTEGER NOT NULL, -- 毫秒
    record_count INTEGER DEFAULT 0,
    status VARCHAR(20) NOT NULL CHECK (status IN ('success', 'error')),
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 5. 用户查询偏好表
CREATE TABLE IF NOT EXISTS user_query_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE UNIQUE,
    preferred_categories TEXT[] DEFAULT '{}',
    default_output_format VARCHAR(10) DEFAULT 'csv' CHECK (default_output_format IN ('csv', 'excel', 'json')),
    default_max_records INTEGER DEFAULT 1000,
    auto_save_queries BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 6. 查询结果缓存表
CREATE TABLE IF NOT EXISTS query_result_cache (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    query_hash VARCHAR(64) NOT NULL UNIQUE, -- 查询配置的哈希值
    query_config JSONB NOT NULL,
    result_data JSONB,
    record_count INTEGER DEFAULT 0,
    execution_time INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    access_count INTEGER DEFAULT 0,
    last_accessed TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_medical_query_history_user_id ON medical_query_history(user_id);
CREATE INDEX IF NOT EXISTS idx_medical_query_history_created_at ON medical_query_history(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_medical_query_history_status ON medical_query_history(status);

CREATE INDEX IF NOT EXISTS idx_medical_query_templates_category ON medical_query_templates(category);
CREATE INDEX IF NOT EXISTS idx_medical_query_templates_created_by ON medical_query_templates(created_by);
CREATE INDEX IF NOT EXISTS idx_medical_query_templates_is_public ON medical_query_templates(is_public);
CREATE INDEX IF NOT EXISTS idx_medical_query_templates_usage_count ON medical_query_templates(usage_count DESC);

CREATE INDEX IF NOT EXISTS idx_data_fields_medical_category ON data_fields(medical_category);
CREATE INDEX IF NOT EXISTS idx_data_fields_is_medical ON data_fields(is_medical_field);
CREATE INDEX IF NOT EXISTS idx_data_fields_table_name ON data_fields(table_name);

CREATE INDEX IF NOT EXISTS idx_query_execution_logs_user_id ON query_execution_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_query_execution_logs_created_at ON query_execution_logs(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_query_execution_logs_query_type ON query_execution_logs(query_type);

CREATE INDEX IF NOT EXISTS idx_query_result_cache_hash ON query_result_cache(query_hash);
CREATE INDEX IF NOT EXISTS idx_query_result_cache_expires_at ON query_result_cache(expires_at);

-- 插入预定义的医学字段分类数据
INSERT INTO data_fields (dataset_id, name, name_en, code, data_type, category, description, unit, table_name, is_medical_field, medical_category, display_order) VALUES
-- 基本信息类别
('mimic_iv', '患者ID', 'subject_id', 'subject_id', 'integer', 'identifier', '患者唯一标识符', NULL, 'patients', true, 'demographics', 1),
('mimic_iv', '年龄', 'anchor_age', 'anchor_age', 'integer', 'demographics', '患者年龄', '岁', 'patients', true, 'demographics', 2),
('mimic_iv', '性别', 'gender', 'gender', 'string', 'demographics', '患者性别', NULL, 'patients', true, 'demographics', 3),
('mimic_iv', '死亡日期', 'dod', 'dod', 'date', 'demographics', '患者死亡日期', NULL, 'patients', true, 'demographics', 4),

-- 住院信息类别
('mimic_iv', '住院ID', 'hadm_id', 'hadm_id', 'integer', 'identifier', '住院唯一标识符', NULL, 'admissions', true, 'admission', 1),
('mimic_iv', '入院时间', 'admittime', 'admittime', 'timestamp', 'temporal', '入院时间', NULL, 'admissions', true, 'admission', 2),
('mimic_iv', '出院时间', 'dischtime', 'dischtime', 'timestamp', 'temporal', '出院时间', NULL, 'admissions', true, 'admission', 3),
('mimic_iv', '死亡时间', 'deathtime', 'deathtime', 'timestamp', 'temporal', '院内死亡时间', NULL, 'admissions', true, 'admission', 4),
('mimic_iv', '入院类型', 'admission_type', 'admission_type', 'string', 'categorical', '入院类型', NULL, 'admissions', true, 'admission', 5),
('mimic_iv', '入院科室', 'admission_location', 'admission_location', 'string', 'categorical', '入院科室', NULL, 'admissions', true, 'admission', 6),
('mimic_iv', '出院科室', 'discharge_location', 'discharge_location', 'string', 'categorical', '出院科室', NULL, 'admissions', true, 'admission', 7),
('mimic_iv', '保险类型', 'insurance', 'insurance', 'string', 'categorical', '保险类型', NULL, 'admissions', true, 'admission', 8),
('mimic_iv', '婚姻状况', 'marital_status', 'marital_status', 'string', 'categorical', '婚姻状况', NULL, 'admissions', true, 'admission', 9),
('mimic_iv', '种族', 'race', 'race', 'string', 'categorical', '种族', NULL, 'admissions', true, 'admission', 10),
('mimic_iv', '院内死亡标志', 'hospital_expire_flag', 'hospital_expire_flag', 'integer', 'categorical', '是否院内死亡', NULL, 'admissions', true, 'admission', 11),

-- 检验结果类别
('mimic_iv', '检验事件ID', 'labevent_id', 'labevent_id', 'integer', 'identifier', '检验事件唯一标识符', NULL, 'labevents', true, 'laboratory', 1),
('mimic_iv', '检验项目ID', 'itemid', 'itemid', 'integer', 'identifier', '检验项目标识符', NULL, 'labevents', true, 'laboratory', 2),
('mimic_iv', '检验时间', 'charttime', 'charttime', 'timestamp', 'temporal', '检验时间', NULL, 'labevents', true, 'laboratory', 3),
('mimic_iv', '检验值', 'valuenum', 'valuenum', 'float', 'numerical', '检验数值', NULL, 'labevents', true, 'laboratory', 4),
('mimic_iv', '检验值文本', 'value', 'value', 'string', 'textual', '检验值文本描述', NULL, 'labevents', true, 'laboratory', 5),
('mimic_iv', '检验单位', 'valueuom', 'valueuom', 'string', 'categorical', '检验值单位', NULL, 'labevents', true, 'laboratory', 6),
('mimic_iv', '参考范围下限', 'ref_range_lower', 'ref_range_lower', 'float', 'numerical', '参考范围下限', NULL, 'labevents', true, 'laboratory', 7),
('mimic_iv', '参考范围上限', 'ref_range_upper', 'ref_range_upper', 'float', 'numerical', '参考范围上限', NULL, 'labevents', true, 'laboratory', 8),
('mimic_iv', '异常标志', 'flag', 'flag', 'string', 'categorical', '检验结果异常标志', NULL, 'labevents', true, 'laboratory', 9),

-- 诊断信息类别
('mimic_iv', 'ICD诊断码', 'icd_code', 'icd_code', 'string', 'categorical', 'ICD诊断代码', NULL, 'diagnoses_icd', true, 'diagnosis', 1),
('mimic_iv', 'ICD版本', 'icd_version', 'icd_version', 'integer', 'categorical', 'ICD编码版本', NULL, 'diagnoses_icd', true, 'diagnosis', 2),
('mimic_iv', '诊断序号', 'seq_num', 'seq_num', 'integer', 'numerical', '诊断序号', NULL, 'diagnoses_icd', true, 'diagnosis', 3),

-- 用药信息类别
('mimic_iv', '药物名称', 'drug', 'drug', 'string', 'textual', '药物名称', NULL, 'prescriptions', true, 'medication', 1),
('mimic_iv', '药物类型', 'drug_type', 'drug_type', 'string', 'categorical', '药物类型', NULL, 'prescriptions', true, 'medication', 2),
('mimic_iv', '给药途径', 'route', 'route', 'string', 'categorical', '给药途径', NULL, 'prescriptions', true, 'medication', 3),
('mimic_iv', '开始时间', 'starttime', 'starttime', 'timestamp', 'temporal', '用药开始时间', NULL, 'prescriptions', true, 'medication', 4),
('mimic_iv', '结束时间', 'stoptime', 'stoptime', 'timestamp', 'temporal', '用药结束时间', NULL, 'prescriptions', true, 'medication', 5),
('mimic_iv', '每日剂量', 'doses_per_24_hrs', 'doses_per_24_hrs', 'float', 'numerical', '每24小时剂量', NULL, 'prescriptions', true, 'medication', 6)

ON CONFLICT (dataset_id, name_en) DO NOTHING;

-- 插入预定义的查询模板
INSERT INTO medical_query_templates (name, description, category, query_config, created_by, is_public, usage_count) VALUES
('基础患者信息查询', '获取患者基本人口学信息', 'demographics', 
'{"studyName":"基础患者信息查询","cohortCriteria":[],"dataDimensions":[{"category":"demographics","fields":[{"id":"age","name":"年龄","nameEn":"anchor_age","type":"number","description":"患者年龄","table":"patients","category":"demographics"},{"id":"gender","name":"性别","nameEn":"gender","type":"string","description":"患者性别","table":"patients","category":"demographics"}],"isSelected":true}],"timeRange":{"type":"admission"},"outputFormat":"csv","maxRecords":1000}',
(SELECT id FROM users WHERE role = 'analyst' LIMIT 1), true, 0),

('成年患者住院分析', '分析成年患者的住院情况', 'admission',
'{"studyName":"成年患者住院分析","cohortCriteria":[{"id":"1","category":"demographics","field":"age","operator":">=","value":18,"description":"年龄大于等于18岁","logic":"AND"}],"dataDimensions":[{"category":"demographics","fields":[{"id":"age","name":"年龄","nameEn":"anchor_age","type":"number","description":"患者年龄","table":"patients","category":"demographics"}],"isSelected":true},{"category":"admission","fields":[{"id":"admission_type","name":"入院类型","nameEn":"admission_type","type":"string","description":"入院类型","table":"admissions","category":"admission"}],"isSelected":true}],"timeRange":{"type":"admission"},"outputFormat":"csv","maxRecords":1000}',
(SELECT id FROM users WHERE role = 'analyst' LIMIT 1), true, 0)

ON CONFLICT DO NOTHING;

-- 创建触发器更新updated_at字段
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_medical_query_templates_updated_at 
    BEFORE UPDATE ON medical_query_templates 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_query_preferences_updated_at 
    BEFORE UPDATE ON user_query_preferences 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 创建清理过期缓存的函数
CREATE OR REPLACE FUNCTION cleanup_expired_cache()
RETURNS void AS $$
BEGIN
    DELETE FROM query_result_cache WHERE expires_at < CURRENT_TIMESTAMP;
END;
$$ LANGUAGE plpgsql;
