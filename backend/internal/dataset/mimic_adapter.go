package dataset

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"github.com/chungzy/medical-data-platform/internal/model"
	"github.com/chungzy/medical-data-platform/pkg/dataset"
)

// MimicAdapter MIMIC-IV数据集适配器
type MimicAdapter struct {
	*BaseAdapter
	tableMaps map[string]string
	fieldMaps map[string]string
	joinRules map[string]string
}

// NewMimicAdapter 创建MIMIC适配器
func NewMimicAdapter(config *AdapterConfig, db *sql.DB) *MimicAdapter {
	adapter := &MimicAdapter{
		BaseAdapter: NewBaseAdapter(config, db),
		tableMaps:   make(map[string]string),
		fieldMaps:   make(map[string]string),
		joinRules:   make(map[string]string),
	}
	adapter.initializeMappings()
	return adapter
}

// initializeMappings 初始化映射规则
func (ma *MimicAdapter) initializeMappings() {
	// 表映射 - 所有31张MIMIC-IV表
	ma.tableMaps = map[string]string{
		// mimiciv_hosp schema - 22张表
		"admissions":         "mimiciv_hosp.admissions",
		"d_hcpcs":           "mimiciv_hosp.d_hcpcs",
		"d_icd_diagnoses":   "mimiciv_hosp.d_icd_diagnoses",
		"d_icd_procedures":  "mimiciv_hosp.d_icd_procedures",
		"d_labitems":        "mimiciv_hosp.d_labitems",
		"diagnoses_icd":     "mimiciv_hosp.diagnoses_icd",
		"drgcodes":          "mimiciv_hosp.drgcodes",
		"emar":              "mimiciv_hosp.emar",
		"emar_detail":       "mimiciv_hosp.emar_detail",
		"hcpcsevents":       "mimiciv_hosp.hcpcsevents",
		"labevents":         "mimiciv_hosp.labevents",
		"microbiologyevents": "mimiciv_hosp.microbiologyevents",
		"omr":               "mimiciv_hosp.omr",
		"patients":          "mimiciv_hosp.patients",
		"pharmacy":          "mimiciv_hosp.pharmacy",
		"poe":               "mimiciv_hosp.poe",
		"poe_detail":        "mimiciv_hosp.poe_detail",
		"prescriptions":     "mimiciv_hosp.prescriptions",
		"procedures_icd":    "mimiciv_hosp.procedures_icd",
		"provider":          "mimiciv_hosp.provider",
		"services":          "mimiciv_hosp.services",
		"transfers":         "mimiciv_hosp.transfers",
		
		// mimiciv_icu schema - 9张表
		"caregiver":         "mimiciv_icu.caregiver",
		"chartevents":       "mimiciv_icu.chartevents",
		"d_items":           "mimiciv_icu.d_items",
		"datetimeevents":    "mimiciv_icu.datetimeevents",
		"icustays":          "mimiciv_icu.icustays",
		"ingredientevents":  "mimiciv_icu.ingredientevents",
		"inputevents":       "mimiciv_icu.inputevents",
		"outputevents":      "mimiciv_icu.outputevents",
		"procedureevents":   "mimiciv_icu.procedureevents",
	}

	// JOIN规则
	ma.joinRules = map[string]string{
		"patients-admissions":    "patients.subject_id = admissions.subject_id",
		"admissions-labevents":   "admissions.hadm_id = labevents.hadm_id",
		"admissions-diagnoses_icd": "admissions.hadm_id = diagnoses_icd.hadm_id",
		"admissions-prescriptions": "admissions.hadm_id = prescriptions.hadm_id",
		"admissions-icustays":    "admissions.hadm_id = icustays.hadm_id",
		"icustays-chartevents":   "icustays.stay_id = chartevents.stay_id",
		"icustays-inputevents":   "icustays.stay_id = inputevents.stay_id",
		"icustays-outputevents":  "icustays.stay_id = outputevents.stay_id",
	}
}

// GetTables 获取数据集表列表
func (ma *MimicAdapter) GetTables(ctx context.Context) ([]model.DatasetTable, error) {
	query := `
		SELECT 
			table_schema,
			table_name,
			table_type
		FROM information_schema.tables 
		WHERE table_schema IN ('mimiciv_hosp', 'mimiciv_icu', 'mimiciv_derived')
		ORDER BY table_schema, table_name
	`

	rows, err := ma.db.QueryContext(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query tables: %w", err)
	}
	defer rows.Close()

	var tables []model.DatasetTable
	for rows.Next() {
		var table model.DatasetTable
		if err := rows.Scan(&table.Schema, &table.Name, &table.Type); err != nil {
			return nil, fmt.Errorf("failed to scan table row: %w", err)
		}
		tables = append(tables, table)
	}

	return tables, nil
}

// GetFields 获取表字段列表
func (ma *MimicAdapter) GetFields(ctx context.Context, tableName string) ([]model.DataField, error) {
	// 获取完整表名
	fullTableName := ma.MapTable(tableName)
	parts := strings.Split(fullTableName, ".")
	if len(parts) != 2 {
		return nil, fmt.Errorf("invalid table name format: %s", fullTableName)
	}
	schema, table := parts[0], parts[1]

	query := `
		SELECT 
			column_name,
			data_type,
			is_nullable,
			column_default,
			ordinal_position
		FROM information_schema.columns 
		WHERE table_schema = $1 AND table_name = $2
		ORDER BY ordinal_position
	`

	rows, err := ma.db.QueryContext(ctx, query, schema, table)
	if err != nil {
		return nil, fmt.Errorf("failed to query fields for table %s: %w", tableName, err)
	}
	defer rows.Close()

	var fields []model.DataField
	for rows.Next() {
		var field model.DataField
		var nullable string
		var defaultValue sql.NullString

		if err := rows.Scan(
			&field.Name,
			&field.Type,
			&nullable,
			&defaultValue,
			&field.Position,
		); err != nil {
			return nil, fmt.Errorf("failed to scan field row: %w", err)
		}

		field.Table = tableName
		field.IsNullable = (nullable == "YES")
		if defaultValue.Valid {
			field.DefaultValue = defaultValue.String
		}

		fields = append(fields, field)
	}

	return fields, nil
}

// GetFieldCategories 获取字段分类
func (ma *MimicAdapter) GetFieldCategories(ctx context.Context) ([]model.FieldCategory, error) {
	// 基于MIMIC-IV的医学字段分类
	
	categories := []model.FieldCategory{
		{
			ID:          "demographics",
			Name:        "人口统计学",
			Description: "患者基本人口学信息和标识符",
			Icon:        "users",
		},
		{
			ID:          "temporal",
			Name:        "时间信息",
			Description: "入院、出院、检查等时间相关信息",
			Icon:        "clock",
		},
		{
			ID:          "clinical_measurements",
			Name:        "临床测量",
			Description: "生命体征、实验室检查等数值测量",
			Icon:        "activity",
		},
		{
			ID:          "diagnoses",
			Name:        "诊断信息",
			Description: "疾病诊断、ICD编码等临床诊断",
			Icon:        "stethoscope",
		},
		{
			ID:          "medications",
			Name:        "药物治疗",
			Description: "处方药物、剂量、给药途径等",
			Icon:        "pill",
		},
		{
			ID:          "procedures",
			Name:        "医疗程序",
			Description: "手术、操作、医疗程序等",
			Icon:        "scissors",
		},
		{
			ID:          "administrative",
			Name:        "管理信息",
			Description: "保险、入院类型、出院去向等管理信息",
			Icon:        "clipboard",
		},
	}

	return categories, nil
}

// BuildQuery 构建查询SQL
func (ma *MimicAdapter) BuildQuery(dsl *model.QueryDSL) (string, []interface{}, error) {
	// 这里应该实现DSL到SQL的转换
	// 目前返回简单的实现
	return "SELECT 1", []interface{}{}, nil
}

// ExecuteQuery 执行查询
func (ma *MimicAdapter) ExecuteQuery(ctx context.Context, sql string, args []interface{}) ([]map[string]interface{}, error) {
	rows, err := ma.db.QueryContext(ctx, sql, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to execute query: %w", err)
	}
	defer rows.Close()

	columns, err := rows.Columns()
	if err != nil {
		return nil, fmt.Errorf("failed to get columns: %w", err)
	}

	var results []map[string]interface{}
	for rows.Next() {
		values := make([]interface{}, len(columns))
		valuePtrs := make([]interface{}, len(columns))
		for i := range values {
			valuePtrs[i] = &values[i]
		}

		if err := rows.Scan(valuePtrs...); err != nil {
			return nil, fmt.Errorf("failed to scan row: %w", err)
		}

		row := make(map[string]interface{})
		for i, col := range columns {
			row[col] = values[i]
		}
		results = append(results, row)
	}

	return results, nil
}

// ExecuteQueryWithCount 执行查询并返回总数
func (ma *MimicAdapter) ExecuteQueryWithCount(ctx context.Context, sql string, args []interface{}) ([]map[string]interface{}, int, error) {
	results, err := ma.ExecuteQuery(ctx, sql, args)
	if err != nil {
		return nil, 0, err
	}
	return results, len(results), nil
}

// ValidateQuery 验证查询
func (ma *MimicAdapter) ValidateQuery(dsl *model.QueryDSL) error {
	// 实现查询验证逻辑
	return nil
}

// MapField 映射字段名
func (ma *MimicAdapter) MapField(field string) string {
	if mapped, exists := ma.fieldMaps[field]; exists {
		return mapped
	}
	return field
}

// MapTable 映射表名
func (ma *MimicAdapter) MapTable(table string) string {
	if mapped, exists := ma.tableMaps[table]; exists {
		return mapped
	}
	return table
}

// GetJoinCondition 获取表间JOIN条件
func (ma *MimicAdapter) GetJoinCondition(table1, table2 string) string {
	key1 := fmt.Sprintf("%s-%s", table1, table2)
	key2 := fmt.Sprintf("%s-%s", table2, table1)
	
	if condition, exists := ma.joinRules[key1]; exists {
		return condition
	}
	if condition, exists := ma.joinRules[key2]; exists {
		return condition
	}
	
	// 默认通过subject_id关联
	return fmt.Sprintf("%s.subject_id = %s.subject_id", table1, table2)
}
