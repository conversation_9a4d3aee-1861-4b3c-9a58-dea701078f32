"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Database, Search, BookOpen, Code, Info, Star, TrendingUp } from "lucide-react"
import { DashboardLayout } from "@/components/dashboard-layout"
import { useDatasets } from "@/lib/hooks/use-datasets"
import { apiClient } from "@/lib/api"

interface DataField {
  id: string
  name: string
  nameEn: string
  code: string
  type: string
  category: string
  description: string
  unit?: string
  range?: string
  examples?: string[]
  popularity?: number
}

export default function DictionaryPage() {
  const { datasets } = useDatasets()
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedDataset, setSelectedDataset] = useState("mimic-iv")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [fields, setFields] = useState<DataField[]>([])
  const [categories, setCategories] = useState([])
  const [loading, setLoading] = useState(false)

  const defaultCategories = [
    { value: "all", label: "全部", icon: "📋", count: 156 },
    { value: "demographics", label: "基本信息", icon: "👤", count: 28 },
    { value: "vitals", label: "生命体征", icon: "💓", count: 35 },
    { value: "labs", label: "实验室指标", icon: "🧪", count: 42 },
    { value: "diagnosis", label: "诊断信息", icon: "🏥", count: 31 },
    { value: "medications", label: "用药信息", icon: "💊", count: 20 },
  ]

  const sampleFields: DataField[] = [
    {
      id: "1",
      name: "患者ID",
      nameEn: "Patient ID",
      code: "SUBJECT_ID",
      type: "INTEGER",
      category: "demographics",
      description: "患者的唯一标识符，用于关联同一患者的所有记录。这是数据库中最重要的主键字段之一。",
      examples: ["10001", "10002", "10003"],
      popularity: 95,
    },
    {
      id: "2",
      name: "年龄",
      nameEn: "Age",
      code: "AGE",
      type: "INTEGER",
      category: "demographics",
      description: "患者入院时的年龄，以年为单位。对于隐私保护，超过89岁的患者年龄会被标记为300+。",
      unit: "岁",
      range: "18-300",
      examples: ["65", "45", "72"],
      popularity: 88,
    },
    {
      id: "3",
      name: "心率",
      nameEn: "Heart Rate",
      code: "HEART_RATE",
      type: "NUMERIC",
      category: "vitals",
      description: "患者的心率测量值，通常通过心电监护仪连续监测获得。是评估患者心血管状态的重要指标。",
      unit: "次/分钟",
      range: "30-200",
      examples: ["72", "85", "110"],
      popularity: 92,
    },
    {
      id: "4",
      name: "血红蛋白",
      nameEn: "Hemoglobin",
      code: "HGB",
      type: "NUMERIC",
      category: "labs",
      description: "血液中血红蛋白浓度，反映患者的贫血状态和氧气携带能力。是最常见的血液检查项目之一。",
      unit: "g/dL",
      range: "6.0-18.0",
      examples: ["12.5", "14.2", "9.8"],
      popularity: 85,
    },
    {
      id: "5",
      name: "收缩压",
      nameEn: "Systolic Blood Pressure",
      code: "SBP",
      type: "NUMERIC",
      category: "vitals",
      description: "收缩期血压测量值，反映心脏收缩时血管内的压力。是评估心血管健康的关键指标。",
      unit: "mmHg",
      range: "70-250",
      examples: ["120", "140", "95"],
      popularity: 90,
    },
    {
      id: "6",
      name: "主要诊断",
      nameEn: "Primary Diagnosis",
      code: "PRIMARY_DIAG",
      type: "VARCHAR",
      category: "diagnosis",
      description: "患者的主要诊断，通常使用ICD编码系统进行标准化编码。代表患者住院的主要原因。",
      examples: ["I21.9", "J44.1", "N18.6"],
      popularity: 78,
    },
  ]

  useEffect(() => {
    fetchFields()
    fetchCategories()
  }, [selectedDataset, selectedCategory, searchTerm])

  const fetchFields = async () => {
    setLoading(true)
    try {
      const response = await apiClient.searchFields({
        q: searchTerm,
        dataset: selectedDataset !== "all" ? selectedDataset : undefined,
        category: selectedCategory !== "all" ? selectedCategory : undefined,
      })

      if (response.data) {
        setFields(response.data.fields || [])
      } else {
        // 使用模拟数据
        setFields(
          sampleFields.filter((field) => {
            const matchesSearch =
              !searchTerm ||
              field.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
              field.nameEn.toLowerCase().includes(searchTerm.toLowerCase()) ||
              field.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
              field.description.toLowerCase().includes(searchTerm.toLowerCase())

            const matchesCategory = selectedCategory === "all" || field.category === selectedCategory

            return matchesSearch && matchesCategory
          }),
        )
      }
    } catch (error) {
      console.error("Failed to fetch fields:", error)
      setFields(sampleFields)
    } finally {
      setLoading(false)
    }
  }

  const fetchCategories = async () => {
    try {
      const response = await apiClient.getCategories()
      if (response.data) {
        setCategories(response.data.categories || [])
      } else {
        setCategories(defaultCategories)
      }
    } catch (error) {
      setCategories(defaultCategories)
    }
  }

  const getCategoryIcon = (category: string) => {
    const cat = (categories.length > 0 ? categories : defaultCategories).find((c: any) => c.value === category)
    return cat?.icon || "📋"
  }

  const getPopularityColor = (popularity: number) => {
    if (popularity >= 90) return "text-emerald-600"
    if (popularity >= 80) return "text-slate-600"
    if (popularity >= 70) return "text-amber-600"
    return "text-slate-600"
  }

  const getDatasetColor = (datasetId: string) => {
    const index = datasets.findIndex((d) => d.id === datasetId)
    const colors = [
      "from-slate-600 to-slate-700",
      "from-emerald-600 to-emerald-700",
      "from-amber-600 to-amber-700",
      "from-rose-600 to-rose-700",
      "from-violet-600 to-violet-700",
      "from-cyan-600 to-cyan-700",
    ]
    return colors[index % colors.length] || colors[0]
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
          <div className="space-y-2">
            <h1 className="text-4xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 bg-clip-text text-transparent">
              数据字典
            </h1>
            <p className="text-lg text-slate-600">浏览和搜索医学数据库字段定义</p>
          </div>
        </div>

        {/* Search and Filter */}
        <Card className="border-0 shadow-lg">
          <CardHeader className="pb-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-r from-slate-700 to-slate-800 rounded-xl">
                <Search className="h-5 w-5 text-white" />
              </div>
              <div>
                <CardTitle className="text-xl">搜索和筛选</CardTitle>
                <CardDescription>快速找到您需要的数据字段</CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-2">
                <label className="text-sm font-medium text-slate-700">搜索字段</label>
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                  <Input
                    placeholder="搜索字段名称、编码或描述..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 h-11"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-slate-700">数据集</label>
                <Select value={selectedDataset} onValueChange={setSelectedDataset}>
                  <SelectTrigger className="h-11">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {datasets.map((dataset, index) => (
                      <SelectItem key={dataset.id} value={dataset.id}>
                        <div className="flex items-center gap-2">
                          <div className={`w-3 h-3 rounded-full bg-gradient-to-r ${getDatasetColor(dataset.id)}`}></div>
                          {dataset.name}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-slate-700">分类</label>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className="h-11">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {(categories.length > 0 ? categories : defaultCategories).map((category: any) => (
                      <SelectItem key={category.value} value={category.value}>
                        <div className="flex items-center gap-2">
                          <span>{category.icon}</span>
                          <span>{category.label}</span>
                          <Badge variant="secondary" className="ml-auto text-xs">
                            {category.count}
                          </Badge>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Category Overview */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {(categories.length > 0 ? categories : defaultCategories).slice(1).map((category: any) => (
            <Card
              key={category.value}
              className={`border-0 shadow-sm cursor-pointer transition-all duration-200 hover:shadow-md ${
                selectedCategory === category.value ? "ring-2 ring-slate-500 bg-slate-50" : ""
              }`}
              onClick={() => setSelectedCategory(category.value)}
            >
              <CardContent className="pt-4 text-center">
                <div className="text-2xl mb-2">{category.icon}</div>
                <div className="text-sm font-medium text-slate-900">{category.label}</div>
                <div className="text-xs text-slate-500">{category.count} 字段</div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Results */}
        <Card className="border-0 shadow-lg">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-r from-emerald-600 to-emerald-700 rounded-xl">
                  <Database className="h-5 w-5 text-white" />
                </div>
                <div>
                  <CardTitle className="text-xl">字段列表</CardTitle>
                  <CardDescription>
                    当前数据集: {datasets.find((d) => d.id === selectedDataset)?.name || "全部"}
                  </CardDescription>
                </div>
              </div>
              <Badge variant="secondary" className="text-sm px-3 py-1">
                {fields.length} 个字段
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="space-y-6">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="animate-pulse">
                    <div className="h-32 bg-slate-100 rounded-xl"></div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="space-y-6">
                {fields.map((field) => (
                  <Card
                    key={field.id}
                    className="border border-slate-200 hover:border-slate-300 hover:shadow-md transition-all duration-200"
                  >
                    <CardContent className="pt-6">
                      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                        <div className="lg:col-span-3">
                          <div className="flex items-start justify-between mb-4">
                            <div className="flex items-start gap-3">
                              <div className="text-2xl">{getCategoryIcon(field.category)}</div>
                              <div>
                                <h3 className="font-semibold text-xl text-slate-900">{field.name}</h3>
                                <p className="text-sm text-slate-600 mt-1">{field.nameEn}</p>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge variant="outline" className="font-mono text-xs">
                                {field.type}
                              </Badge>
                              <Badge variant="secondary">
                                {
                                  (categories.length > 0 ? categories : defaultCategories).find(
                                    (c: any) => c.value === field.category,
                                  )?.label
                                }
                              </Badge>
                              {field.popularity && (
                                <div className="flex items-center gap-1">
                                  <Star className={`h-3 w-3 ${getPopularityColor(field.popularity)}`} />
                                  <span className={`text-xs font-medium ${getPopularityColor(field.popularity)}`}>
                                    {field.popularity}%
                                  </span>
                                </div>
                              )}
                            </div>
                          </div>

                          <div className="space-y-4">
                            <div className="flex items-center gap-2">
                              <Code className="h-4 w-4 text-slate-500" />
                              <span className="font-mono text-sm bg-slate-100 px-3 py-1 rounded-lg border">
                                {field.code}
                              </span>
                            </div>

                            <div className="flex items-start gap-2">
                              <Info className="h-4 w-4 text-slate-500 mt-0.5 flex-shrink-0" />
                              <p className="text-sm text-slate-700 leading-relaxed">{field.description}</p>
                            </div>

                            <div className="flex flex-wrap gap-4 text-sm">
                              {field.unit && (
                                <div className="flex items-center gap-1">
                                  <span className="font-medium text-slate-600">单位:</span>
                                  <Badge variant="outline" className="text-xs">
                                    {field.unit}
                                  </Badge>
                                </div>
                              )}

                              {field.range && (
                                <div className="flex items-center gap-1">
                                  <span className="font-medium text-slate-600">范围:</span>
                                  <Badge variant="outline" className="text-xs">
                                    {field.range}
                                  </Badge>
                                </div>
                              )}

                              {field.popularity && (
                                <div className="flex items-center gap-1">
                                  <TrendingUp className="h-3 w-3 text-slate-500" />
                                  <span className="font-medium text-slate-600">使用率:</span>
                                  <span className={`text-xs font-medium ${getPopularityColor(field.popularity)}`}>
                                    {field.popularity}%
                                  </span>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>

                        <div className="lg:col-span-1">
                          {field.examples && (
                            <div className="bg-slate-50 rounded-xl p-4 border">
                              <h4 className="font-medium text-sm mb-3 text-slate-700">示例值</h4>
                              <div className="space-y-2">
                                {field.examples.map((example, index) => (
                                  <div
                                    key={index}
                                    className="font-mono text-xs bg-white px-3 py-2 rounded-lg border text-slate-800"
                                  >
                                    {example}
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}

                {fields.length === 0 && (
                  <div className="text-center py-16">
                    <BookOpen className="h-16 w-16 mx-auto mb-6 text-slate-300" />
                    <h3 className="text-xl font-medium text-slate-900 mb-2">未找到匹配的字段</h3>
                    <p className="text-slate-500 mb-6">请尝试调整搜索条件或筛选器</p>
                    <Button
                      onClick={() => {
                        setSearchTerm("")
                        setSelectedCategory("all")
                      }}
                    >
                      重置筛选条件
                    </Button>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          {(categories.length > 0 ? categories : defaultCategories).slice(1, 5).map((category: any, index) => {
            const colors = [
              "from-slate-600 to-slate-700",
              "from-emerald-600 to-emerald-700",
              "from-amber-600 to-amber-700",
              "from-rose-600 to-rose-700",
            ]
            return (
              <Card key={category.value} className="border-0 shadow-lg">
                <CardContent className="pt-6">
                  <div className="flex items-center gap-3">
                    <div className={`p-3 rounded-xl bg-gradient-to-r ${colors[index]} shadow-lg`}>
                      <span className="text-white text-lg">{category.icon}</span>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-slate-900">{category.count}</div>
                      <p className="text-sm text-slate-600">{category.label}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>
      </div>
    </DashboardLayout>
  )
}
