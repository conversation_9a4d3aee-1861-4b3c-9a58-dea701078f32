# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test
.bin
# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Configuration files (keep templates)
config.yaml
config.production.yaml
config.testing.yaml
config.local.yaml
config.development.yaml
*.local.yaml

# But keep example/template files
!config.example.yaml

# Log files
*.log
logs/

# Temporary files
tmp/
temp/

# Database files
*.db
*.sqlite
*.sqlite3

# Environment files
.env
.env.local
.env.production
.env.testing

# Build output
dist/
build/

# Uploads and data files
uploads/
data/
exports/

# Coverage reports
coverage.txt
coverage.html

# Backup files
*.bak
*.backup 