package service

import (
	"context"

	"github.com/chungzy/medical-data-platform/internal/model"
	"github.com/chungzy/medical-data-platform/internal/repository"
)

type DatasetService struct {
	datasetRepo repository.DatasetRepository
}

func NewDatasetService(datasetRepo repository.DatasetRepository) *DatasetService {
	return &DatasetService{
		datasetRepo: datasetRepo,
	}
}

// GetAllDatasets 获取所有数据集
func (ds *DatasetService) GetAllDatasets(ctx context.Context) ([]model.Dataset, error) {
	return ds.datasetRepo.GetAll(ctx)
}

// GetDatasetByID 根据ID获取数据集详情
func (ds *DatasetService) GetDatasetByID(ctx context.Context, id string) (*model.DatasetDetailResponse, error) {
	// 获取数据集基本信息
	dataset, err := ds.datasetRepo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	// 获取数据集字段
	fields, err := ds.datasetRepo.GetFields(ctx, id)
	if err != nil {
		return nil, err
	}

	// 获取数据集表信息
	tables, err := ds.datasetRepo.GetTables(ctx, id)
	if err != nil {
		return nil, err
	}

	// 构建响应
	response := &model.DatasetDetailResponse{
		ID:          dataset.ID,
		Name:        dataset.Name,
		Description: dataset.Description,
		Tables:      tables,
		Fields:      fields,
	}

	return response, nil
}
