# 医学数据分析平台配置文件模板
# Medical Data Platform Configuration Template
# 复制此文件为 config.yaml 并修改相应配置

# 应用程序配置
app:
  name: "Medical Data Platform"
  version: "1.0.0"
  environment: "development"  # development, production, testing
  log_level: "info"          # debug, info, warn, error
  max_query_results: 10000   # 单次查询最大返回结果数
  export_file_retention_days: 7  # 导出文件保留天数
  max_concurrent_queries: 5  # 用户最大并发查询数

# 服务器配置
server:
  host: "localhost"          # 服务器监听地址
  port: "8088"              # 服务器端口
  mode: "debug"             # debug, release

# 数据库配置
database:
  host: "localhost"         # 数据库主机地址
  port: 5432               # 数据库端口
  user: "postgres"         # 数据库用户名
  password: "your_password_here"  # 数据库密码 - 请修改
  dbname: "medical_data_platform" # 数据库名称
  sslmode: "disable"       # SSL模式: disable, require, verify-ca, verify-full

# JWT配置
jwt:
  secret_key: "your-jwt-secret-key-change-in-production"  # JWT密钥 - 生产环境请使用强密钥
  expiration_hours: 24     # Token过期时间（小时）

# 数据集配置（可选）
datasets:
  # MIMIC-III配置
  mimic_iii:
    enabled: true
    schema: "mimiciii"
    description: "MIMIC-III Critical Care Database"
  
  # MIMIC-IV配置
  mimic_iv:
    enabled: true
    schema: "mimiciv_hosp,mimiciv_icu"
    description: "MIMIC-IV Critical Care Database"
  
  # eICU配置
  eicu:
    enabled: false
    schema: "eicu_crd"
    description: "eICU Collaborative Research Database"
  
  # NHANES配置
  nhanes:
    enabled: false
    schema: "nhanes"
    description: "National Health and Nutrition Examination Survey"

# 安全配置
security:
  cors:
    allowed_origins: ["http://localhost:3000", "http://localhost:8088"]
    allowed_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    allowed_headers: ["Content-Type", "Authorization"]
    allow_credentials: true
  
  rate_limiting:
    enabled: true
    requests_per_minute: 100
    burst_size: 10

# 日志配置
logging:
  level: "info"            # debug, info, warn, error
  format: "json"           # json, text
  output: "stdout"         # stdout, file
  file_path: "logs/app.log"  # 当output为file时使用

# 缓存配置
cache:
  enabled: true
  type: "memory"           # memory, redis
  ttl_minutes: 30          # 缓存过期时间（分钟）
  max_size: 1000          # 最大缓存条目数

# 文件存储配置
storage:
  type: "local"            # local, s3, minio
  local_path: "./uploads"  # 本地存储路径
  max_file_size_mb: 100   # 最大文件大小（MB） 