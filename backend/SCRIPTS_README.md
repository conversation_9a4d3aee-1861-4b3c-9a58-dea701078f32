# 医疗数据平台后端 - 脚本使用指南

本文档介绍如何使用项目的构建和运行脚本。

## 脚本概览

项目提供了以下脚本用于项目的编译、启动、停止和重启：

- `build.sh` - 构建脚本
- `start.sh` - 启动脚本  
- `stop.sh` - 停止脚本
- `restart.sh` - 重启脚本

## 脚本位置

脚本有两个位置：

1. **根目录脚本**：`backend/` 目录下的脚本（推荐使用）
2. **详细脚本**：`backend/scripts/` 目录下的完整脚本

根目录的脚本是便捷入口，会自动调用 `scripts/` 目录下的详细脚本。

## 构建脚本 (build.sh)

### 基本用法

```bash
# 标准构建
./build.sh

# 显示帮助
./build.sh --help
```

### 构建选项

```bash
# 清理后构建
./build.sh --clean

# 发布模式构建（优化）
./build.sh --release

# 调试模式构建
./build.sh --debug

# 运行测试
./build.sh --test

# 代码检查
./build.sh --lint

# 格式化代码
./build.sh --format

# 详细输出
./build.sh --verbose

# 交叉编译
./build.sh --cross-compile

# 构建Docker镜像
./build.sh --docker

# 组合使用
./build.sh --clean --release --test --lint
```

### 构建输出

- 标准构建：`bin/server`
- 调试构建：`bin/server-debug`
- 交叉编译：`build/server-{os}-{arch}`

## 启动脚本 (start.sh)

### 基本用法

```bash
# 构建并启动
./start.sh --build

# 开发模式启动
./start.sh --dev

# 显示帮助
./start.sh --help
```

### 启动模式

- `--build, -b`：构建后启动
- `--dev, -d`：开发模式启动（不构建）

## 停止脚本 (stop.sh)

### 基本用法

```bash
# 优雅停止
./stop.sh

# 强制停止
./stop.sh --force

# 停止Docker开发环境
./stop.sh --docker

# 停止Docker生产环境
./stop.sh --production
```

### 停止选项

- `--force, -f`：强制停止（使用SIGKILL）
- `--docker, -d`：停止Docker开发环境
- `--production, -p`：停止Docker生产环境
- `--timeout, -t`：设置超时时间（默认30秒）

## 重启脚本 (restart.sh)

### 基本用法

```bash
# 重启本地服务
./restart.sh

# 强制重启
./restart.sh --force

# 重启Docker开发环境
./restart.sh --docker

# 重启Docker生产环境
./restart.sh --production
```

### 重启选项

- `--force, -f`：强制停止后重启
- `--docker, -d`：重启Docker开发环境
- `--production, -p`：重启Docker生产环境
- `--build`：Docker模式下重新构建镜像
- `--pull`：Docker模式下拉取最新镜像
- `--timeout, -t`：停止超时时间
- `--wait, -w`：停止后等待时间

## 常用工作流程

### 开发流程

```bash
# 1. 首次构建
./build.sh --clean --test --lint

# 2. 启动开发服务器
./start.sh --dev

# 3. 代码修改后重新构建
./build.sh

# 4. 重启服务
./restart.sh
```

### 生产部署流程

```bash
# 1. 发布构建
./build.sh --clean --release --test

# 2. 构建Docker镜像
./build.sh --docker

# 3. 启动生产环境
./restart.sh --production --pull
```

### 测试流程

```bash
# 运行完整测试
./build.sh --clean --test --lint --format

# 交叉编译测试
./build.sh --cross-compile
```

## 环境变量

脚本支持以下环境变量：

- `VERSION`：版本号（默认从git获取）
- `DATABASE_URL`：数据库连接URL
- `LOG_LEVEL`：日志级别（默认info）
- `GIN_MODE`：Gin模式（默认debug）
- `SERVER_PORT`：服务器端口（默认8080）

## 故障排除

### 构建失败

1. 检查Go环境：`go version`
2. 检查依赖：`go mod tidy`
3. 清理缓存：`./build.sh --clean`

### 启动失败

1. 检查端口占用：`lsof -i :8080`
2. 检查数据库连接
3. 查看日志：`tail -f logs/app.log`

### 停止失败

1. 查看进程：`ps aux | grep server`
2. 强制停止：`./stop.sh --force`
3. 清理PID文件：`rm -f medical-platform.pid`

## 日志和监控

- 应用日志：`logs/app.log`
- Gin日志：`logs/gin.log`
- 健康检查：`http://localhost:8080/health`
- API文档：`http://localhost:8080/swagger/index.html`

## 更多信息

- 查看Makefile：`make help`
- 项目文档：`doc/` 目录
- 配置说明：`doc/configuration.md`
- 部署指南：`doc/deployment.md`
