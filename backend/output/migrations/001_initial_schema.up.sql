-- 用户表
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20),
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(20) NOT NULL CHECK (role IN ('student', 'doctor', 'researcher', 'teacher', 'analyst')),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 数据集表
CREATE TABLE datasets (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    version VARCHAR(20),
    patient_count INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'maintenance', 'inactive')),
    last_updated TIMESTAMP DEFAULT NOW()
);

-- 数据字段表
CREATE TABLE data_fields (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    dataset_id VARCHAR(50) REFERENCES datasets(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    name_en VARCHAR(100),
    code VARCHAR(50),
    data_type VARCHAR(20),
    category VARCHAR(50),
    description TEXT,
    unit VARCHAR(20),
    value_range VARCHAR(100),
    examples JSONB
);

-- 查询历史表
CREATE TABLE query_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(200),
    dataset_id VARCHAR(50) REFERENCES datasets(id),
    query_config JSONB NOT NULL,
    status VARCHAR(20) DEFAULT 'completed' CHECK (status IN ('completed', 'running', 'failed')),
    record_count INTEGER,
    execution_time INTEGER, -- milliseconds
    created_at TIMESTAMP DEFAULT NOW()
);

-- 查询模板表
CREATE TABLE query_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    category VARCHAR(50),
    dataset_id VARCHAR(50) REFERENCES datasets(id),
    template_config JSONB NOT NULL,
    is_public BOOLEAN DEFAULT false,
    usage_count INTEGER DEFAULT 0,
    rating DECIMAL(2,1) DEFAULT 0,
    author VARCHAR(100),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_data_fields_dataset_id ON data_fields(dataset_id);
CREATE INDEX idx_data_fields_category ON data_fields(category);
CREATE INDEX idx_query_history_user_id ON query_history(user_id);
CREATE INDEX idx_query_history_dataset_id ON query_history(dataset_id);
CREATE INDEX idx_query_history_created_at ON query_history(created_at DESC);
CREATE INDEX idx_query_templates_user_id ON query_templates(user_id);
CREATE INDEX idx_query_templates_dataset_id ON query_templates(dataset_id);
CREATE INDEX idx_query_templates_category ON query_templates(category);
CREATE INDEX idx_query_templates_is_public ON query_templates(is_public);

-- 插入初始数据集
INSERT INTO datasets (id, name, description, version, patient_count, status) VALUES
('mimic_iv', 'MIMIC-IV', 'Medical Information Mart for Intensive Care IV', '2.2', 382278, 'active'),
('eicu', 'eICU Collaborative Research Database', 'Multi-center ICU database', '2.0', 139367, 'active'),
('nhanes', 'National Health and Nutrition Examination Survey', 'US health and nutrition survey data', '2017-2020', 15560, 'active'),
('pic', 'Pediatric Intensive Care', 'Pediatric critical care database', '1.0', 12881, 'active');

-- 插入MIMIC-IV数据字段示例
INSERT INTO data_fields (dataset_id, name, name_en, code, data_type, category, description, unit, examples) VALUES
('mimic_iv', '患者ID', 'Patient ID', 'subject_id', 'integer', 'identifier', '患者唯一标识符', NULL, '["10000032", "10000980", "10001217"]'),
('mimic_iv', '年龄', 'Age', 'anchor_age', 'integer', 'demographics', '患者年龄', 'years', '["65", "76", "89"]'),
('mimic_iv', '性别', 'Gender', 'gender', 'string', 'demographics', '患者性别', NULL, '["M", "F"]'),
('mimic_iv', '入院时间', 'Admission Time', 'admittime', 'timestamp', 'temporal', '入院时间', NULL, '["2180-07-23 16:15:00", "2180-07-23 16:00:00"]'),
('mimic_iv', '出院时间', 'Discharge Time', 'dischtime', 'timestamp', 'temporal', '出院时间', NULL, '["2180-07-30 20:00:00", "2180-07-25 14:00:00"]'),
('mimic_iv', '入院类型', 'Admission Type', 'admission_type', 'string', 'clinical', '入院类型', NULL, '["ELECTIVE", "URGENT", "EMERGENCY"]'),
('mimic_iv', '诊断', 'Diagnosis', 'long_title', 'string', 'clinical', '诊断描述', NULL, '["Acute myocardial infarction", "Pneumonia"]'),
('mimic_iv', '实验室数值', 'Lab Value', 'valuenum', 'float', 'laboratory', '实验室检查数值', 'various', '["7.4", "140", "4.2"]');

-- 插入eICU数据字段示例  
INSERT INTO data_fields (dataset_id, name, name_en, code, data_type, category, description, unit, examples) VALUES
('eicu', '患者单元住院ID', 'Patient Unit Stay ID', 'patientunitstayid', 'integer', 'identifier', '患者ICU住院唯一标识', NULL, '["141168", "141201", "141203"]'),
('eicu', '医院ID', 'Hospital ID', 'hospitalid', 'integer', 'identifier', '医院标识符', NULL, '["73", "264", "443"]'),
('eicu', '年龄', 'Age', 'age', 'string', 'demographics', '患者年龄', 'years', '["73", "40", "> 89"]'),
('eicu', '性别', 'Gender', 'gender', 'string', 'demographics', '患者性别', NULL, '["Male", "Female"]'),
('eicu', 'APACHE评分', 'APACHE Score', 'apachescore', 'integer', 'clinical', 'APACHE III评分', 'points', '["71", "54", "96"]');

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表创建更新时间触发器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_query_templates_updated_at BEFORE UPDATE ON query_templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column(); 