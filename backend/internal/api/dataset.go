package api

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/chungzy/medical-data-platform/internal/model"
	"github.com/chungzy/medical-data-platform/internal/service"
	"github.com/chungzy/medical-data-platform/pkg/logger"
)

type DatasetController struct {
	datasetService *service.DatasetService
}

func NewDatasetController(datasetService *service.DatasetService) *DatasetController {
	return &DatasetController{
		datasetService: datasetService,
	}
}

// GetDatasets 获取数据集列表
func (dc *DatasetController) GetDatasets(c *gin.Context) {
	c.Set("business_operation", "get_datasets")

	logger.InfoWithGinContext(c, "Getting datasets list")

	datasets, err := dc.datasetService.GetAllDatasets(c.Request.Context())
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to get datasets")
		c.<PERSON>(http.StatusInternalServerError, gin.H{"error": "Failed to get datasets", "details": err.Error()})
		return
	}

	logger.InfoWithGinContext(c, "Datasets retrieved successfully", map[string]interface{}{
		"datasets_count": len(datasets),
	})

	response := model.DatasetListResponse{
		Datasets: datasets,
	}

	c.JSON(http.StatusOK, response)
}

// GetDatasetByID 获取数据集详情
func (dc *DatasetController) GetDatasetByID(c *gin.Context) {
	c.Set("business_operation", "get_dataset_by_id")

	id := c.Param("id")
	logger.InfoWithGinContext(c, "Getting dataset by ID", map[string]interface{}{
		"dataset_id": id,
	})

	if id == "" {
		logger.WarnWithGinContext(c, "Dataset ID is required")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Dataset ID is required"})
		return
	}

	dataset, err := dc.datasetService.GetDatasetByID(c.Request.Context(), id)
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Dataset not found", map[string]interface{}{
			"dataset_id": id,
		})
		c.JSON(http.StatusNotFound, gin.H{"error": "Dataset not found", "details": err.Error()})
		return
	}

	logger.InfoWithGinContext(c, "Dataset retrieved successfully", map[string]interface{}{
		"dataset_id":   id,
		"dataset_name": dataset.Name,
	})

	c.JSON(http.StatusOK, dataset)
}
