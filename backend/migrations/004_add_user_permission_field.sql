-- 添加用户权限字段
ALTER TABLE users ADD COLUMN IF NOT EXISTS permission INTEGER DEFAULT 2;

-- 添加权限字段注释
COMMENT ON COLUMN users.permission IS '用户权限级别: 0=无权限, 1=管理员, 2=普通用户';

-- 创建权限字段索引
CREATE INDEX IF NOT EXISTS idx_users_permission ON users(permission);

-- 根据现有role字段更新permission字段
UPDATE users SET permission = CASE 
    WHEN role = 'admin' THEN 1
    WHEN role = 'user' THEN 2
    ELSE 2
END WHERE permission IS NULL OR permission = 0;

-- 为新用户设置默认权限为普通用户
ALTER TABLE users ALTER COLUMN permission SET DEFAULT 2;

-- 添加权限检查约束
ALTER TABLE users ADD CONSTRAINT chk_user_permission CHECK (permission IN (0, 1, 2));
