package dsl

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/chungzy/medical-data-platform/internal/model"
)

type SQLBuilder struct {
	dataset   string
	tableMaps map[string]string // 数据集表映射
	fieldMaps map[string]string // 字段映射
}

func NewSQLBuilder(dataset string) *SQLBuilder {
	return &SQLBuilder{
		dataset:   dataset,
		tableMaps: getTableMappings(dataset),
		fieldMaps: getFieldMappings(dataset),
	}
}

// 构建完整的SQL查询
func (b *SQLBuilder) BuildQuery(dsl *model.QueryDSL) (string, []interface{}, error) {
	var query strings.Builder
	var args []interface{}
	argIndex := 1

	// SELECT 子句
	selectClause, err := b.buildSelectClause(dsl.Select)
	if err != nil {
		return "", nil, fmt.Errorf("building SELECT clause: %w", err)
	}
	query.WriteString("SELECT ")
	query.WriteString(selectClause)

	// FROM 子句
	fromClause, err := b.buildFromClause(dsl.From)
	if err != nil {
		return "", nil, fmt.Errorf("building FROM clause: %w", err)
	}
	query.WriteString(" FROM ")
	query.WriteString(fromClause)

	// JOIN 子句
	if len(dsl.Joins) > 0 {
		joinClause, err := b.buildJoinClause(dsl.Joins)
		if err != nil {
			return "", nil, fmt.Errorf("building JOIN clause: %w", err)
		}
		query.WriteString(" ")
		query.WriteString(joinClause)
	}

	// WHERE 子句
	if dsl.Where != nil {
		whereClause, whereArgs, err := b.buildWhereClause(dsl.Where, argIndex)
		if err != nil {
			return "", nil, fmt.Errorf("building WHERE clause: %w", err)
		}
		query.WriteString(" WHERE ")
		query.WriteString(whereClause)
		args = append(args, whereArgs...)
		argIndex += len(whereArgs)
	}

	// GROUP BY 子句
	if len(dsl.GroupBy) > 0 {
		groupByClause, err := b.buildGroupByClause(dsl.GroupBy)
		if err != nil {
			return "", nil, fmt.Errorf("building GROUP BY clause: %w", err)
		}
		query.WriteString(" GROUP BY ")
		query.WriteString(groupByClause)
	}

	// HAVING 子句
	if dsl.Having != nil {
		havingClause, havingArgs, err := b.buildWhereClause(dsl.Having, argIndex)
		if err != nil {
			return "", nil, fmt.Errorf("building HAVING clause: %w", err)
		}
		query.WriteString(" HAVING ")
		query.WriteString(havingClause)
		args = append(args, havingArgs...)
		argIndex += len(havingArgs)
	}

	// ORDER BY 子句
	if len(dsl.OrderBy) > 0 {
		orderByClause, err := b.buildOrderByClause(dsl.OrderBy)
		if err != nil {
			return "", nil, fmt.Errorf("building ORDER BY clause: %w", err)
		}
		query.WriteString(" ORDER BY ")
		query.WriteString(orderByClause)
	}

	// LIMIT 和 OFFSET
	if dsl.Limit != nil {
		query.WriteString(fmt.Sprintf(" LIMIT %d", *dsl.Limit))
	}
	if dsl.Offset != nil {
		query.WriteString(fmt.Sprintf(" OFFSET %d", *dsl.Offset))
	}

	return query.String(), args, nil
}

func (b *SQLBuilder) buildSelectClause(fields []string) (string, error) {
	var mappedFields []string
	for _, field := range fields {
		mappedField := b.mapField(field)
		mappedFields = append(mappedFields, mappedField)
	}
	return strings.Join(mappedFields, ", "), nil
}

func (b *SQLBuilder) buildFromClause(table string) (string, error) {
	if table == "" {
		// 如果没有指定表，使用数据集的默认主表
		table = b.getDefaultTable()
	}
	return b.mapTable(table), nil
}

func (b *SQLBuilder) buildJoinClause(joins []model.JoinClause) (string, error) {
	var joinParts []string
	for _, join := range joins {
		joinType := "INNER"
		if join.Type != "" {
			joinType = strings.ToUpper(join.Type)
		}

		mappedTable := b.mapTable(join.Table)
		joinPart := fmt.Sprintf("%s JOIN %s ON %s", joinType, mappedTable, join.On)
		joinParts = append(joinParts, joinPart)
	}
	return strings.Join(joinParts, " "), nil
}

func (b *SQLBuilder) buildWhereClause(conditionGroup *model.ConditionGroup, startArgIndex int) (string, []interface{}, error) {
	return b.buildConditionGroup(conditionGroup, startArgIndex)
}

func (b *SQLBuilder) buildConditionGroup(group *model.ConditionGroup, argIndex int) (string, []interface{}, error) {
	var parts []string
	var args []interface{}

	// 处理直接条件
	for _, condition := range group.Conditions {
		conditionSQL, conditionArgs, err := b.buildCondition(condition, argIndex+len(args))
		if err != nil {
			return "", nil, err
		}
		parts = append(parts, conditionSQL)
		args = append(args, conditionArgs...)
	}

	// 处理嵌套条件组
	for _, nestedGroup := range group.Groups {
		nestedSQL, nestedArgs, err := b.buildConditionGroup(&nestedGroup, argIndex+len(args))
		if err != nil {
			return "", nil, err
		}
		parts = append(parts, fmt.Sprintf("(%s)", nestedSQL))
		args = append(args, nestedArgs...)
	}

	logic := "AND"
	if group.Logic != "" {
		logic = strings.ToUpper(group.Logic)
	}

	return strings.Join(parts, fmt.Sprintf(" %s ", logic)), args, nil
}

func (b *SQLBuilder) buildCondition(condition model.Condition, argIndex int) (string, []interface{}, error) {
	field := b.mapField(condition.Field)
	operator := strings.ToUpper(condition.Operator)

	switch operator {
	case "IS_NULL":
		return fmt.Sprintf("%s IS NULL", field), nil, nil
	case "IS_NOT_NULL":
		return fmt.Sprintf("%s IS NOT NULL", field), nil, nil
	case "IN":
		values, ok := condition.Value.([]interface{})
		if !ok {
			return "", nil, fmt.Errorf("IN operator requires array value")
		}
		placeholders := make([]string, len(values))
		for i := range values {
			placeholders[i] = "$" + strconv.Itoa(argIndex+i)
		}
		sql := fmt.Sprintf("%s IN (%s)", field, strings.Join(placeholders, ", "))
		return sql, values, nil
	case "NOT_IN":
		values, ok := condition.Value.([]interface{})
		if !ok {
			return "", nil, fmt.Errorf("NOT_IN operator requires array value")
		}
		placeholders := make([]string, len(values))
		for i := range values {
			placeholders[i] = "$" + strconv.Itoa(argIndex+i)
		}
		sql := fmt.Sprintf("%s NOT IN (%s)", field, strings.Join(placeholders, ", "))
		return sql, values, nil
	default:
		// 标准操作符 =, !=, >, <, >=, <=, LIKE
		placeholder := "$" + strconv.Itoa(argIndex)
		sql := fmt.Sprintf("%s %s %s", field, operator, placeholder)
		return sql, []interface{}{condition.Value}, nil
	}
}

func (b *SQLBuilder) buildGroupByClause(fields []string) (string, error) {
	var mappedFields []string
	for _, field := range fields {
		mappedField := b.mapField(field)
		mappedFields = append(mappedFields, mappedField)
	}
	return strings.Join(mappedFields, ", "), nil
}

func (b *SQLBuilder) buildOrderByClause(orderBy []model.OrderClause) (string, error) {
	var orderParts []string
	for _, order := range orderBy {
		direction := "ASC"
		if order.Direction != "" {
			direction = strings.ToUpper(order.Direction)
		}
		mappedField := b.mapField(order.Field)
		orderParts = append(orderParts, fmt.Sprintf("%s %s", mappedField, direction))
	}
	return strings.Join(orderParts, ", "), nil
}

// 字段映射 - 将前端字段名映射到实际数据库字段
func (b *SQLBuilder) mapField(field string) string {
	if mapped, exists := b.fieldMaps[field]; exists {
		return mapped
	}
	return field // 如果没有映射，返回原字段名
}

// 表映射 - 将逻辑表名映射到实际数据库表
func (b *SQLBuilder) mapTable(table string) string {
	if mapped, exists := b.tableMaps[table]; exists {
		return mapped
	}
	return table // 如果没有映射，返回原表名
}

// 获取数据集默认主表
func (b *SQLBuilder) getDefaultTable() string {
	switch b.dataset {
	case "mimic_iv":
		return "patients"
	case "eicu":
		return "patient"
	case "nhanes":
		return "demographics"
	default:
		return "main_table"
	}
}
