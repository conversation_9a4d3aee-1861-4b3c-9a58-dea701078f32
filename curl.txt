# 医疗数据平台API调试指南
# 问题：接口调试报错 401 - 缺少JWT认证

# ===== 解决方案 =====

# 方案1：通过前端界面登录（推荐）
# 1. 访问 http://localhost:3000/login
# 2. 注册新账户或登录现有账户
# 3. 登录成功后，JWT token会自动保存到localStorage
# 4. 然后访问 http://localhost:3000/query-builder 使用查询构建器

# 方案2：通过API直接获取token
echo "=== 步骤1：注册测试用户 ==="
curl 'http://localhost:8088/api/auth/register' \
  -H 'Content-Type: application/json' \
  --data-raw '{
    "name": "测试用户",
    "email": "<EMAIL>",
    "password": "test123456",
    "role": "researcher"
  }'

echo -e "\n=== 步骤2：登录获取JWT token ==="
LOGIN_RESPONSE=$(curl -s 'http://localhost:8088/api/auth/login' \
  -H 'Content-Type: application/json' \
  --data-raw '{
    "email": "<EMAIL>",
    "password": "test123456"
  }')

echo "登录响应: $LOGIN_RESPONSE"

# 提取token（需要jq工具）
# TOKEN=$(echo $LOGIN_RESPONSE | jq -r '.token')
# 或者手动从响应中复制token

echo -e "\n=== 步骤3：使用JWT token执行医疗查询 ==="
echo "请将下面的 YOUR_JWT_TOKEN_HERE 替换为实际的token："

curl 'http://localhost:8088/api/medical-query/execute' \
  -H 'Accept: */*' \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN_HERE' \
  --data-raw '{
    "dataset_id":"mimic-iv",
    "study_name":"测试查询_'$(date +%s)'",
    "description":"通过curl创建的测试查询",
    "cohort":{
      "criteria":[
        {
          "id":"'$(date +%s)'",
          "type":"demographic",
          "field":"age",
          "operator":"between",
          "value":["18","65"],
          "label":"年龄 18-65 岁"
        }
      ],
      "estimatedSize":null
    },
    "fields":[
      {
        "field":{
          "id":"heart_rate",
          "name":"心率",
          "nameEn":"heart_rate",
          "type":"numeric",
          "description":"患者心率",
          "table":"chartevents",
          "category":"clinical_measurements",
          "unit":"bpm"
        }
      }
    ],
    "filters":[],
    "time_range":null,
    "group_by":[],
    "order_by":[],
    "limit":100
  }'

# 方案3：使用测试页面
echo -e "\n=== 方案3：使用测试页面 ==="
echo "访问 http://localhost:3000/test-auth 进行完整的认证和API测试"

# ===== 故障排除 =====
echo -e "\n=== 故障排除 ==="
echo "1. 确保后端服务运行在 http://localhost:8088"
echo "2. 确保前端服务运行在 http://localhost:3000"
echo "3. 检查数据库连接是否正常"
echo "4. 如果注册失败，可能用户已存在，直接尝试登录"
echo "5. JWT token有效期为24小时，过期需要重新登录"