package repository

import (
	"context"
	"database/sql"

	"github.com/chungzy/medical-data-platform/internal/model"

	"github.com/google/uuid"
)

type queryRepository struct {
	db *sql.DB
}

func NewQueryRepository(db *sql.DB) QueryRepository {
	return &queryRepository{db: db}
}

func (r *queryRepository) ExecuteQuery(ctx context.Context, sql string, args []interface{}) ([]map[string]interface{}, error) {
	rows, err := r.db.QueryContext(ctx, sql, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	// 获取列信息
	columns, err := rows.Columns()
	if err != nil {
		return nil, err
	}

	// 读取数据
	var results []map[string]interface{}
	for rows.Next() {
		values := make([]interface{}, len(columns))
		valuePtrs := make([]interface{}, len(columns))
		for i := range values {
			valuePtrs[i] = &values[i]
		}

		if err := rows.Scan(valuePtrs...); err != nil {
			return nil, err
		}

		row := make(map[string]interface{})
		for i, col := range columns {
			val := values[i]
			if b, ok := val.([]byte); ok {
				row[col] = string(b)
			} else {
				row[col] = val
			}
		}
		results = append(results, row)
	}

	return results, rows.Err()
}

func (r *queryRepository) ExecuteQueryWithCount(ctx context.Context, sql string, args []interface{}) ([]map[string]interface{}, int, error) {
	// 执行查询获取数据
	results, err := r.ExecuteQuery(ctx, sql, args)
	if err != nil {
		return nil, 0, err
	}

	// 简化处理，返回结果数量作为总数
	// 实际应该执行COUNT查询
	total := len(results)
	return results, total, nil
}

func (r *queryRepository) SaveQueryHistory(ctx context.Context, history *model.QueryHistory) error {
	query := `
		INSERT INTO query_history (id, user_id, name, dataset_id, query_config, status, record_count, execution_time, created_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
	`

	_, err := r.db.ExecContext(ctx, query,
		history.ID, history.UserID, history.Name, history.DatasetID,
		history.QueryConfig, history.Status, history.RecordCount,
		history.ExecutionTime, history.CreatedAt,
	)
	return err
}

func (r *queryRepository) GetQueryHistory(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.QueryHistory, int, error) {
	query := `
		SELECT id, user_id, name, dataset_id, query_config, status, record_count, execution_time, created_at
		FROM query_history WHERE user_id = $1
		ORDER BY created_at DESC
		LIMIT $2 OFFSET $3
	`

	rows, err := r.db.QueryContext(ctx, query, userID, limit, offset)
	if err != nil {
		return nil, 0, err
	}
	defer rows.Close()

	var histories []model.QueryHistory
	for rows.Next() {
		var history model.QueryHistory

		err := rows.Scan(
			&history.ID, &history.UserID, &history.Name, &history.DatasetID,
			&history.QueryConfig, &history.Status, &history.RecordCount,
			&history.ExecutionTime, &history.CreatedAt,
		)
		if err != nil {
			return nil, 0, err
		}

		histories = append(histories, history)
	}

	// 获取总数
	countQuery := `SELECT COUNT(*) FROM query_history WHERE user_id = $1`
	var total int
	err = r.db.QueryRowContext(ctx, countQuery, userID).Scan(&total)
	if err != nil {
		return nil, 0, err
	}

	return histories, total, nil
}

func (r *queryRepository) GetQueryHistoryByID(ctx context.Context, id uuid.UUID) (*model.QueryHistory, error) {
	query := `
		SELECT id, user_id, name, dataset_id, query_config, status, record_count, execution_time, created_at
		FROM query_history WHERE id = $1
	`

	history := &model.QueryHistory{}

	err := r.db.QueryRowContext(ctx, query, id).Scan(
		&history.ID, &history.UserID, &history.Name, &history.DatasetID,
		&history.QueryConfig, &history.Status, &history.RecordCount,
		&history.ExecutionTime, &history.CreatedAt,
	)
	if err != nil {
		return nil, err
	}

	return history, nil
}

func (r *queryRepository) DeleteQueryHistory(ctx context.Context, id uuid.UUID) error {
	query := `DELETE FROM query_history WHERE id = $1`
	_, err := r.db.ExecContext(ctx, query, id)
	return err
}
