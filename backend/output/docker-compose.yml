version: '3.8'

services:
  # 医疗数据平台后端服务
  medical-platform:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: medical-platform-dev
    ports:
      - "8080:8080"
    environment:
      - GIN_MODE=debug
      - LOG_LEVEL=debug
      - LOG_DIR=/app/logs
      - DATABASE_HOST=postgres
      - DATABASE_PORT=5432
      - DATABASE_USER=medical_user
      - DATABASE_PASSWORD=medical_password_2024
      - DATABASE_NAME=medical_data_platform
      - DATABASE_SSLMODE=disable
    volumes:
      - ./logs:/app/logs
      - ./exports:/app/exports
      - ./configs:/app/configs
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - medical-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: medical-postgres-dev
    environment:
      - POSTGRES_USER=medical_user
      - POSTGRES_PASSWORD=medical_password_2024
      - POSTGRES_DB=medical_data_platform
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./migrations:/docker-entrypoint-initdb.d
    networks:
      - medical-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U medical_user -d medical_data_platform"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis缓存（可选）
  redis:
    image: redis:7-alpine
    container_name: medical-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - medical-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Nginx反向代理（可选）
  nginx:
    image: nginx:alpine
    container_name: medical-nginx-dev
    ports:
      - "80:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - medical-platform
    networks:
      - medical-network
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  medical-network:
    driver: bridge 