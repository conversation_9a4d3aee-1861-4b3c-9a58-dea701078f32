"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { 
  Database, 
  Search, 
  CheckCircle, 
  AlertCircle, 
  XCircle,
  Users,
  Calendar,
  BarChart3,
  Layers,
  Zap,
  Shield
} from "lucide-react"
import { formatDate } from "@/lib/utils"

interface Dataset {
  id: string
  name: string
  description: string
  version: string
  tableCount: number
  features: string[]
  status: "healthy" | "warning" | "error"
  patientCount?: number
  lastUpdated?: string
  accessLevel?: "public" | "restricted" | "private"
}

interface DatasetSelectorProps {
  datasets: Dataset[]
  selectedDataset: Dataset | null
  onDatasetSelect: (dataset: Dataset) => void
}

export function DatasetSelector({ datasets, selectedDataset, onDatasetSelect }: DatasetSelectorProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState<string>("all")

  // 过滤数据集
  const filteredDatasets = datasets.filter(dataset => {
    const matchesSearch = dataset.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         dataset.description.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesCategory = selectedCategory === "all" || 
                           dataset.features.includes(selectedCategory)
    
    return matchesSearch && matchesCategory
  })

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "healthy":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case "warning":
        return <AlertCircle className="h-4 w-4 text-yellow-500" />
      case "error":
        return <XCircle className="h-4 w-4 text-red-500" />
      default:
        return <Database className="h-4 w-4 text-gray-500" />
    }
  }

  // 获取访问级别颜色
  const getAccessLevelColor = (level?: string) => {
    switch (level) {
      case "public":
        return "bg-green-100 text-green-800"
      case "restricted":
        return "bg-yellow-100 text-yellow-800"
      case "private":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  // 特性图标映射
  const featureIcons: Record<string, any> = {
    "time_range": Calendar,
    "aggregation": BarChart3,
    "joins": Layers,
    "complex_queries": Zap
  }

  const categories = [
    { id: "all", label: "全部数据集" },
    { id: "time_range", label: "时间序列" },
    { id: "aggregation", label: "聚合分析" },
    { id: "joins", label: "多表关联" },
    { id: "complex_queries", label: "复杂查询" }
  ]

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900">选择数据集</h2>
        <p className="text-gray-600 mt-1">
          选择一个医学数据集开始构建您的查询。每个数据集都有不同的特性和能力。
        </p>
      </div>

      {/* 搜索和过滤 */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="搜索数据集..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <div className="flex gap-2 flex-wrap">
          {categories.map((category) => (
            <Button
              key={category.id}
              variant={selectedCategory === category.id ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory(category.id)}
              className="whitespace-nowrap"
            >
              {category.label}
            </Button>
          ))}
        </div>
      </div>

      {/* 数据集网格 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredDatasets.map((dataset) => (
          <Card 
            key={dataset.id}
            className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
              selectedDataset?.id === dataset.id 
                ? "ring-2 ring-blue-500 shadow-lg" 
                : "hover:shadow-md"
            }`}
            onClick={() => onDatasetSelect(dataset)}
          >
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-2">
                  <Database className="h-5 w-5 text-blue-600" />
                  <CardTitle className="text-lg">{dataset.name}</CardTitle>
                </div>
                {getStatusIcon(dataset.status)}
              </div>
              
              <CardDescription className="text-sm line-clamp-2">
                {dataset.description}
              </CardDescription>
            </CardHeader>

            <CardContent className="space-y-4">
              {/* 基本信息 */}
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <Layers className="h-4 w-4 text-gray-500" />
                  <span className="text-gray-600">{dataset.tableCount} 张表</span>
                </div>
                
                {dataset.patientCount && (
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-gray-500" />
                    <span className="text-gray-600">
                      {dataset.patientCount.toLocaleString()} 患者
                    </span>
                  </div>
                )}
              </div>

              {/* 版本和访问级别 */}
              <div className="flex items-center justify-between">
                <Badge variant="outline" className="text-xs">
                  v{dataset.version}
                </Badge>
                
                {dataset.accessLevel && (
                  <Badge className={`text-xs ${getAccessLevelColor(dataset.accessLevel)}`}>
                    <Shield className="h-3 w-3 mr-1" />
                    {dataset.accessLevel === "public" ? "公开" : 
                     dataset.accessLevel === "restricted" ? "受限" : "私有"}
                  </Badge>
                )}
              </div>

              {/* 特性标签 */}
              <div className="flex flex-wrap gap-1">
                {dataset.features.slice(0, 4).map((feature) => {
                  const Icon = featureIcons[feature]
                  return (
                    <Badge 
                      key={feature} 
                      variant="secondary" 
                      className="text-xs flex items-center gap-1"
                    >
                      {Icon && <Icon className="h-3 w-3" />}
                      {feature === "time_range" ? "时间序列" :
                       feature === "aggregation" ? "聚合" :
                       feature === "joins" ? "关联" :
                       feature === "complex_queries" ? "复杂查询" : feature}
                    </Badge>
                  )
                })}
                {dataset.features.length > 4 && (
                  <Badge variant="secondary" className="text-xs">
                    +{dataset.features.length - 4}
                  </Badge>
                )}
              </div>

              {/* 最后更新时间 */}
              {dataset.lastUpdated && (
                <div className="text-xs text-gray-500 flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  更新于 {formatDate(dataset.lastUpdated)}
                </div>
              )}

              {/* 选择指示器 */}
              {selectedDataset?.id === dataset.id && (
                <div className="flex items-center gap-2 text-blue-600 font-medium text-sm">
                  <CheckCircle className="h-4 w-4" />
                  已选择
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* 空状态 */}
      {filteredDatasets.length === 0 && (
        <div className="text-center py-12">
          <Database className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">未找到数据集</h3>
          <p className="text-gray-600">
            尝试调整搜索条件或选择不同的分类
          </p>
        </div>
      )}

      {/* 选择提示 */}
      {selectedDataset && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <CheckCircle className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-900">
                已选择 {selectedDataset.name}
              </h4>
              <p className="text-blue-700 text-sm mt-1">
                您可以继续下一步定义研究人群，或重新选择其他数据集。
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
