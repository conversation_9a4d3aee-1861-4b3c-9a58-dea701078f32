package repository

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/chungzy/medical-data-platform/internal/model"
	"github.com/chungzy/medical-data-platform/pkg/logger"
	"github.com/google/uuid"
)

type MedicalQueryRepository interface {
	// 查询历史相关
	SaveQueryHistory(ctx context.Context, history *model.MedicalQueryHistory) error
	UpdateQueryHistory(ctx context.Context, history *model.MedicalQueryHistory) error
	GetQueryHistory(ctx context.Context, userID uuid.UUID, page, limit int) ([]model.MedicalQueryHistory, int, error)
	GetQueryHistoryByID(ctx context.Context, queryID uuid.UUID) (*model.MedicalQueryHistory, error)
	DeleteQueryHistory(ctx context.Context, queryID uuid.UUID) error

	// 查询模板相关
	SaveQueryTemplate(ctx context.Context, template *model.MedicalQueryTemplate) error
	UpdateQueryTemplate(ctx context.Context, template *model.MedicalQueryTemplate) error
	GetQueryTemplates(ctx context.Context, page, limit int, category string) ([]model.MedicalQueryTemplate, int, error)
	GetQueryTemplateByID(ctx context.Context, templateID uuid.UUID) (*model.MedicalQueryTemplate, error)
	DeleteQueryTemplate(ctx context.Context, templateID uuid.UUID) error
	IncrementTemplateUsage(ctx context.Context, templateID uuid.UUID) error

	// 医学字段分类相关
	GetMedicalCategories(ctx context.Context) ([]model.MedicalFieldCategory, error)
	GetMedicalFieldsByCategory(ctx context.Context, category string) ([]model.MedicalField, error)

	// 统计相关
	GetQueryStats(ctx context.Context, userID uuid.UUID) (*model.MedicalQueryStats, error)
	GetPopularTemplates(ctx context.Context, limit int) ([]model.MedicalQueryTemplate, error)
}

type medicalQueryRepository struct {
	db *sql.DB
}

func NewMedicalQueryRepository(db *sql.DB) MedicalQueryRepository {
	return &medicalQueryRepository{
		db: db,
	}
}

// SaveQueryHistory 保存查询历史
func (mqr *medicalQueryRepository) SaveQueryHistory(ctx context.Context, history *model.MedicalQueryHistory) error {
	query := `
		INSERT INTO medical_query_history (
			id, user_id, study_name, query_config, status, 
			record_count, execution_time, error_message, created_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
	`

	_, err := mqr.db.ExecContext(ctx, query,
		history.ID,
		history.UserID,
		history.StudyName,
		history.QueryConfig,
		history.Status,
		history.RecordCount,
		history.ExecutionTime,
		history.ErrorMessage,
		history.CreatedAt,
	)

	if err != nil {
		logger.Error("Failed to save medical query history", err, map[string]interface{}{
			"query_id": history.ID,
			"user_id":  history.UserID,
		})
		return fmt.Errorf("failed to save query history: %w", err)
	}

	return nil
}

// UpdateQueryHistory 更新查询历史
func (mqr *medicalQueryRepository) UpdateQueryHistory(ctx context.Context, history *model.MedicalQueryHistory) error {
	query := `
		UPDATE medical_query_history 
		SET status = $2, record_count = $3, execution_time = $4, error_message = $5
		WHERE id = $1
	`

	_, err := mqr.db.ExecContext(ctx, query,
		history.ID,
		history.Status,
		history.RecordCount,
		history.ExecutionTime,
		history.ErrorMessage,
	)

	if err != nil {
		logger.Error("Failed to update medical query history", err, map[string]interface{}{
			"query_id": history.ID,
		})
		return fmt.Errorf("failed to update query history: %w", err)
	}

	return nil
}

// GetQueryHistory 获取查询历史
func (mqr *medicalQueryRepository) GetQueryHistory(ctx context.Context, userID uuid.UUID, page, limit int) ([]model.MedicalQueryHistory, int, error) {
	offset := (page - 1) * limit

	// 获取总数
	countQuery := `SELECT COUNT(*) FROM medical_query_history WHERE user_id = $1`
	var total int
	err := mqr.db.QueryRowContext(ctx, countQuery, userID).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count query history: %w", err)
	}

	// 获取数据
	query := `
		SELECT id, user_id, study_name, query_config, status, 
			   record_count, execution_time, error_message, created_at
		FROM medical_query_history 
		WHERE user_id = $1 
		ORDER BY created_at DESC 
		LIMIT $2 OFFSET $3
	`

	rows, err := mqr.db.QueryContext(ctx, query, userID, limit, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to query history: %w", err)
	}
	defer rows.Close()

	var histories []model.MedicalQueryHistory
	for rows.Next() {
		var history model.MedicalQueryHistory
		err := rows.Scan(
			&history.ID,
			&history.UserID,
			&history.StudyName,
			&history.QueryConfig,
			&history.Status,
			&history.RecordCount,
			&history.ExecutionTime,
			&history.ErrorMessage,
			&history.CreatedAt,
		)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to scan query history: %w", err)
		}
		histories = append(histories, history)
	}

	return histories, total, nil
}

// GetQueryHistoryByID 根据ID获取查询历史
func (mqr *medicalQueryRepository) GetQueryHistoryByID(ctx context.Context, queryID uuid.UUID) (*model.MedicalQueryHistory, error) {
	query := `
		SELECT id, user_id, study_name, query_config, status, 
			   record_count, execution_time, error_message, created_at
		FROM medical_query_history 
		WHERE id = $1
	`

	var history model.MedicalQueryHistory
	err := mqr.db.QueryRowContext(ctx, query, queryID).Scan(
		&history.ID,
		&history.UserID,
		&history.StudyName,
		&history.QueryConfig,
		&history.Status,
		&history.RecordCount,
		&history.ExecutionTime,
		&history.ErrorMessage,
		&history.CreatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("query history not found")
		}
		return nil, fmt.Errorf("failed to get query history: %w", err)
	}

	return &history, nil
}

// DeleteQueryHistory 删除查询历史
func (mqr *medicalQueryRepository) DeleteQueryHistory(ctx context.Context, queryID uuid.UUID) error {
	query := `DELETE FROM medical_query_history WHERE id = $1`

	_, err := mqr.db.ExecContext(ctx, query, queryID)
	if err != nil {
		return fmt.Errorf("failed to delete query history: %w", err)
	}

	return nil
}

// SaveQueryTemplate 保存查询模板
func (mqr *medicalQueryRepository) SaveQueryTemplate(ctx context.Context, template *model.MedicalQueryTemplate) error {
	query := `
		INSERT INTO medical_query_templates (
			id, name, description, category, query_config, 
			created_by, created_at, updated_at, is_public, usage_count
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
	`

	_, err := mqr.db.ExecContext(ctx, query,
		template.ID,
		template.Name,
		template.Description,
		template.Category,
		template.QueryConfig,
		template.CreatedBy,
		template.CreatedAt,
		template.UpdatedAt,
		template.IsPublic,
		template.UsageCount,
	)

	if err != nil {
		logger.Error("Failed to save medical query template", err, map[string]interface{}{
			"template_id": template.ID,
			"name":        template.Name,
		})
		return fmt.Errorf("failed to save query template: %w", err)
	}

	return nil
}

// UpdateQueryTemplate 更新查询模板
func (mqr *medicalQueryRepository) UpdateQueryTemplate(ctx context.Context, template *model.MedicalQueryTemplate) error {
	query := `
		UPDATE medical_query_templates 
		SET name = $2, description = $3, category = $4, query_config = $5, 
			updated_at = $6, is_public = $7
		WHERE id = $1
	`

	_, err := mqr.db.ExecContext(ctx, query,
		template.ID,
		template.Name,
		template.Description,
		template.Category,
		template.QueryConfig,
		template.UpdatedAt,
		template.IsPublic,
	)

	if err != nil {
		return fmt.Errorf("failed to update query template: %w", err)
	}

	return nil
}

// GetQueryTemplates 获取查询模板
func (mqr *medicalQueryRepository) GetQueryTemplates(ctx context.Context, page, limit int, category string) ([]model.MedicalQueryTemplate, int, error) {
	offset := (page - 1) * limit

	// 构建查询条件
	whereClause := "WHERE is_public = true"
	args := []interface{}{limit, offset}
	argIndex := 3

	if category != "" {
		whereClause += fmt.Sprintf(" AND category = $%d", argIndex)
		args = append(args, category)
		argIndex++
	}

	// 获取总数
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM medical_query_templates %s", whereClause)
	var total int
	countArgs := args[2:] // 去掉 limit 和 offset
	err := mqr.db.QueryRowContext(ctx, countQuery, countArgs...).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count query templates: %w", err)
	}

	// 获取数据
	query := fmt.Sprintf(`
		SELECT id, name, description, category, query_config, 
			   created_by, created_at, updated_at, is_public, usage_count
		FROM medical_query_templates 
		%s 
		ORDER BY usage_count DESC, created_at DESC 
		LIMIT $1 OFFSET $2
	`, whereClause)

	rows, err := mqr.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to query templates: %w", err)
	}
	defer rows.Close()

	var templates []model.MedicalQueryTemplate
	for rows.Next() {
		var template model.MedicalQueryTemplate
		err := rows.Scan(
			&template.ID,
			&template.Name,
			&template.Description,
			&template.Category,
			&template.QueryConfig,
			&template.CreatedBy,
			&template.CreatedAt,
			&template.UpdatedAt,
			&template.IsPublic,
			&template.UsageCount,
		)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to scan query template: %w", err)
		}
		templates = append(templates, template)
	}

	return templates, total, nil
}

// GetQueryTemplateByID 根据ID获取查询模板
func (mqr *medicalQueryRepository) GetQueryTemplateByID(ctx context.Context, templateID uuid.UUID) (*model.MedicalQueryTemplate, error) {
	query := `
		SELECT id, name, description, category, query_config,
			   created_by, created_at, updated_at, is_public, usage_count
		FROM medical_query_templates
		WHERE id = $1
	`

	var template model.MedicalQueryTemplate
	err := mqr.db.QueryRowContext(ctx, query, templateID).Scan(
		&template.ID,
		&template.Name,
		&template.Description,
		&template.Category,
		&template.QueryConfig,
		&template.CreatedBy,
		&template.CreatedAt,
		&template.UpdatedAt,
		&template.IsPublic,
		&template.UsageCount,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("query template not found")
		}
		return nil, fmt.Errorf("failed to get query template: %w", err)
	}

	return &template, nil
}

// DeleteQueryTemplate 删除查询模板
func (mqr *medicalQueryRepository) DeleteQueryTemplate(ctx context.Context, templateID uuid.UUID) error {
	query := `DELETE FROM medical_query_templates WHERE id = $1`

	_, err := mqr.db.ExecContext(ctx, query, templateID)
	if err != nil {
		return fmt.Errorf("failed to delete query template: %w", err)
	}

	return nil
}

// IncrementTemplateUsage 增加模板使用次数
func (mqr *medicalQueryRepository) IncrementTemplateUsage(ctx context.Context, templateID uuid.UUID) error {
	query := `UPDATE medical_query_templates SET usage_count = usage_count + 1 WHERE id = $1`

	_, err := mqr.db.ExecContext(ctx, query, templateID)
	if err != nil {
		return fmt.Errorf("failed to increment template usage: %w", err)
	}

	return nil
}

// GetMedicalCategories 获取医学字段分类
func (mqr *medicalQueryRepository) GetMedicalCategories(ctx context.Context) ([]model.MedicalFieldCategory, error) {
	// 这里可以从数据库获取，暂时返回空，让服务层使用预定义的分类
	return []model.MedicalFieldCategory{}, nil
}

// GetMedicalFieldsByCategory 根据分类获取医学字段
func (mqr *medicalQueryRepository) GetMedicalFieldsByCategory(ctx context.Context, category string) ([]model.MedicalField, error) {
	query := `
		SELECT id, name, name_en, data_type, description, category, unit
		FROM data_fields
		WHERE category = $1 AND dataset_id = 'mimic_iv'
		ORDER BY name
	`

	rows, err := mqr.db.QueryContext(ctx, query, category)
	if err != nil {
		return nil, fmt.Errorf("failed to query medical fields: %w", err)
	}
	defer rows.Close()

	var fields []model.MedicalField
	for rows.Next() {
		var field model.MedicalField
		var unit sql.NullString

		err := rows.Scan(
			&field.ID,
			&field.Name,
			&field.NameEn,
			&field.Type,
			&field.Description,
			&field.Category,
			&unit,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan medical field: %w", err)
		}

		if unit.Valid {
			field.Unit = &unit.String
		}

		fields = append(fields, field)
	}

	return fields, nil
}

// GetQueryStats 获取查询统计
func (mqr *medicalQueryRepository) GetQueryStats(ctx context.Context, userID uuid.UUID) (*model.MedicalQueryStats, error) {
	// 获取总查询数
	var totalQueries, successfulQueries, failedQueries int
	var avgExecutionTime float64

	statsQuery := `
		SELECT
			COUNT(*) as total,
			COUNT(CASE WHEN status = 'completed' THEN 1 END) as successful,
			COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed,
			COALESCE(AVG(CASE WHEN execution_time IS NOT NULL THEN execution_time END), 0) as avg_time
		FROM medical_query_history
		WHERE user_id = $1
	`

	err := mqr.db.QueryRowContext(ctx, statsQuery, userID).Scan(
		&totalQueries, &successfulQueries, &failedQueries, &avgExecutionTime,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get query stats: %w", err)
	}

	// 获取最近查询
	recentQuery := `
		SELECT id, user_id, study_name, query_config, status,
			   record_count, execution_time, error_message, created_at
		FROM medical_query_history
		WHERE user_id = $1
		ORDER BY created_at DESC
		LIMIT 5
	`

	rows, err := mqr.db.QueryContext(ctx, recentQuery, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get recent queries: %w", err)
	}
	defer rows.Close()

	var recentQueries []model.MedicalQueryHistory
	for rows.Next() {
		var query model.MedicalQueryHistory
		err := rows.Scan(
			&query.ID,
			&query.UserID,
			&query.StudyName,
			&query.QueryConfig,
			&query.Status,
			&query.RecordCount,
			&query.ExecutionTime,
			&query.ErrorMessage,
			&query.CreatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan recent query: %w", err)
		}
		recentQueries = append(recentQueries, query)
	}

	stats := &model.MedicalQueryStats{
		TotalQueries:      totalQueries,
		SuccessfulQueries: successfulQueries,
		FailedQueries:     failedQueries,
		AvgExecutionTime:  avgExecutionTime,
		PopularCategories: []model.CategoryUsageStats{}, // 可以后续实现
		RecentQueries:     recentQueries,
	}

	return stats, nil
}

// GetPopularTemplates 获取热门模板
func (mqr *medicalQueryRepository) GetPopularTemplates(ctx context.Context, limit int) ([]model.MedicalQueryTemplate, error) {
	query := `
		SELECT id, name, description, category, query_config,
			   created_by, created_at, updated_at, is_public, usage_count
		FROM medical_query_templates
		WHERE is_public = true
		ORDER BY usage_count DESC, created_at DESC
		LIMIT $1
	`

	rows, err := mqr.db.QueryContext(ctx, query, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to query popular templates: %w", err)
	}
	defer rows.Close()

	var templates []model.MedicalQueryTemplate
	for rows.Next() {
		var template model.MedicalQueryTemplate
		err := rows.Scan(
			&template.ID,
			&template.Name,
			&template.Description,
			&template.Category,
			&template.QueryConfig,
			&template.CreatedBy,
			&template.CreatedAt,
			&template.UpdatedAt,
			&template.IsPublic,
			&template.UsageCount,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan popular template: %w", err)
		}
		templates = append(templates, template)
	}

	return templates, nil
}
