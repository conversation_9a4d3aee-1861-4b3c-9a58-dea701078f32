"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  Users, 
  Calendar, 
  Filter, 
  Plus, 
  X,
  Target,
  Clock,
  Activity,
  Heart,
  Brain,
  Stethoscope
} from "lucide-react"
import { formatNumber } from "@/lib/utils"

interface Dataset {
  id: string
  name: string
  description: string
  version: string
  tableCount: number
  features: string[]
  status: "healthy" | "warning" | "error"
}

interface CohortCriteria {
  id: string
  type: "demographic" | "temporal" | "clinical" | "diagnosis"
  field: string
  operator: string
  value: any
  label: string
}

interface CohortBuilderProps {
  dataset: Dataset | null
  cohort: any
  onCohortChange: (cohort: any) => void
}

export function CohortBuilder({ dataset, cohort, onCohortChange }: CohortBuilderProps) {
  const [activeTab, setActiveTab] = useState("demographic")
  const [criteria, setCriteria] = useState<CohortCriteria[]>([])
  const [estimatedSize, setEstimatedSize] = useState<number | null>(null)

  if (!dataset) {
    return (
      <div className="text-center py-12">
        <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">请先选择数据集</h3>
        <p className="text-gray-600">
          选择数据集后才能定义研究人群
        </p>
      </div>
    )
  }

  // 添加条件
  const addCriteria = (type: string, field: string, operator: string, value: any, label: string) => {
    const newCriteria: CohortCriteria = {
      id: Date.now().toString(),
      type: type as any,
      field,
      operator,
      value,
      label
    }
    
    const updatedCriteria = [...criteria, newCriteria]
    setCriteria(updatedCriteria)
    
    // 更新人群定义
    onCohortChange({
      criteria: updatedCriteria,
      estimatedSize
    })
  }

  // 删除条件
  const removeCriteria = (id: string) => {
    const updatedCriteria = criteria.filter(c => c.id !== id)
    setCriteria(updatedCriteria)
    onCohortChange({
      criteria: updatedCriteria,
      estimatedSize
    })
  }

  // 人口统计学条件
  const DemographicCriteria = () => {
    const [ageMin, setAgeMin] = useState("")
    const [ageMax, setAgeMax] = useState("")
    const [gender, setGender] = useState("")

    const addAgeCriteria = () => {
      if (ageMin || ageMax) {
        const label = `年龄 ${ageMin || '0'}-${ageMax || '∞'} 岁`
        addCriteria("demographic", "age", "between", [ageMin, ageMax], label)
        setAgeMin("")
        setAgeMax("")
      }
    }

    const addGenderCriteria = () => {
      if (gender) {
        const label = `性别: ${gender === 'M' ? '男性' : '女性'}`
        addCriteria("demographic", "gender", "equals", gender, label)
        setGender("")
      }
    }

    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              年龄范围
            </CardTitle>
            <CardDescription>
              设置研究人群的年龄范围
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="age-min">最小年龄</Label>
                <Input
                  id="age-min"
                  type="number"
                  placeholder="0"
                  value={ageMin}
                  onChange={(e) => setAgeMin(e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="age-max">最大年龄</Label>
                <Input
                  id="age-max"
                  type="number"
                  placeholder="100"
                  value={ageMax}
                  onChange={(e) => setAgeMax(e.target.value)}
                />
              </div>
            </div>
            <Button onClick={addAgeCriteria} className="w-full">
              <Plus className="h-4 w-4 mr-2" />
              添加年龄条件
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              性别
            </CardTitle>
            <CardDescription>
              选择研究人群的性别
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Select value={gender} onValueChange={setGender}>
              <SelectTrigger>
                <SelectValue placeholder="选择性别" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="M">男性</SelectItem>
                <SelectItem value="F">女性</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={addGenderCriteria} className="w-full" disabled={!gender}>
              <Plus className="h-4 w-4 mr-2" />
              添加性别条件
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  // 时间条件
  const TemporalCriteria = () => {
    const [admissionStart, setAdmissionStart] = useState("")
    const [admissionEnd, setAdmissionEnd] = useState("")
    const [stayDuration, setStayDuration] = useState("")

    const addAdmissionDateCriteria = () => {
      if (admissionStart || admissionEnd) {
        const label = `入院时间: ${admissionStart || '开始'} 至 ${admissionEnd || '结束'}`
        addCriteria("temporal", "admission_date", "between", [admissionStart, admissionEnd], label)
        setAdmissionStart("")
        setAdmissionEnd("")
      }
    }

    const addStayDurationCriteria = () => {
      if (stayDuration) {
        const label = `住院时长 ≥ ${stayDuration} 天`
        addCriteria("temporal", "stay_duration", "gte", stayDuration, label)
        setStayDuration("")
      }
    }

    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              入院时间范围
            </CardTitle>
            <CardDescription>
              设置研究时间窗口
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="admission-start">开始日期</Label>
                <Input
                  id="admission-start"
                  type="date"
                  value={admissionStart}
                  onChange={(e) => setAdmissionStart(e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="admission-end">结束日期</Label>
                <Input
                  id="admission-end"
                  type="date"
                  value={admissionEnd}
                  onChange={(e) => setAdmissionEnd(e.target.value)}
                />
              </div>
            </div>
            <Button onClick={addAdmissionDateCriteria} className="w-full">
              <Plus className="h-4 w-4 mr-2" />
              添加时间条件
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              住院时长
            </CardTitle>
            <CardDescription>
              设置最小住院天数
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="stay-duration">最小住院天数</Label>
              <Input
                id="stay-duration"
                type="number"
                placeholder="1"
                value={stayDuration}
                onChange={(e) => setStayDuration(e.target.value)}
              />
            </div>
            <Button onClick={addStayDurationCriteria} className="w-full" disabled={!stayDuration}>
              <Plus className="h-4 w-4 mr-2" />
              添加时长条件
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  // 临床条件
  const ClinicalCriteria = () => {
    const [vitalSign, setVitalSign] = useState("")
    const [vitalOperator, setVitalOperator] = useState("")
    const [vitalValue, setVitalValue] = useState("")

    const addVitalSignCriteria = () => {
      if (vitalSign && vitalOperator && vitalValue) {
        const vitalLabels: Record<string, string> = {
          heart_rate: "心率",
          blood_pressure_systolic: "收缩压",
          blood_pressure_diastolic: "舒张压",
          temperature: "体温",
          respiratory_rate: "呼吸频率"
        }
        
        const operatorLabels: Record<string, string> = {
          gt: ">",
          gte: "≥",
          lt: "<",
          lte: "≤",
          eq: "="
        }

        const label = `${vitalLabels[vitalSign]} ${operatorLabels[vitalOperator]} ${vitalValue}`
        addCriteria("clinical", vitalSign, vitalOperator, vitalValue, label)
        setVitalSign("")
        setVitalOperator("")
        setVitalValue("")
      }
    }

    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              生命体征
            </CardTitle>
            <CardDescription>
              基于生命体征筛选患者
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label>生命体征</Label>
              <Select value={vitalSign} onValueChange={setVitalSign}>
                <SelectTrigger>
                  <SelectValue placeholder="选择生命体征" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="heart_rate">心率 (bpm)</SelectItem>
                  <SelectItem value="blood_pressure_systolic">收缩压 (mmHg)</SelectItem>
                  <SelectItem value="blood_pressure_diastolic">舒张压 (mmHg)</SelectItem>
                  <SelectItem value="temperature">体温 (°C)</SelectItem>
                  <SelectItem value="respiratory_rate">呼吸频率 (/min)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>条件</Label>
                <Select value={vitalOperator} onValueChange={setVitalOperator}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择条件" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="gt">大于 </SelectItem>
                    <SelectItem value="gte">大于等于 ≥</SelectItem>
                    <SelectItem value="lt">小于 </SelectItem>
                    <SelectItem value="lte">小于等于 ≤</SelectItem>
                    <SelectItem value="eq">等于 =</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>数值</Label>
                <Input
                  type="number"
                  placeholder="输入数值"
                  value={vitalValue}
                  onChange={(e) => setVitalValue(e.target.value)}
                />
              </div>
            </div>

            <Button 
              onClick={addVitalSignCriteria} 
              className="w-full"
              disabled={!vitalSign || !vitalOperator || !vitalValue}
            >
              <Plus className="h-4 w-4 mr-2" />
              添加生命体征条件
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900">定义研究人群</h2>
        <p className="text-gray-600 mt-1">
          为 {dataset.name} 设置人群筛选条件，构建您的研究队列
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 左侧：条件设置 */}
        <div className="lg:col-span-2">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="demographic" className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                人口统计学
              </TabsTrigger>
              <TabsTrigger value="temporal" className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                时间条件
              </TabsTrigger>
              <TabsTrigger value="clinical" className="flex items-center gap-2">
                <Activity className="h-4 w-4" />
                临床条件
              </TabsTrigger>
            </TabsList>

            <TabsContent value="demographic" className="mt-6">
              <DemographicCriteria />
            </TabsContent>

            <TabsContent value="temporal" className="mt-6">
              <TemporalCriteria />
            </TabsContent>

            <TabsContent value="clinical" className="mt-6">
              <ClinicalCriteria />
            </TabsContent>
          </Tabs>
        </div>

        {/* 右侧：已选条件和预估 */}
        <div className="space-y-6">
          {/* 已选条件 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                筛选条件
              </CardTitle>
              <CardDescription>
                当前已设置的人群筛选条件
              </CardDescription>
            </CardHeader>
            <CardContent>
              {criteria.length === 0 ? (
                <p className="text-gray-500 text-sm text-center py-4">
                  暂无筛选条件
                </p>
              ) : (
                <div className="space-y-2">
                  {criteria.map((criterion) => (
                    <div
                      key={criterion.id}
                      className="flex items-center justify-between p-2 bg-gray-50 rounded-lg"
                    >
                      <span className="text-sm">{criterion.label}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeCriteria(criterion.id)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* 人群预估 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                人群预估
              </CardTitle>
              <CardDescription>
                基于当前条件的人群规模预估
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-4">
                <div className="text-3xl font-bold text-blue-600">
                  {estimatedSize ? formatNumber(estimatedSize) : "~"}
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  预估患者数量
                </div>
                
                {criteria.length > 0 && (
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="mt-4"
                    onClick={() => setEstimatedSize(Math.floor(Math.random() * 10000 + 1000))}
                  >
                    重新估算
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
