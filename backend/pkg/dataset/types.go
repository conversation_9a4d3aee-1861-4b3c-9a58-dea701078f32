package dataset

import "fmt"

// DatasetType 数据集类型
type DatasetType string

const (
	DatasetTypeMIMICIV DatasetType = "mimic-iv"
	DatasetTypeEICU    DatasetType = "eicu"
	DatasetTypeNHANES  DatasetType = "nhanes"
	DatasetTypePIC     DatasetType = "pic"
)

// DatasetConfig 数据集配置
type DatasetConfig struct {
	Type          DatasetType     `json:"type"`
	Name          string          `json:"name"`
	Description   string          `json:"description"`
	Version       string          `json:"version"`
	Schemas       []string        `json:"schemas"`
	PrimaryTables []string        `json:"primary_tables"`
	TableCount    int             `json:"table_count"`
	Features      DatasetFeatures `json:"features"`
}

// DatasetFeatures 数据集特性
type DatasetFeatures struct {
	SupportsTimeRange   bool `json:"supports_time_range"`
	SupportsAggregation bool `json:"supports_aggregation"`
	SupportsJoins       bool `json:"supports_joins"`
	MaxQueryComplexity  int  `json:"max_query_complexity"`
}

// GetSupportedDatasets 获取支持的数据集列表
func GetSupportedDatasets() []DatasetType {
	return []DatasetType{
		DatasetTypeMIMICIV,
		DatasetTypeEICU,
		DatasetTypeNHANES,
		DatasetTypePIC,
	}
}

// ValidateDatasetType 验证数据集类型
func ValidateDatasetType(datasetType DatasetType) bool {
	supportedDatasets := GetSupportedDatasets()
	for _, supported := range supportedDatasets {
		if datasetType == supported {
			return true
		}
	}
	return false
}

// GetDatasetConfig 获取数据集配置
func GetDatasetConfig(datasetType DatasetType) (*DatasetConfig, error) {
	switch datasetType {
	case DatasetTypeMIMICIV:
		return &DatasetConfig{
			Type:        DatasetTypeMIMICIV,
			Name:        "MIMIC-IV",
			Description: "Medical Information Mart for Intensive Care IV",
			Version:     "2.2",
			Schemas:     []string{"mimiciv_hosp", "mimiciv_icu", "mimiciv_derived"},
			PrimaryTables: []string{"patients", "admissions", "icustays"},
			TableCount:    31,
			Features: DatasetFeatures{
				SupportsTimeRange:   true,
				SupportsAggregation: true,
				SupportsJoins:       true,
				MaxQueryComplexity:  10,
			},
		}, nil
	case DatasetTypeEICU:
		return &DatasetConfig{
			Type:        DatasetTypeEICU,
			Name:        "eICU Collaborative Research Database",
			Description: "Multi-center ICU database",
			Version:     "2.0",
			Schemas:     []string{"eicu_crd"},
			PrimaryTables: []string{"patient", "admissiondx"},
			TableCount:    25,
			Features: DatasetFeatures{
				SupportsTimeRange:   true,
				SupportsAggregation: true,
				SupportsJoins:       true,
				MaxQueryComplexity:  8,
			},
		}, nil
	case DatasetTypeNHANES:
		return &DatasetConfig{
			Type:        DatasetTypeNHANES,
			Name:        "National Health and Nutrition Examination Survey",
			Description: "Population health survey data",
			Version:     "2017-2018",
			Schemas:     []string{"nhanes"},
			PrimaryTables: []string{"demographics", "examination"},
			TableCount:    15,
			Features: DatasetFeatures{
				SupportsTimeRange:   false,
				SupportsAggregation: true,
				SupportsJoins:       true,
				MaxQueryComplexity:  6,
			},
		}, nil
	case DatasetTypePIC:
		return &DatasetConfig{
			Type:        DatasetTypePIC,
			Name:        "Pediatric Intensive Care",
			Description: "Pediatric ICU database",
			Version:     "1.0",
			Schemas:     []string{"pic"},
			PrimaryTables: []string{"patients", "events"},
			TableCount:    12,
			Features: DatasetFeatures{
				SupportsTimeRange:   true,
				SupportsAggregation: true,
				SupportsJoins:       true,
				MaxQueryComplexity:  7,
			},
		}, nil
	default:
		return nil, fmt.Errorf("unsupported dataset type: %s", datasetType)
	}
}

// GetTableSchemaMapping 获取表到schema的映射
func GetTableSchemaMapping(datasetType DatasetType) map[string]string {
	switch datasetType {
	case DatasetTypeMIMICIV:
		return map[string]string{
			// mimiciv_hosp schema
			"admissions": "mimiciv_hosp", "d_hcpcs": "mimiciv_hosp", "d_icd_diagnoses": "mimiciv_hosp",
			"d_icd_procedures": "mimiciv_hosp", "d_labitems": "mimiciv_hosp", "diagnoses_icd": "mimiciv_hosp",
			"drgcodes": "mimiciv_hosp", "emar": "mimiciv_hosp", "emar_detail": "mimiciv_hosp",
			"hcpcsevents": "mimiciv_hosp", "labevents": "mimiciv_hosp", "microbiologyevents": "mimiciv_hosp",
			"omr": "mimiciv_hosp", "patients": "mimiciv_hosp", "pharmacy": "mimiciv_hosp",
			"poe": "mimiciv_hosp", "poe_detail": "mimiciv_hosp", "prescriptions": "mimiciv_hosp",
			"procedures_icd": "mimiciv_hosp", "provider": "mimiciv_hosp", "services": "mimiciv_hosp",
			"transfers": "mimiciv_hosp",
			// mimiciv_icu schema
			"caregiver": "mimiciv_icu", "chartevents": "mimiciv_icu", "d_items": "mimiciv_icu",
			"datetimeevents": "mimiciv_icu", "icustays": "mimiciv_icu", "ingredientevents": "mimiciv_icu",
			"inputevents": "mimiciv_icu", "outputevents": "mimiciv_icu", "procedureevents": "mimiciv_icu",
		}
	case DatasetTypeEICU:
		return map[string]string{
			"patient": "eicu_crd", "admissiondx": "eicu_crd", "diagnosis": "eicu_crd",
			"lab": "eicu_crd", "medication": "eicu_crd", "nursecharting": "eicu_crd",
		}
	case DatasetTypeNHANES:
		return map[string]string{
			"demographics": "nhanes", "examination": "nhanes", "laboratory": "nhanes",
			"questionnaire": "nhanes", "dietary": "nhanes",
		}
	case DatasetTypePIC:
		return map[string]string{
			"patients": "pic", "events": "pic", "diagnoses": "pic",
			"medications": "pic", "procedures": "pic",
		}
	default:
		return map[string]string{}
	}
}
