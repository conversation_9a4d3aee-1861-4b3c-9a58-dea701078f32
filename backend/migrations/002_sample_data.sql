-- 示例数据插入语句
-- 用于开发和测试环境

-- 插入示例数据集表信息
INSERT INTO dataset_tables (dataset_id, table_name, display_name, description, record_count) VALUES
-- MIMIC-III 表
('mimic-iii', 'patients', '患者信息表', '包含患者的基本信息', 46520),
('mimic-iii', 'admissions', '入院记录表', '患者入院、出院和转科记录', 58976),
('mimic-iii', 'icustays', 'ICU住院表', 'ICU住院记录', 61532),
('mimic-iii', 'chartevents', '图表事件表', '患者监护数据', 330712483),
('mimic-iii', 'labevents', '实验室检查表', '实验室检查结果', 27854055),
('mimic-iii', 'prescriptions', '处方表', '药物处方记录', 4156450),
('mimic-iii', 'diagnoses_icd', '诊断表', 'ICD诊断代码', 651047),
('mimic-iii', 'procedures_icd', '手术表', 'ICD手术代码', 240095),

-- MIMIC-IV 表
('mimic-iv', 'patients', '患者信息表', '包含患者的基本信息', 76540),
('mimic-iv', 'admissions', '入院记录表', '患者入院、出院记录', 523740),
('mimic-iv', 'icustays', 'ICU住院表', 'ICU住院记录', 76943),
('mimic-iv', 'chartevents', '图表事件表', '患者监护数据', 454324021),
('mimic-iv', 'labevents', '实验室检查表', '实验室检查结果', 122103667),
('mimic-iv', 'prescriptions', '处方表', '药物处方记录', 17372351),

-- eICU 表
('eicu', 'patient', '患者表', '患者基本信息', 139367),
('eicu', 'admissiondx', '入院诊断表', '入院诊断信息', 202712),
('eicu', 'vitalperiodic', '生命体征表', '定期生命体征记录', 146671642),
('eicu', 'lab', '实验室检查表', '实验室检查结果', 27876586),
('eicu', 'medication', '药物表', '药物使用记录', 7939992)
ON CONFLICT (dataset_id, table_name) DO NOTHING;

-- 插入示例数据字段
INSERT INTO data_fields (dataset_id, table_name, field_name, display_name, description, data_type, category, is_required) VALUES
-- 患者表字段
('mimic-iii', 'patients', 'subject_id', '患者ID', '患者唯一标识符', 'INTEGER', '标识符', true),
('mimic-iii', 'patients', 'gender', '性别', '患者性别', 'VARCHAR', '人口统计学', true),
('mimic-iii', 'patients', 'dob', '出生日期', '患者出生日期', 'TIMESTAMP', '人口统计学', false),
('mimic-iii', 'patients', 'dod', '死亡日期', '患者死亡日期', 'TIMESTAMP', '人口统计学', false),

-- 入院表字段
('mimic-iii', 'admissions', 'subject_id', '患者ID', '患者唯一标识符', 'INTEGER', '标识符', true),
('mimic-iii', 'admissions', 'hadm_id', '入院ID', '入院唯一标识符', 'INTEGER', '标识符', true),
('mimic-iii', 'admissions', 'admittime', '入院时间', '患者入院时间', 'TIMESTAMP', '时间', true),
('mimic-iii', 'admissions', 'dischtime', '出院时间', '患者出院时间', 'TIMESTAMP', '时间', false),
('mimic-iii', 'admissions', 'admission_type', '入院类型', '入院类型分类', 'VARCHAR', '分类', true),
('mimic-iii', 'admissions', 'diagnosis', '诊断', '入院诊断', 'TEXT', '临床', false),

-- ICU住院表字段
('mimic-iii', 'icustays', 'subject_id', '患者ID', '患者唯一标识符', 'INTEGER', '标识符', true),
('mimic-iii', 'icustays', 'hadm_id', '入院ID', '入院唯一标识符', 'INTEGER', '标识符', true),
('mimic-iii', 'icustays', 'icustay_id', 'ICU住院ID', 'ICU住院唯一标识符', 'INTEGER', '标识符', true),
('mimic-iii', 'icustays', 'first_careunit', '首个监护单元', '患者首个监护单元', 'VARCHAR', '分类', true),
('mimic-iii', 'icustays', 'intime', 'ICU入院时间', 'ICU入院时间', 'TIMESTAMP', '时间', true),
('mimic-iii', 'icustays', 'outtime', 'ICU出院时间', 'ICU出院时间', 'TIMESTAMP', '时间', false),
('mimic-iii', 'icustays', 'los', '住院天数', 'ICU住院天数', 'NUMERIC', '数值', false),

-- 图表事件表字段
('mimic-iii', 'chartevents', 'subject_id', '患者ID', '患者唯一标识符', 'INTEGER', '标识符', true),
('mimic-iii', 'chartevents', 'hadm_id', '入院ID', '入院唯一标识符', 'INTEGER', '标识符', false),
('mimic-iii', 'chartevents', 'icustay_id', 'ICU住院ID', 'ICU住院唯一标识符', 'INTEGER', '标识符', false),
('mimic-iii', 'chartevents', 'itemid', '项目ID', '监护项目标识符', 'INTEGER', '标识符', true),
('mimic-iii', 'chartevents', 'charttime', '记录时间', '监护数据记录时间', 'TIMESTAMP', '时间', true),
('mimic-iii', 'chartevents', 'value', '数值', '监护数据数值', 'VARCHAR', '数值', false),
('mimic-iii', 'chartevents', 'valuenum', '数值型数值', '监护数据数值型数值', 'NUMERIC', '数值', false),
('mimic-iii', 'chartevents', 'valueuom', '单位', '监护数据单位', 'VARCHAR', '单位', false),

-- 实验室检查表字段
('mimic-iii', 'labevents', 'subject_id', '患者ID', '患者唯一标识符', 'INTEGER', '标识符', true),
('mimic-iii', 'labevents', 'hadm_id', '入院ID', '入院唯一标识符', 'INTEGER', '标识符', false),
('mimic-iii', 'labevents', 'itemid', '项目ID', '实验室项目标识符', 'INTEGER', '标识符', true),
('mimic-iii', 'labevents', 'charttime', '检查时间', '实验室检查时间', 'TIMESTAMP', '时间', true),
('mimic-iii', 'labevents', 'value', '检查结果', '实验室检查结果', 'VARCHAR', '数值', false),
('mimic-iii', 'labevents', 'valuenum', '数值型结果', '实验室检查数值型结果', 'NUMERIC', '数值', false),
('mimic-iii', 'labevents', 'valueuom', '单位', '实验室检查结果单位', 'VARCHAR', '单位', false),
('mimic-iii', 'labevents', 'flag', '异常标志', '检查结果异常标志', 'VARCHAR', '标志', false)

ON CONFLICT (dataset_id, table_name, field_name) DO NOTHING;

-- 插入示例查询模板
INSERT INTO query_templates (name, description, category, dataset_id, template_config, is_public, author) VALUES
('患者基本信息查询', '查询患者的基本人口统计学信息', '基础查询', 'mimic-iii', 
 '{"tables": ["patients"], "fields": ["subject_id", "gender", "dob"], "conditions": [], "limit": 100}', 
 true, '系统管理员'),

('ICU住院患者查询', '查询ICU住院患者的住院信息', '临床查询', 'mimic-iii',
 '{"tables": ["icustays"], "fields": ["subject_id", "icustay_id", "first_careunit", "intime", "outtime", "los"], "conditions": [], "limit": 100}',
 true, '系统管理员'),

('实验室检查结果查询', '查询患者的实验室检查结果', '检验查询', 'mimic-iii',
 '{"tables": ["labevents"], "fields": ["subject_id", "itemid", "charttime", "valuenum", "valueuom"], "conditions": [{"field": "valuenum", "operator": "IS NOT NULL"}], "limit": 1000}',
 true, '系统管理员'),

('生命体征监护数据查询', '查询患者的生命体征监护数据', '监护查询', 'mimic-iii',
 '{"tables": ["chartevents"], "fields": ["subject_id", "itemid", "charttime", "valuenum", "valueuom"], "conditions": [{"field": "valuenum", "operator": "IS NOT NULL"}], "limit": 1000}',
 true, '系统管理员')

ON CONFLICT DO NOTHING; 