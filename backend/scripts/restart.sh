#!/bin/bash

# Medical Data Platform Server - Restart Script
# 医疗数据平台服务器重启脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 显示帮助信息
show_help() {
    echo "Medical Data Platform Server - Restart Script"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help          显示帮助信息"
    echo "  -f, --force         强制停止后重启"
    echo "  -d, --docker        重启Docker容器"
    echo "  -p, --production    重启生产环境Docker服务"
    echo "  -t, --timeout SEC   停止等待超时时间（默认30秒）"
    echo "  -w, --wait SEC      停止后等待时间（默认5秒）"
    echo "  --build             Docker模式下重新构建镜像"
    echo "  --pull              Docker模式下拉取最新镜像"
    echo ""
    echo "Examples:"
    echo "  $0                  # 重启本地服务"
    echo "  $0 -f               # 强制停止后重启本地服务"
    echo "  $0 -d               # 重启开发环境Docker服务"
    echo "  $0 -p               # 重启生产环境Docker服务"
    echo "  $0 -d --build       # 重新构建并重启开发环境"
    echo "  $0 -p --pull        # 拉取最新镜像并重启生产环境"
}

# 重启本地服务
restart_local_service() {
    local force_kill=${1:-false}
    local timeout=${2:-30}
    local wait_time=${3:-5}
    
    log_info "正在重启医疗数据平台本地服务..."
    
    # 停止服务
    log_info "步骤 1/3: 停止服务"
    if [[ -f "$SCRIPT_DIR/stop.sh" ]]; then
        if [[ "$force_kill" == "true" ]]; then
            bash "$SCRIPT_DIR/stop.sh" -f -t "$timeout"
        else
            bash "$SCRIPT_DIR/stop.sh" -t "$timeout"
        fi
    else
        log_error "stop.sh脚本不存在"
        return 1
    fi
    
    # 等待
    log_info "步骤 2/3: 等待 ${wait_time} 秒..."
    sleep "$wait_time"
    
    # 启动服务
    log_info "步骤 3/3: 启动服务"
    if [[ -f "$SCRIPT_DIR/start.sh" ]]; then
        bash "$SCRIPT_DIR/start.sh"
    else
        log_error "start.sh脚本不存在"
        return 1
    fi
    
    log_success "本地服务重启完成"
}

# 重启Docker开发环境
restart_docker_dev() {
    local build_image=${1:-false}
    local wait_time=${2:-5}
    
    log_info "正在重启Docker开发环境..."
    
    cd "$PROJECT_ROOT"
    
    if [[ ! -f "docker-compose.yml" ]]; then
        log_error "docker-compose.yml文件不存在"
        return 1
    fi
    
    # 停止服务
    log_info "步骤 1/4: 停止Docker服务"
    docker-compose down
    
    # 等待
    log_info "步骤 2/4: 等待 ${wait_time} 秒..."
    sleep "$wait_time"
    
    # 构建镜像（如果需要）
    if [[ "$build_image" == "true" ]]; then
        log_info "步骤 3/4: 重新构建镜像"
        docker-compose build --no-cache
    else
        log_info "步骤 3/4: 跳过镜像构建"
    fi
    
    # 启动服务
    log_info "步骤 4/4: 启动Docker服务"
    docker-compose up -d
    
    # 显示服务状态
    log_info "服务状态:"
    docker-compose ps
    
    log_success "Docker开发环境重启完成"
}

# 重启Docker生产环境
restart_docker_prod() {
    local pull_image=${1:-false}
    local wait_time=${2:-5}
    
    log_info "正在重启Docker生产环境..."
    
    cd "$PROJECT_ROOT"
    
    if [[ ! -f "docker-compose.prod.yml" ]]; then
        log_error "docker-compose.prod.yml文件不存在"
        return 1
    fi
    
    # 停止服务
    log_info "步骤 1/4: 停止Docker生产服务"
    docker-compose -f docker-compose.prod.yml down
    
    # 等待
    log_info "步骤 2/4: 等待 ${wait_time} 秒..."
    sleep "$wait_time"
    
    # 拉取镜像（如果需要）
    if [[ "$pull_image" == "true" ]]; then
        log_info "步骤 3/4: 拉取最新镜像"
        docker-compose -f docker-compose.prod.yml pull
    else
        log_info "步骤 3/4: 跳过镜像拉取"
    fi
    
    # 启动服务
    log_info "步骤 4/4: 启动Docker生产服务"
    docker-compose -f docker-compose.prod.yml up -d
    
    # 显示服务状态
    log_info "服务状态:"
    docker-compose -f docker-compose.prod.yml ps
    
    log_success "Docker生产环境重启完成"
}

# 健康检查
health_check() {
    local max_attempts=30
    local attempt=1
    local health_url="http://localhost:8080/health"
    
    log_info "正在进行健康检查..."
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f -s "$health_url" >/dev/null 2>&1; then
            log_success "健康检查通过 (尝试 $attempt/$max_attempts)"
            return 0
        fi
        
        if [[ $((attempt % 5)) -eq 0 ]]; then
            log_info "健康检查中... (尝试 $attempt/$max_attempts)"
        fi
        
        sleep 2
        attempt=$((attempt + 1))
    done
    
    log_warning "健康检查超时，服务可能需要更多时间启动"
    return 1
}

# 主函数
main() {
    local force_kill=false
    local docker_mode=false
    local production_mode=false
    local timeout=30
    local wait_time=5
    local build_image=false
    local pull_image=false
    local skip_health_check=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -f|--force)
                force_kill=true
                shift
                ;;
            -d|--docker)
                docker_mode=true
                shift
                ;;
            -p|--production)
                production_mode=true
                shift
                ;;
            -t|--timeout)
                timeout="$2"
                if ! [[ "$timeout" =~ ^[0-9]+$ ]]; then
                    log_error "超时时间必须是数字"
                    exit 1
                fi
                shift 2
                ;;
            -w|--wait)
                wait_time="$2"
                if ! [[ "$wait_time" =~ ^[0-9]+$ ]]; then
                    log_error "等待时间必须是数字"
                    exit 1
                fi
                shift 2
                ;;
            --build)
                build_image=true
                shift
                ;;
            --pull)
                pull_image=true
                shift
                ;;
            --skip-health-check)
                skip_health_check=true
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 显示重启信息
    echo "========================================"
    echo "  医疗数据平台服务器 - 重启脚本"
    echo "========================================"
    echo "时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "模式: $(if [[ "$production_mode" == "true" ]]; then echo "生产环境Docker"; elif [[ "$docker_mode" == "true" ]]; then echo "开发环境Docker"; else echo "本地服务"; fi)"
    echo "强制停止: $(if [[ "$force_kill" == "true" ]]; then echo "是"; else echo "否"; fi)"
    echo "停止超时: ${timeout}秒"
    echo "等待时间: ${wait_time}秒"
    if [[ "$docker_mode" == "true" ]]; then
        echo "重新构建: $(if [[ "$build_image" == "true" ]]; then echo "是"; else echo "否"; fi)"
    fi
    if [[ "$production_mode" == "true" ]]; then
        echo "拉取镜像: $(if [[ "$pull_image" == "true" ]]; then echo "是"; else echo "否"; fi)"
    fi
    echo "========================================"
    
    # 执行重启操作
    local restart_success=false
    
    if [[ "$production_mode" == "true" ]]; then
        restart_docker_prod "$pull_image" "$wait_time" && restart_success=true
    elif [[ "$docker_mode" == "true" ]]; then
        restart_docker_dev "$build_image" "$wait_time" && restart_success=true
    else
        restart_local_service "$force_kill" "$timeout" "$wait_time" && restart_success=true
    fi
    
    if [[ "$restart_success" == "true" ]]; then
        # 健康检查（除非跳过）
        if [[ "$skip_health_check" != "true" ]]; then
            health_check || log_warning "健康检查未通过，但服务已重启"
        fi
        
        log_success "医疗数据平台服务重启完成"
        echo ""
        echo "服务访问地址:"
        echo "  - 健康检查: http://localhost:8080/health"
        echo "  - API文档: http://localhost:8080/swagger/index.html"
        echo ""
    else
        log_error "服务重启失败"
        exit 1
    fi
}

# 信号处理
trap 'log_error "脚本被中断"; exit 1' INT TERM

# 执行主函数
main "$@" 