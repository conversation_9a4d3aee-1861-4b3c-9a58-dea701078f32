# 医学数据查询构建器

## 概述

医学数据查询构建器是一个专为医学生和医学研究人员设计的直观数据查询工具。它将复杂的SQL查询操作抽象为医学业务流程，使用户能够轻松地从MIMIC数据库中提取所需的医学数据。

## 核心特性

### 🏥 以患者为中心的查询流程
- **第一步：定义研究人群** - 通过医学术语设置患者筛选条件
- **第二步：选择数据维度** - 按医学分类选择需要的数据字段
- **第三步：设置时间范围** - 定义数据的时间约束
- **第四步：预览导出** - 查看结果并导出数据

### 🔬 医学术语导向
- 使用"研究人群"替代"WHERE条件"
- 使用"数据维度"替代"字段选择"
- 使用"关联数据"替代"JOIN操作"
- 隐藏复杂的SQL技术细节

### 📊 智能化功能
- **自动表关联建议** - 系统自动识别表间关系
- **查询复杂度评估** - 实时评估查询性能
- **结果数量预估** - 提前估算查询结果
- **查询优化建议** - 提供性能优化建议

## 技术架构

### 前端组件
```
frontend/components/query-builder/
├── medical-query-builder.tsx     # 主查询构建器组件
└── ...
```

### 后端API
```
backend/internal/
├── api/medical_query.go          # 医学查询API控制器
├── service/medical_query_service.go  # 医学查询服务层
├── model/medical_query.go        # 医学查询数据模型
└── repository/medical_query_repository.go  # 医学查询数据访问层
```

### 数据库表
```sql
-- 医学查询历史表
medical_query_history

-- 医学查询模板表
medical_query_templates

-- 查询执行日志表
query_execution_logs

-- 用户查询偏好表
user_query_preferences

-- 查询结果缓存表
query_result_cache
```

## API 端点

### 医学查询相关
- `GET /api/medical-query/categories` - 获取医学字段分类
- `POST /api/medical-query/validate-cohort` - 验证人群筛选条件
- `POST /api/medical-query/estimate` - 估算查询结果
- `POST /api/medical-query/execute` - 执行医学查询
- `GET /api/medical-query/templates` - 获取查询模板
- `POST /api/medical-query/templates` - 保存查询模板
- `GET /api/medical-query/history` - 获取查询历史
- `POST /api/medical-query/:queryId/export` - 导出查询结果

## 数据字段分类

### 基本信息 (demographics)
- 患者ID (subject_id)
- 年龄 (anchor_age)
- 性别 (gender)
- 死亡日期 (dod)

### 住院信息 (admission)
- 住院ID (hadm_id)
- 入院时间 (admittime)
- 出院时间 (dischtime)
- 入院类型 (admission_type)
- 入院科室 (admission_location)
- 保险类型 (insurance)

### 检验结果 (laboratory)
- 检验事件ID (labevent_id)
- 检验项目ID (itemid)
- 检验时间 (charttime)
- 检验值 (valuenum)
- 检验单位 (valueuom)

### 诊断信息 (diagnosis)
- ICD诊断码 (icd_code)
- ICD版本 (icd_version)
- 诊断序号 (seq_num)

### 用药信息 (medication)
- 药物名称 (drug)
- 药物类型 (drug_type)
- 给药途径 (route)
- 开始时间 (starttime)
- 结束时间 (stoptime)

## 使用示例

### 1. 基础患者信息查询
```typescript
const queryConfig = {
  studyName: "基础患者信息查询",
  cohortCriteria: [],
  dataDimensions: [
    {
      category: "demographics",
      fields: [
        { id: "age", name: "年龄", nameEn: "anchor_age" },
        { id: "gender", name: "性别", nameEn: "gender" }
      ],
      isSelected: true
    }
  ],
  timeRange: { type: "admission" },
  outputFormat: "csv",
  maxRecords: 1000
}
```

### 2. 成年患者住院分析
```typescript
const queryConfig = {
  studyName: "成年患者住院分析",
  cohortCriteria: [
    {
      id: "1",
      category: "demographics",
      field: "age",
      operator: ">=",
      value: 18,
      description: "年龄大于等于18岁"
    }
  ],
  dataDimensions: [
    { category: "demographics", isSelected: true },
    { category: "admission", isSelected: true }
  ],
  timeRange: { type: "admission" },
  outputFormat: "excel",
  maxRecords: 5000
}
```

## 查询模板

系统提供预定义的查询模板：

1. **基础患者信息查询** - 获取患者基本人口学信息
2. **成年患者住院分析** - 分析成年患者的住院情况
3. **ICU首次入院指标** - ICU患者首次入院相关指标
4. **机械通气患者分析** - 机械通气患者的临床数据
5. **心血管疾病风险** - 心血管疾病患者风险评估
6. **感染患者抗生素** - 感染患者抗生素使用情况

## 性能优化

### 查询优化策略
1. **智能索引建议** - 基于查询模式建议创建索引
2. **查询缓存** - 缓存常用查询结果
3. **分页查询** - 大结果集自动分页
4. **异步执行** - 复杂查询异步处理

### 资源限制
- 单次查询最大记录数：50,000条
- 查询超时时间：5分钟
- 并发查询限制：每用户3个
- 结果缓存时间：24小时

## 安全性

### 数据访问控制
- 基于角色的访问控制 (RBAC)
- 查询审计日志
- 敏感数据脱敏
- 导出权限管理

### 查询安全
- SQL注入防护
- 查询复杂度限制
- 资源使用监控
- 异常查询检测

## 部署说明

### 数据库迁移
```bash
# 运行医学查询相关的数据库迁移
psql -d medical_data_platform -f backend/migrations/003_medical_query_tables.sql
```

### 环境配置
```yaml
# config.yaml
medical_query:
  max_records: 50000
  query_timeout: 300s
  cache_ttl: 24h
  enable_query_cache: true
```

### 启动服务
```bash
# 启动后端服务
cd backend
go run cmd/server/main.go

# 启动前端服务
cd frontend
npm run dev
```

## 监控和日志

### 查询监控指标
- 查询执行时间
- 查询成功率
- 资源使用情况
- 用户活跃度

### 日志记录
- 查询执行日志
- 错误日志
- 性能日志
- 用户操作日志

## 未来规划

### 功能增强
- [ ] AI辅助查询建议
- [ ] 可视化查询结果
- [ ] 协作查询功能
- [ ] 查询结果分享
- [ ] 移动端支持

### 性能优化
- [ ] 查询并行化
- [ ] 智能查询重写
- [ ] 预计算视图
- [ ] 分布式查询

### 数据源扩展
- [ ] 支持更多医学数据库
- [ ] 实时数据流处理
- [ ] 外部数据源集成
- [ ] 多数据源联合查询
