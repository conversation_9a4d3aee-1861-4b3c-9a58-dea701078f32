package database

import (
	"context"
	"database/sql"
	"fmt"
	"sync"
	"time"

	"github.com/chungzy/medical-data-platform/pkg/logger"
	_ "github.com/lib/pq"
)

// DataSourceConfig 数据源配置
type DataSourceConfig struct {
	Enabled           bool   `yaml:"enabled"`
	Type              string `yaml:"type"`
	Host              string `yaml:"host"`
	Port              int    `yaml:"port"`
	User              string `yaml:"user"`
	Password          string `yaml:"password"`
	DBName            string `yaml:"dbname"`
	SSLMode           string `yaml:"sslmode"`
	Schema            string `yaml:"schema"`
	Description       string `yaml:"description"`
	MaxConnections    int    `yaml:"max_connections"`
	ConnectionTimeout int    `yaml:"connection_timeout"`
	QueryTimeout      int    `yaml:"query_timeout"`
}

// DataSourceStatus 数据源状态
type DataSourceStatus struct {
	ID          string    `json:"id"`
	Name        string    `json:"name"`
	IsHealthy   bool      `json:"isHealthy"`
	LastCheck   time.Time `json:"lastCheck"`
	ErrorMsg    string    `json:"errorMsg,omitempty"`
	Description string    `json:"description"`
}

// DataSourceManager 数据源管理器
type DataSourceManager struct {
	connections map[string]*sql.DB
	configs     map[string]DataSourceConfig
	statuses    map[string]DataSourceStatus
	mutex       sync.RWMutex
}

// NewDataSourceManager 创建数据源管理器
func NewDataSourceManager() *DataSourceManager {
	return &DataSourceManager{
		connections: make(map[string]*sql.DB),
		configs:     make(map[string]DataSourceConfig),
		statuses:    make(map[string]DataSourceStatus),
	}
}

// AddDataSource 添加数据源
func (dsm *DataSourceManager) AddDataSource(id string, config DataSourceConfig) error {
	dsm.mutex.Lock()
	defer dsm.mutex.Unlock()

	if !config.Enabled {
		logger.Info("Data source is disabled", map[string]interface{}{
			"datasource_id": id,
		})
		dsm.statuses[id] = DataSourceStatus{
			ID:          id,
			Name:        config.Description,
			IsHealthy:   false,
			LastCheck:   time.Now(),
			ErrorMsg:    "Data source is disabled",
			Description: config.Description,
		}
		return nil
	}

	// 构建连接字符串
	connStr := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		config.Host, config.Port, config.User, config.Password, config.DBName, config.SSLMode)

	// 建立数据库连接
	db, err := sql.Open(config.Type, connStr)
	if err != nil {
		logger.Error("Failed to open database connection", err, map[string]interface{}{
			"datasource_id": id,
			"host":          config.Host,
			"port":          config.Port,
			"dbname":        config.DBName,
		})
		dsm.statuses[id] = DataSourceStatus{
			ID:          id,
			Name:        config.Description,
			IsHealthy:   false,
			LastCheck:   time.Now(),
			ErrorMsg:    err.Error(),
			Description: config.Description,
		}
		return err
	}

	// 设置连接池参数
	if config.MaxConnections > 0 {
		db.SetMaxOpenConns(config.MaxConnections)
		db.SetMaxIdleConns(config.MaxConnections / 2)
	}
	db.SetConnMaxLifetime(time.Hour)

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(config.ConnectionTimeout)*time.Second)
	defer cancel()

	if err := db.PingContext(ctx); err != nil {
		logger.Error("Failed to ping database", err, map[string]interface{}{
			"datasource_id": id,
			"host":          config.Host,
			"port":          config.Port,
			"dbname":        config.DBName,
		})
		db.Close()
		dsm.statuses[id] = DataSourceStatus{
			ID:          id,
			Name:        config.Description,
			IsHealthy:   false,
			LastCheck:   time.Now(),
			ErrorMsg:    err.Error(),
			Description: config.Description,
		}
		return err
	}

	// 保存连接和配置
	dsm.connections[id] = db
	dsm.configs[id] = config
	dsm.statuses[id] = DataSourceStatus{
		ID:          id,
		Name:        config.Description,
		IsHealthy:   true,
		LastCheck:   time.Now(),
		Description: config.Description,
	}

	logger.Info("Data source connected successfully", map[string]interface{}{
		"datasource_id": id,
		"host":          config.Host,
		"port":          config.Port,
		"dbname":        config.DBName,
	})

	return nil
}

// GetConnection 获取数据库连接
func (dsm *DataSourceManager) GetConnection(id string) (*sql.DB, error) {
	dsm.mutex.RLock()
	defer dsm.mutex.RUnlock()

	db, exists := dsm.connections[id]
	if !exists {
		return nil, fmt.Errorf("data source not found: %s", id)
	}

	// 检查连接是否健康
	if err := db.Ping(); err != nil {
		logger.Error("Data source connection unhealthy", err, map[string]interface{}{
			"datasource_id": id,
		})
		return nil, fmt.Errorf("data source unhealthy: %s", id)
	}

	return db, nil
}

// GetStatus 获取数据源状态
func (dsm *DataSourceManager) GetStatus(id string) (DataSourceStatus, bool) {
	dsm.mutex.RLock()
	defer dsm.mutex.RUnlock()

	status, exists := dsm.statuses[id]
	return status, exists
}

// GetAllStatuses 获取所有数据源状态
func (dsm *DataSourceManager) GetAllStatuses() map[string]DataSourceStatus {
	dsm.mutex.RLock()
	defer dsm.mutex.RUnlock()

	statuses := make(map[string]DataSourceStatus)
	for id, status := range dsm.statuses {
		statuses[id] = status
	}
	return statuses
}

// HealthCheck 执行健康检查
func (dsm *DataSourceManager) HealthCheck(id string) error {
	dsm.mutex.Lock()
	defer dsm.mutex.Unlock()

	db, exists := dsm.connections[id]
	if !exists {
		return fmt.Errorf("data source not found: %s", id)
	}

	config := dsm.configs[id]
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(config.ConnectionTimeout)*time.Second)
	defer cancel()

	err := db.PingContext(ctx)
	status := dsm.statuses[id]
	status.LastCheck = time.Now()

	if err != nil {
		status.IsHealthy = false
		status.ErrorMsg = err.Error()
		logger.Error("Data source health check failed", err, map[string]interface{}{
			"datasource_id": id,
		})
	} else {
		status.IsHealthy = true
		status.ErrorMsg = ""
	}

	dsm.statuses[id] = status
	return err
}

// Close 关闭所有连接
func (dsm *DataSourceManager) Close() {
	dsm.mutex.Lock()
	defer dsm.mutex.Unlock()

	for id, db := range dsm.connections {
		if err := db.Close(); err != nil {
			logger.Error("Failed to close database connection", err, map[string]interface{}{
				"datasource_id": id,
			})
		}
	}

	dsm.connections = make(map[string]*sql.DB)
	dsm.configs = make(map[string]DataSourceConfig)
	dsm.statuses = make(map[string]DataSourceStatus)
}

// StartHealthCheckRoutine 启动健康检查例程
func (dsm *DataSourceManager) StartHealthCheckRoutine(interval time.Duration) {
	ticker := time.NewTicker(interval)
	go func() {
		for range ticker.C {
			dsm.performHealthChecks()
		}
	}()
}

// performHealthChecks 执行所有数据源的健康检查
func (dsm *DataSourceManager) performHealthChecks() {
	dsm.mutex.RLock()
	ids := make([]string, 0, len(dsm.connections))
	for id := range dsm.connections {
		ids = append(ids, id)
	}
	dsm.mutex.RUnlock()

	for _, id := range ids {
		dsm.HealthCheck(id)
	}
}

// IsDataSourceHealthy 检查数据源是否健康
func (dsm *DataSourceManager) IsDataSourceHealthy(id string) bool {
	status, exists := dsm.GetStatus(id)
	return exists && status.IsHealthy
}

// GetHealthyDataSources 获取所有健康的数据源ID列表
func (dsm *DataSourceManager) GetHealthyDataSources() []string {
	dsm.mutex.RLock()
	defer dsm.mutex.RUnlock()

	var healthy []string
	for id, status := range dsm.statuses {
		if status.IsHealthy {
			healthy = append(healthy, id)
		}
	}
	return healthy
}

// GetConnectionStats 获取连接池统计信息
func (dsm *DataSourceManager) GetConnectionStats(id string) (map[string]interface{}, error) {
	dsm.mutex.RLock()
	defer dsm.mutex.RUnlock()

	db, exists := dsm.connections[id]
	if !exists {
		return nil, fmt.Errorf("data source not found: %s", id)
	}

	stats := db.Stats()
	return map[string]interface{}{
		"maxOpenConnections": stats.MaxOpenConnections,
		"openConnections":    stats.OpenConnections,
		"inUse":              stats.InUse,
		"idle":               stats.Idle,
		"waitCount":          stats.WaitCount,
		"waitDuration":       stats.WaitDuration.String(),
		"maxIdleClosed":      stats.MaxIdleClosed,
		"maxIdleTimeClosed":  stats.MaxIdleTimeClosed,
		"maxLifetimeClosed":  stats.MaxLifetimeClosed,
	}, nil
}

// GetAllConnectionStats 获取所有数据源的连接池统计信息
func (dsm *DataSourceManager) GetAllConnectionStats() map[string]interface{} {
	dsm.mutex.RLock()
	defer dsm.mutex.RUnlock()

	result := make(map[string]interface{})
	for id := range dsm.connections {
		if stats, err := dsm.GetConnectionStats(id); err == nil {
			result[id] = stats
		}
	}
	return result
}

// LogConnectionStats 记录连接池统计信息
func (dsm *DataSourceManager) LogConnectionStats() {
	dsm.mutex.RLock()
	defer dsm.mutex.RUnlock()

	for id, db := range dsm.connections {
		stats := db.Stats()
		logger.Info("Connection pool stats", map[string]interface{}{
			"datasource_id":       id,
			"max_open_conns":      stats.MaxOpenConnections,
			"open_conns":          stats.OpenConnections,
			"in_use":              stats.InUse,
			"idle":                stats.Idle,
			"wait_count":          stats.WaitCount,
			"wait_duration_ms":    stats.WaitDuration.Milliseconds(),
			"max_idle_closed":     stats.MaxIdleClosed,
			"max_lifetime_closed": stats.MaxLifetimeClosed,
		})
	}
}

// StartConnectionStatsLogging 启动连接池统计日志记录
func (dsm *DataSourceManager) StartConnectionStatsLogging(interval time.Duration) {
	ticker := time.NewTicker(interval)
	go func() {
		for range ticker.C {
			dsm.LogConnectionStats()
		}
	}()
}
