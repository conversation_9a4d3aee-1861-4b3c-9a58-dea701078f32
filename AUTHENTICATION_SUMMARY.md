# 医疗数据平台认证和权限系统总结（简化版）

## 概述

已完成医疗数据平台的认证和权限系统完善，实现了极简的权限管理设计，只在user表中添加一个permission字段来控制用户权限。

## 权限系统设计

### 简化权限模型
- **权限字段**: 在user表中添加`permission`字段（整数类型）
- **权限级别**:
  - `0`: 无权限（禁用用户）
  - `1`: 管理员（完整权限）
  - `2`: 普通用户（基础权限）

### 权限矩阵

| 功能 | 无权限(0) | 管理员(1) | 普通用户(2) |
|------|-----------|-----------|-------------|
| 登录系统 | ✗ | ✓ | ✓ |
| 数据集读取 | ✗ | ✓ | ✓ |
| 查询执行 | ✗ | ✓ | ✓ |
| 查询保存 | ✗ | ✓ | ✓ |
| 数据导出 | ✗ | ✓ | ✓ |
| 用户管理 | ✗ | ✓ | ✗ |
| 系统管理 | ✗ | ✓ | ✗ |

## 主要功能

### 1. 用户认证
- **注册**: 支持邮箱注册，密码强度验证
- **登录**: JWT token认证
- **密码安全**: 
  - 最少8位字符
  - 包含大小写字母、数字、特殊字符
  - bcrypt加密存储
  - 密码强度实时显示

### 2. 权限控制
- **路由保护**: 基于角色和权限的页面访问控制
- **组件级权限**: 细粒度的UI元素显示控制
- **API权限**: 后端接口权限验证中间件

### 3. 用户管理
- **管理员界面**: 完整的用户CRUD操作
- **角色分配**: 动态角色管理
- **状态管理**: 用户激活/禁用

## 技术实现

### 后端 (Go)
- **JWT认证**: 安全的token生成和验证
- **简化权限中间件**: 基于permission字段的API访问控制
- **密码服务**: 安全的密码处理和验证
- **极简数据库设计**: 只在user表添加permission字段

### 前端 (React/Next.js)
- **认证Hook**: `useAuth` 统一认证状态管理
- **权限Hook**: `usePermissions` 基于permission级别的权限检查
- **路由保护**: `RouteGuard` 组件保护敏感页面
- **权限组件**: `PermissionGuard` 条件渲染

## 文件结构

### 后端文件
```
backend/
├── internal/
│   ├── middleware/
│   │   ├── auth.go                    # JWT认证中间件
│   │   └── simple_permission.go      # 简化权限检查中间件
│   ├── model/
│   │   └── user.go                   # 用户模型（包含permission字段）
│   ├── service/
│   │   ├── user_service.go           # 用户服务
│   │   ├── simple_permission_service.go # 简化权限服务
│   │   └── password_service.go       # 密码安全服务
└── migrations/
    └── 004_add_user_permission_field.sql # 添加权限字段
```

### 前端文件
```
frontend/
├── lib/hooks/
│   ├── use-auth.tsx            # 认证Hook
│   └── use-permissions.tsx     # 权限Hook
├── components/auth/
│   ├── route-guard.tsx         # 路由保护组件
│   └── password-strength.tsx   # 密码强度组件
├── app/
│   ├── login/page.tsx          # 登录注册页面
│   ├── admin/users/page.tsx    # 用户管理页面
│   ├── test-auth/page.tsx      # 认证测试页面
│   └── unauthorized/page.tsx   # 未授权页面
└── query-builder/page.tsx     # 查询构建器（已保护）
```

## 使用指南

### 1. 用户注册和登录
1. 访问 `http://localhost:3000/login`
2. 选择"注册"标签页
3. 填写用户信息，选择角色（普通用户/管理员）
4. 系统会验证密码强度并显示实时反馈
5. 注册成功后自动登录

### 2. 权限测试
1. 访问 `http://localhost:3000/test-auth`
2. 可以快速创建测试账户
3. 查看当前用户权限
4. 测试API调用

### 3. 管理员功能
1. 使用管理员账户登录
2. 访问 `http://localhost:3000/admin/users`
3. 管理用户账户、分配角色
4. 查看用户统计信息

### 4. API调用
```bash
# 1. 登录获取token
curl 'http://localhost:8088/api/auth/login' \
  -H 'Content-Type: application/json' \
  --data-raw '{
    "email": "<EMAIL>",
    "password": "password123"
  }'

# 2. 使用token调用API
curl 'http://localhost:8088/api/medical-query/execute' \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  --data-raw '{"dataset_id":"mimic-iv",...}'
```

## 安全特性

1. **JWT Token**: 安全的无状态认证
2. **密码加密**: bcrypt哈希存储
3. **权限验证**: 多层权限检查
4. **路由保护**: 前端路由级别保护
5. **API保护**: 后端接口权限验证
6. **密码策略**: 强密码要求和验证

## 测试账户

系统提供了便捷的测试功能：
- 测试邮箱: `<EMAIL>`
- 测试密码: `test123456`
- 默认角色: `user`

可以通过测试页面快速创建和测试不同角色的账户。

## 数据库变更

只需要执行一个简单的迁移：

```sql
-- 添加权限字段
ALTER TABLE users ADD COLUMN IF NOT EXISTS permission INTEGER DEFAULT 2;

-- 根据现有role字段更新permission字段
UPDATE users SET permission = CASE
    WHEN role = 'admin' THEN 1
    WHEN role = 'user' THEN 2
    ELSE 2
END;

-- 添加约束
ALTER TABLE users ADD CONSTRAINT chk_user_permission CHECK (permission IN (0, 1, 2));
```

## 开发模式配置

为了解决开发阶段的权限问题，已添加开发模式支持：

### 环境变量配置
```bash
# 前端 (.env.development)
NEXT_PUBLIC_DEV_MODE=true
NEXT_PUBLIC_BYPASS_AUTH=true

# 后端 (.env.development)
DEV_MODE=true
BYPASS_AUTH=true
```

### 开发模式功能
- ✅ **绕过所有权限检查** - 开发阶段不会出现401/403错误
- ✅ **模拟管理员用户** - 自动获得所有权限
- ✅ **跳过JWT验证** - 无需真实token即可访问API
- ✅ **完整功能访问** - 所有页面和功能都可正常使用
- ✅ **免密码登录** - 支持一键快速登录，无需输入账号密码
- ✅ **预填充表单** - 登录表单自动填入开发账号信息
- ✅ **开发模式提示** - 界面显示开发模式状态和说明

## 用户界面修复

### Dashboard Layout 修复
- ✅ **用户信息显示** - 从认证状态获取真实用户信息
- ✅ **头像初始化** - 动态显示用户姓名首字母
- ✅ **角色显示** - 正确显示用户角色（管理员/普通用户）
- ✅ **下拉菜单功能** - 个人资料、设置、退出登录按钮正常工作

### 新增页面
- ✅ **个人资料页面** (`/profile`) - 显示用户详细信息和权限
- ✅ **设置页面** (`/settings`) - 用户偏好和系统设置
- ✅ **开发模式提示** - 在开发环境显示特殊标识

## 免密码登录功能

### 登录页面增强
- ✅ **快速登录按钮** - 在开发模式下显示"🚀 快速登录（免密码）"按钮
- ✅ **预填充账号** - 自动填入 `<EMAIL>` 和 `dev123456`
- ✅ **开发模式提示** - 页面顶部显示开发模式状态
- ✅ **一键登录** - 点击快速登录按钮即可免密码登录

### 登录方式
1. **快速登录**：点击"🚀 快速登录（免密码）"按钮
2. **表单登录**：使用预填充的账号密码点击"登录"
3. **任意登录**：在开发模式下，任何邮箱都可以免密码登录

## 使用方法

### 开发环境启动
1. **设置环境变量**：确保 `.env.development` 文件存在
2. **启动前端**：`npm run dev` (自动加载开发配置)
3. **启动后端**：确保设置了 `DEV_MODE=true` 和 `BYPASS_AUTH=true`
4. **访问登录页**：`http://localhost:3000/login`
5. **快速登录**：点击快速登录按钮或使用预填充账号

### 生产环境
1. **移除开发配置**：删除或设置 `DEV_MODE=false`
2. **启用权限检查**：正常的JWT认证和权限验证
3. **用户登录**：需要真实的用户认证

## 测试和验证

### 开发模式测试页面
访问 `http://localhost:3000/dev-test` 可以查看：
- ✅ **开发模式状态** - 环境变量和配置状态
- ✅ **用户信息** - 当前登录用户详情
- ✅ **权限信息** - 权限级别和可用权限列表
- ✅ **功能测试** - 测试API调用和页面访问
- ✅ **环境变量** - 显示所有相关环境变量

### 测试流程
1. **访问登录页** → 看到开发模式提示和快速登录按钮
2. **点击快速登录** → 免密码登录成功
3. **访问测试页** → 验证所有功能正常
4. **测试权限** → 确认拥有管理员权限
5. **测试功能** → 访问各个受保护的页面

## 下一步建议

1. **运行数据库迁移**: 执行 `004_add_user_permission_field.sql`
2. **开发测试**: 使用开发模式进行功能开发和测试
3. **验证功能**: 使用 `/dev-test` 页面验证所有功能
4. **生产部署**: 配置生产环境的JWT密钥和权限设置
5. **功能完善**: 添加更多用户管理和权限控制功能

## 故障排除

1. **401错误**: 检查JWT token是否正确传递
2. **403错误**: 验证用户角色和权限配置
3. **登录失败**: 检查用户凭据和数据库连接
4. **权限异常**: 确认角色权限分配正确

系统现在具备了完整的认证和权限管理能力，可以安全地支持多用户访问和数据保护。
