"use client"

import React, { createContext, useContext, useEffect, useState } from "react"
import { useAuth } from "./use-auth"
import { apiClient } from "@/lib/api"

// 权限定义
export const PERMISSIONS = {
  // 数据集权限
  DATASET_READ: "dataset:read",
  DATASET_WRITE: "dataset:write",
  DATASET_ADMIN: "dataset:admin",
  
  // 查询权限
  QUERY_EXECUTE: "query:execute",
  QUERY_SAVE: "query:save",
  QUERY_DELETE: "query:delete",
  QUERY_SHARE: "query:share",
  
  // 导出权限
  EXPORT_CREATE: "export:create",
  EXPORT_DOWNLOAD: "export:download",
  
  // 用户管理权限
  USER_READ: "user:read",
  USER_WRITE: "user:write",
  USER_DELETE: "user:delete",
  
  // 系统管理权限
  SYSTEM_ADMIN: "system:admin",
  SYSTEM_CONFIG: "system:config",
} as const

// 角色定义
export const ROLES = {
  USER: "user",
  ADMIN: "admin",
} as const

export type Permission = typeof PERMISSIONS[keyof typeof PERMISSIONS]
export type Role = typeof ROLES[keyof typeof ROLES]

interface UserPermissions {
  permission: number // 0:无权限, 1:管理员, 2:普通用户
  roles: Role[]
  permissions: Permission[]
  datasets: string[]
}

interface PermissionContextType {
  permissions: UserPermissions | null
  loading: boolean
  hasPermission: (permission: Permission) => boolean
  hasRole: (role: Role) => boolean
  hasAnyRole: (roles: Role[]) => boolean
  canAccessDataset: (datasetId: string) => boolean
  isAdmin: () => boolean
  refreshPermissions: () => Promise<void>
}

const PermissionContext = createContext<PermissionContextType | undefined>(undefined)

export function PermissionProvider({ children }: { children: React.ReactNode }) {
  const [permissions, setPermissions] = useState<UserPermissions | null>(null)
  const [loading, setLoading] = useState(true)
  const { user, isAuthenticated } = useAuth()

  const fetchPermissions = async () => {
    // 检查开发模式
    const isDevMode = process.env.NEXT_PUBLIC_DEV_MODE === 'true'
    const bypassAuth = process.env.NEXT_PUBLIC_BYPASS_AUTH === 'true'

    if (bypassAuth && isDevMode) {
      // 开发模式：模拟管理员权限，绕过所有权限检查
      const devPermissions: UserPermissions = {
        permission: 1, // 管理员权限
        roles: [ROLES.ADMIN],
        permissions: Object.values(PERMISSIONS),
        datasets: ["mimic-iv", "eicu", "nhanes", "pic"],
      }
      setPermissions(devPermissions)
      setLoading(false)
      return
    }

    if (!isAuthenticated || !user) {
      setPermissions(null)
      setLoading(false)
      return
    }

    try {
      const response = await apiClient.get("/auth/permissions")
      if (response.data) {
        setPermissions(response.data)
      }
    } catch (error) {
      console.error("Failed to fetch permissions:", error)
      // 设置默认权限基于用户角色
      setPermissions(getDefaultPermissions(user.role as Role))
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchPermissions()
  }, [isAuthenticated, user])

  const hasPermission = (permission: Permission): boolean => {
    if (!permissions) return false
    // 简化版本：基于权限级别判断
    if (permissions.permission === 1) return true // 管理员有所有权限
    if (permissions.permission === 2) {
      // 普通用户的基础权限
      const userPermissions = [
        PERMISSIONS.DATASET_READ,
        PERMISSIONS.QUERY_EXECUTE,
        PERMISSIONS.QUERY_SAVE,
        PERMISSIONS.EXPORT_CREATE,
        PERMISSIONS.EXPORT_DOWNLOAD,
      ]
      return userPermissions.includes(permission)
    }
    return false
  }

  const hasRole = (role: Role): boolean => {
    if (!permissions) return false
    if (permissions.permission === 1 && role === ROLES.ADMIN) return true
    if (permissions.permission === 2 && role === ROLES.USER) return true
    return false
  }

  const hasAnyRole = (roles: Role[]): boolean => {
    if (!permissions) return false
    return roles.some(role => hasRole(role))
  }

  const canAccessDataset = (datasetId: string): boolean => {
    if (!permissions) return false
    // 有基础权限的用户都可以访问数据集
    return permissions.permission >= 2
  }

  const isAdmin = (): boolean => {
    return permissions?.permission === 1
  }

  const refreshPermissions = async () => {
    setLoading(true)
    await fetchPermissions()
  }

  return (
    <PermissionContext.Provider
      value={{
        permissions,
        loading,
        hasPermission,
        hasRole,
        hasAnyRole,
        canAccessDataset,
        isAdmin,
        refreshPermissions,
      }}
    >
      {children}
    </PermissionContext.Provider>
  )
}

export function usePermissions() {
  const context = useContext(PermissionContext)
  if (context === undefined) {
    throw new Error("usePermissions must be used within a PermissionProvider")
  }
  return context
}

// 获取角色的默认权限
function getDefaultPermissions(role: Role): UserPermissions {
  const rolePermissions: Record<Role, Permission[]> = {
    [ROLES.USER]: [
      PERMISSIONS.DATASET_READ,
      PERMISSIONS.QUERY_EXECUTE,
      PERMISSIONS.QUERY_SAVE,
      PERMISSIONS.EXPORT_CREATE,
      PERMISSIONS.EXPORT_DOWNLOAD,
    ],
    [ROLES.ADMIN]: Object.values(PERMISSIONS),
  }

  return {
    permission: role === ROLES.ADMIN ? 1 : 2, // 1:管理员, 2:普通用户
    roles: [role],
    permissions: rolePermissions[role] || [],
    datasets: ["mimic-iv", "eicu", "nhanes", "pic"], // 默认可访问所有数据集
  }
}

// 权限检查组件
interface PermissionGuardProps {
  permission?: Permission
  role?: Role
  anyRole?: Role[]
  fallback?: React.ReactNode
  children: React.ReactNode
}

export function PermissionGuard({
  permission,
  role,
  anyRole,
  fallback = null,
  children,
}: PermissionGuardProps) {
  const { hasPermission, hasRole, hasAnyRole, loading } = usePermissions()

  if (loading) {
    return <div className="animate-pulse">Loading...</div>
  }

  let hasAccess = true

  if (permission && !hasPermission(permission)) {
    hasAccess = false
  }

  if (role && !hasRole(role)) {
    hasAccess = false
  }

  if (anyRole && !hasAnyRole(anyRole)) {
    hasAccess = false
  }

  return hasAccess ? <>{children}</> : <>{fallback}</>
}

// 数据集访问检查组件
interface DatasetGuardProps {
  datasetId: string
  fallback?: React.ReactNode
  children: React.ReactNode
}

export function DatasetGuard({ datasetId, fallback = null, children }: DatasetGuardProps) {
  const { canAccessDataset, loading } = usePermissions()

  if (loading) {
    return <div className="animate-pulse">Loading...</div>
  }

  return canAccessDataset(datasetId) ? <>{children}</> : <>{fallback}</>
}

// 角色显示组件
export function RoleBadge({ role }: { role: Role }) {
  const roleNames: Record<Role, string> = {
    [ROLES.USER]: "普通用户",
    [ROLES.ADMIN]: "管理员",
  }

  const roleColors: Record<Role, string> = {
    [ROLES.USER]: "bg-blue-100 text-blue-800",
    [ROLES.ADMIN]: "bg-red-100 text-red-800",
  }

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${roleColors[role]}`}>
      {roleNames[role]}
    </span>
  )
}
