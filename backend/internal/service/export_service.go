package service

import (
	"context"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"github.com/google/uuid"

	"github.com/chungzy/medical-data-platform/internal/model"
	"github.com/chungzy/medical-data-platform/internal/repository"
)

type ExportService struct {
	queryRepo    repository.QueryRepository
	exportRepo   repository.ExportRepository
	queryService *QueryService
	exportDir    string
}

func NewExportService(queryRepo repository.QueryRepository, exportRepo repository.ExportRepository, queryService *QueryService) *ExportService {
	exportDir := "exports"
	os.MkdirAll(exportDir, 0755)

	return &ExportService{
		queryRepo:    queryRepo,
		exportRepo:   exportRepo,
		queryService: queryService,
		exportDir:    exportDir,
	}
}

// CreateExport 创建导出任务
func (s *ExportService) CreateExport(ctx context.Context, userID uuid.UUID, req *model.ExportRequest) (*model.ExportRecord, error) {
	// 获取查询历史
	queryHistory, err := s.queryService.GetQueryHistoryByID(ctx, req.QueryID)
	if err != nil {
		return nil, fmt.Errorf("query not found: %w", err)
	}

	// 检查用户权限
	if queryHistory.UserID != userID {
		return nil, fmt.Errorf("permission denied")
	}

	// 创建导出记录
	exportRecord := &model.ExportRecord{
		ID:         uuid.New(),
		UserID:     userID,
		QueryID:    &req.QueryID,
		ExportType: req.Format,
		Status:     "pending",
		ExpiresAt:  time.Now().Add(7 * 24 * time.Hour), // 7天后过期
		CreatedAt:  time.Now(),
	}

	// 保存导出记录
	if err := s.exportRepo.Create(ctx, exportRecord); err != nil {
		return nil, err
	}

	// 异步处理导出
	go s.processExport(ctx, exportRecord, queryHistory, req.Fields)

	return exportRecord, nil
}

// CreateEnhancedExport 创建增强导出任务
func (s *ExportService) CreateEnhancedExport(userID, queryID, format string, options map[string]interface{}) (*model.ExportRecord, error) {
	ctx := context.Background()

	// 创建导出记录
	exportRecord := &model.ExportRecord{
		ID:         uuid.New(),
		UserID:     uuid.MustParse(userID),
		ExportType: format,
		Status:     "pending",
		ExpiresAt:  time.Now().Add(7 * 24 * time.Hour), // 7天后过期
		CreatedAt:  time.Now(),
	}

	// 如果queryID不为空，设置QueryID
	if queryID != "" {
		queryUUID := uuid.MustParse(queryID)
		exportRecord.QueryID = &queryUUID
	}

	// 保存导出记录
	if err := s.exportRepo.Create(ctx, exportRecord); err != nil {
		return nil, fmt.Errorf("failed to create export record: %w", err)
	}

	// 启动异步导出任务
	go s.processEnhancedExport(exportRecord, options)

	return exportRecord, nil
}

// processEnhancedExport 处理增强导出任务
func (s *ExportService) processEnhancedExport(record *model.ExportRecord, options map[string]interface{}) {
	ctx := context.Background()

	// 更新状态为处理中
	record.Status = "processing"
	s.exportRepo.Update(ctx, record)

	// 模拟导出过程
	time.Sleep(2 * time.Second)

	// 模拟导出成功
	record.Status = "completed"
	filePath := fmt.Sprintf("exports/enhanced_%s.%s", record.ID.String(), record.ExportType)
	record.FilePath = &filePath
	fileName := fmt.Sprintf("enhanced_export_%s.%s", record.ID.String(), record.ExportType)
	record.FileName = &fileName
	var fileSize int64 = 1024 // 模拟文件大小
	record.FileSize = &fileSize

	s.exportRepo.Update(ctx, record)
}

// processExport 处理导出任务
func (s *ExportService) processExport(ctx context.Context, exportRecord *model.ExportRecord, queryHistory *model.QueryHistory, fields []string) {
	// 更新状态为处理中
	exportRecord.Status = "processing"
	s.exportRepo.Update(ctx, exportRecord)

	// 重新执行查询获取数据
	var queryDSL model.QueryDSL
	if err := json.Unmarshal(queryHistory.QueryConfig, &queryDSL); err != nil {
		exportRecord.Status = "failed"
		s.exportRepo.Update(ctx, exportRecord)
		return
	}

	// 这里简化处理，实际应该重新构建SQL并执行
	data := []map[string]interface{}{
		{"id": 1, "name": "Sample Data", "value": 100},
		{"id": 2, "name": "Test Data", "value": 200},
	}

	// 根据格式导出数据
	var filePath string
	var err error

	switch exportRecord.ExportType {
	case "csv":
		filePath, err = s.exportToCSV(data, fields, exportRecord.ID.String())
	case "json":
		filePath, err = s.exportToJSON(data, fields, exportRecord.ID.String())
	default:
		err = fmt.Errorf("unsupported export format: %s", exportRecord.ExportType)
	}

	if err != nil {
		exportRecord.Status = "failed"
		s.exportRepo.Update(ctx, exportRecord)
		return
	}

	// 获取文件信息
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		exportRecord.Status = "failed"
		s.exportRepo.Update(ctx, exportRecord)
		return
	}

	// 更新导出记录
	exportRecord.Status = "completed"
	exportRecord.FilePath = &filePath
	fileName := fileInfo.Name()
	exportRecord.FileName = &fileName
	fileSize := fileInfo.Size()
	exportRecord.FileSize = &fileSize

	s.exportRepo.Update(ctx, exportRecord)
}

// exportToCSV 导出为CSV格式
func (s *ExportService) exportToCSV(data []map[string]interface{}, fields []string, exportID string) (string, error) {
	fileName := fmt.Sprintf("export_%s.csv", exportID)
	filePath := filepath.Join(s.exportDir, fileName)

	file, err := os.Create(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	writer := csv.NewWriter(file)
	defer writer.Flush()

	if len(data) == 0 {
		return filePath, nil
	}

	// 确定要导出的字段
	var headers []string
	if len(fields) > 0 {
		headers = fields
	} else {
		// 使用第一行数据的所有字段
		for key := range data[0] {
			headers = append(headers, key)
		}
	}

	// 写入表头
	if err := writer.Write(headers); err != nil {
		return "", err
	}

	// 写入数据
	for _, row := range data {
		var record []string
		for _, header := range headers {
			value := ""
			if val, exists := row[header]; exists && val != nil {
				value = fmt.Sprintf("%v", val)
			}
			record = append(record, value)
		}
		if err := writer.Write(record); err != nil {
			return "", err
		}
	}

	return filePath, nil
}

// exportToJSON 导出为JSON格式
func (s *ExportService) exportToJSON(data []map[string]interface{}, fields []string, exportID string) (string, error) {
	fileName := fmt.Sprintf("export_%s.json", exportID)
	filePath := filepath.Join(s.exportDir, fileName)

	// 过滤字段
	var filteredData []map[string]interface{}
	if len(fields) > 0 {
		for _, row := range data {
			filteredRow := make(map[string]interface{})
			for _, field := range fields {
				if val, exists := row[field]; exists {
					filteredRow[field] = val
				}
			}
			filteredData = append(filteredData, filteredRow)
		}
	} else {
		filteredData = data
	}

	file, err := os.Create(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "  ")
	if err := encoder.Encode(filteredData); err != nil {
		return "", err
	}

	return filePath, nil
}

// GetExportStatus 获取导出状态
func (s *ExportService) GetExportStatus(ctx context.Context, exportID uuid.UUID, userID uuid.UUID) (*model.ExportRecord, error) {
	exportRecord, err := s.exportRepo.GetByID(ctx, exportID)
	if err != nil {
		return nil, err
	}

	// 检查用户权限
	if exportRecord.UserID != userID {
		return nil, fmt.Errorf("permission denied")
	}

	return exportRecord, nil
}

// GetDownloadInfo 获取下载信息
func (s *ExportService) GetDownloadInfo(ctx context.Context, exportID uuid.UUID, userID uuid.UUID) (string, string, error) {
	exportRecord, err := s.GetExportStatus(ctx, exportID, userID)
	if err != nil {
		return "", "", err
	}

	if exportRecord.Status != "completed" {
		return "", "", fmt.Errorf("export not completed")
	}

	if exportRecord.FilePath == nil || exportRecord.FileName == nil {
		return "", "", fmt.Errorf("export file not found")
	}

	// 检查文件是否过期
	if time.Now().After(exportRecord.ExpiresAt) {
		return "", "", fmt.Errorf("export file expired")
	}

	// 检查文件是否存在
	if _, err := os.Stat(*exportRecord.FilePath); os.IsNotExist(err) {
		return "", "", fmt.Errorf("export file not found")
	}

	return *exportRecord.FilePath, *exportRecord.FileName, nil
}

// IncrementDownloadCount 增加下载次数
func (s *ExportService) IncrementDownloadCount(ctx context.Context, exportID uuid.UUID) error {
	return s.exportRepo.IncrementDownloadCount(ctx, exportID)
}
