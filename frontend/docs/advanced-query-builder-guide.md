# 高级查询构建器使用指南

## 🎯 概述

高级查询构建器是一个现代化、直观的医学数据查询工具，支持多数据集查询，提供步骤式的查询构建体验。

## 🚀 主要特性

### ✨ **核心功能**
- **多数据集支持**: MIMIC-IV、eICU、NHANES、PIC等
- **步骤式导航**: 6个清晰的构建步骤
- **智能完整性检查**: 实时查询完整性指示器
- **可视化预览**: SQL和DSL双重预览
- **丰富的导出格式**: CSV、JSON、TXT等

### 🎨 **用户体验**
- **渐进式披露**: 适合不同技能水平的用户
- **实时反馈**: 即时验证和建议
- **历史记录**: 查询历史和快速重用
- **响应式设计**: 支持桌面和移动设备

## 📋 使用步骤

### 步骤1: 选择数据集
1. 浏览可用的医学数据集
2. 查看数据集详细信息（版本、表数量、特性等）
3. 根据研究需求选择合适的数据集
4. 检查数据集状态和访问权限

**支持的数据集:**
- **MIMIC-IV**: 重症监护医学信息数据库 (382,278 患者)
- **eICU-CRD**: 多中心重症监护协作研究数据库 (139,367 患者)
- **NHANES**: 国家健康与营养调查 (9,254 参与者)
- **PIC**: 儿科重症监护数据库 (12,881 患者)

### 步骤2: 定义研究人群
构建研究队列的筛选条件：

#### 人口统计学条件
- **年龄范围**: 设置最小/最大年龄
- **性别**: 选择男性/女性
- **种族**: 选择特定种族群体

#### 时间条件
- **入院时间范围**: 设置研究时间窗口
- **住院时长**: 设置最小住院天数
- **随访时间**: 设置观察期长度

#### 临床条件
- **生命体征**: 心率、血压、体温等
- **实验室指标**: 血糖、肌酐、血红蛋白等
- **诊断条件**: ICD编码、疾病分类

### 步骤3: 选择字段
从数据集中选择需要的数据字段：

#### 字段分类
- **人口统计学**: 患者ID、年龄、性别等
- **时间信息**: 入院时间、出院时间等
- **临床测量**: 生命体征、实验室检查等
- **诊断信息**: 疾病诊断、ICD编码等
- **药物治疗**: 处方药物、剂量等
- **医疗程序**: 手术、操作等
- **管理信息**: 保险、入院类型等

#### 字段操作
- **搜索过滤**: 快速找到需要的字段
- **批量选择**: 按分类批量添加字段
- **字段预览**: 查看字段详细信息和示例
- **依赖检查**: 自动检查字段依赖关系

### 步骤4: 设置过滤条件
添加精确的数据过滤条件：

#### 支持的操作符
- **字符串**: 等于、包含、在列表中等
- **数值**: 大于、小于、在范围内等
- **日期**: 早于、晚于、在日期范围内等
- **布尔**: True/False、为空/不为空

#### 逻辑组合
- **AND**: 所有条件都必须满足
- **OR**: 任一条件满足即可
- **复杂组合**: 支持多层逻辑嵌套

### 步骤5: 预览查询
验证和优化查询：

#### 查询分析
- **复杂度评估**: 简单/中等/复杂
- **性能预估**: 预计执行时间
- **资源评估**: 内存和计算需求

#### 预览格式
- **DSL格式**: 结构化查询描述语言
- **SQL预览**: 生成的SQL语句
- **查询摘要**: 人类可读的查询描述

#### 验证检查
- **字段验证**: 检查字段有效性
- **条件验证**: 验证过滤条件逻辑
- **性能建议**: 优化建议和警告

### 步骤6: 查看结果
分析和导出查询结果：

#### 结果展示
- **数据表格**: 可排序、可搜索的数据表
- **统计信息**: 字段统计和数据质量
- **原始数据**: JSON格式的完整数据

#### 数据操作
- **搜索过滤**: 在结果中搜索特定内容
- **排序**: 按任意字段排序
- **分页**: 处理大量结果数据

#### 导出选项
- **CSV**: 适合Excel和统计软件
- **JSON**: 适合程序化处理
- **TXT**: 纯文本格式

## 🔧 高级功能

### 查询优化
- **自动索引建议**: 提升查询性能
- **查询重写**: 优化复杂查询
- **缓存策略**: 重用常见查询结果

### 协作功能
- **查询分享**: 生成分享链接
- **版本控制**: 查询历史和版本管理
- **团队协作**: 多用户协同编辑

### 数据质量
- **缺失值检测**: 识别数据缺失模式
- **异常值检测**: 发现数据异常
- **一致性检查**: 验证数据一致性

## 🎨 界面说明

### 主界面布局
```
┌─────────────────────────────────────────────────────────┐
│ 顶部工具栏: 数据集标识 | 完整性指示器 | 操作按钮        │
├─────────────┬───────────────────────────────────────────┤
│ 左侧导航    │ 主要内容区域                              │
│ - 步骤列表  │ - 数据集选择                              │
│ - 进度指示  │ - 人群构建                                │
│ - 查询历史  │ - 字段选择                                │
│             │ - 过滤设置                                │
│             │ - 查询预览                                │
│             │ - 结果展示                                │
└─────────────┴───────────────────────────────────────────┘
```

### 状态指示器
- **🟢 完成**: 步骤已完成
- **🔵 当前**: 正在进行的步骤
- **⚪ 待完成**: 尚未开始的步骤
- **🔴 禁用**: 不可访问的步骤

### 完整性指示器
- **0-40%**: 🔴 需要更多信息
- **40-70%**: 🟡 基本可执行
- **70-100%**: 🟢 完整可执行

## 🚨 注意事项

### 性能考虑
- **字段限制**: 建议选择不超过50个字段
- **结果限制**: 大型查询建议设置合理的LIMIT
- **复杂度控制**: 避免过于复杂的过滤条件

### 数据安全
- **访问控制**: 遵守数据集访问权限
- **数据脱敏**: 敏感数据自动脱敏处理
- **审计日志**: 所有查询操作都有记录

### 最佳实践
- **渐进式构建**: 从简单查询开始，逐步增加复杂度
- **结果验证**: 始终验证查询结果的合理性
- **文档记录**: 为重要查询添加描述和注释

## 🔗 相关资源

### 数据集文档
- [MIMIC-IV 官方文档](https://mimic.mit.edu/)
- [eICU-CRD 数据字典](https://eicu-crd.mit.edu/)
- [NHANES 数据指南](https://www.cdc.gov/nchs/nhanes/)

### 技术支持
- **在线帮助**: 界面内置帮助系统
- **API文档**: 完整的API参考文档
- **社区论坛**: 用户交流和问题解答

### 更新日志
- **v1.0.0**: 基础查询构建功能
- **v1.1.0**: 多数据集支持
- **v1.2.0**: 高级过滤器和可视化
- **v1.3.0**: 协作功能和性能优化

---

**提示**: 如果您在使用过程中遇到任何问题，请查看内置帮助或联系技术支持团队。
