# 医学数据分析平台增强配置文件
# Enhanced Medical Data Platform Configuration

# 应用程序配置
app:
  name: "Enhanced Medical Data Platform"
  version: "2.0.0"
  environment: "development"  # development, production, testing
  log_level: "info"          # debug, info, warn, error
  max_query_results: 50000   # 增加查询结果限制
  export_file_retention_days: 14  # 增加导出文件保留时间
  max_concurrent_queries: 10      # 增加并发查询数
  
  # 日志配置
  log:
    dir: "logs"
    max_size: 100
    max_backups: 10
    max_age: 30
    compress: true
    enable_console: true
    enable_structured: true  # 启用结构化日志

  # 文件清理配置
  file_cleanup:
    enable: true
    interval: "12h"  # 更频繁的清理
    export_dir: "exports"
    temp_dir: "tmp"
    cleanup_on_startup: true

# 服务器配置
server:
  host: "0.0.0.0"  # 监听所有接口
  port: "8088"
  mode: "debug"

# 主数据库配置（平台元数据）
database:
  host: "localhost"
  port: 5432
  user: "medical_platform"
  password: "platform_password_2024"
  dbname: "medical_data_platform"
  sslmode: "disable"

# 医学数据源配置
datasources:
  # MIMIC-IV数据库
  mimic_iv:
    enabled: true
    type: "postgresql"
    host: "mimic-db.example.com"
    port: 5432
    user: "mimic_readonly"
    password: "mimic_secure_password"
    dbname: "mimic_iv"
    sslmode: "require"
    schema: "mimiciv_hosp,mimiciv_icu,mimiciv_ed"
    description: "MIMIC-IV Critical Care Database v2.2"
    max_connections: 20
    connection_timeout: 60
    query_timeout: 600  # 10分钟查询超时
    
  # eICU数据库
  eicu:
    enabled: true
    type: "postgresql"
    host: "eicu-db.example.com"
    port: 5432
    user: "eicu_readonly"
    password: "eicu_secure_password"
    dbname: "eicu_crd"
    sslmode: "require"
    schema: "eicu_crd"
    description: "eICU Collaborative Research Database v2.0"
    max_connections: 15
    connection_timeout: 60
    query_timeout: 600
    
  # NHANES数据库
  nhanes:
    enabled: true
    type: "postgresql"
    host: "nhanes-db.example.com"
    port: 5432
    user: "nhanes_readonly"
    password: "nhanes_secure_password"
    dbname: "nhanes"
    sslmode: "require"
    schema: "nhanes"
    description: "National Health and Nutrition Examination Survey 2017-2020"
    max_connections: 10
    connection_timeout: 60
    query_timeout: 300
    
  # PIC数据库
  pic:
    enabled: false  # 暂时禁用
    type: "postgresql"
    host: "pic-db.example.com"
    port: 5432
    user: "pic_readonly"
    password: "pic_secure_password"
    dbname: "pic"
    sslmode: "require"
    schema: "pic"
    description: "Pediatric Intensive Care Database v1.0"
    max_connections: 5
    connection_timeout: 60
    query_timeout: 300

# JWT配置
jwt:
  secret_key: "enhanced-jwt-secret-key-change-in-production-2024"
  expiration_hours: 24

# 查询引擎配置
query_engine:
  # 缓存配置
  cache:
    enabled: true
    type: "memory"  # memory, redis
    default_timeout: 300  # 5分钟
    max_entries: 1000
    
  # 查询优化配置
  optimization:
    enabled: true
    max_limit: 50000
    default_limit: 1000
    enable_query_plan: true
    enable_performance_hints: true
    
  # 并发控制
  concurrency:
    max_concurrent_queries: 10
    query_queue_size: 100
    timeout_seconds: 600

# 导出配置
export:
  # 支持的格式
  formats: ["csv", "json", "excel", "parquet"]
  
  # 大小限制
  max_file_size: 104857600  # 100MB
  max_rows: 1000000         # 100万行
  
  # 默认设置
  default_expiration_days: 14
  compression_enabled: true
  
  # 格式特定配置
  csv:
    include_bom: true
    include_header: true
    delimiter: ","
    
  json:
    pretty_print: true
    array_format: true
    
  excel:
    max_sheets: 10
    max_rows_per_sheet: 100000

# 安全配置
security:
  cors:
    allowed_origins: 
      - "http://localhost:3000"
      - "http://localhost:8088"
      - "https://medical-platform.example.com"
    allowed_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    allowed_headers: ["Content-Type", "Authorization", "X-User-ID"]
    allow_credentials: true
    
  rate_limiting:
    enabled: true
    requests_per_minute: 200
    burst_size: 20
    
  # API密钥配置（用于服务间调用）
  api_keys:
    enabled: false
    keys: []

# 监控配置
monitoring:
  # 健康检查
  health_check:
    enabled: true
    interval: "30s"
    timeout: "10s"
    
  # 指标收集
  metrics:
    enabled: true
    endpoint: "/metrics"
    
  # 性能分析
  profiling:
    enabled: false  # 生产环境建议关闭
    endpoint: "/debug/pprof"

# 数据集特定配置
dataset_configs:
  mimic_iv:
    # 表别名映射
    table_aliases:
      patients: "p"
      admissions: "a"
      icustays: "i"
      chartevents: "c"
      labevents: "l"
      
    # 常用JOIN规则
    auto_joins:
      enabled: true
      rules:
        - from: "patients"
          to: "admissions"
          condition: "p.subject_id = a.subject_id"
        - from: "admissions"
          to: "icustays"
          condition: "a.hadm_id = i.hadm_id"
          
    # 查询优化提示
    optimization_hints:
      - "Use indexes on subject_id, hadm_id, stay_id"
      - "Consider time-based partitioning for large queries"
      
  eicu:
    table_aliases:
      patient: "pt"
      admissiondx: "dx"
      lab: "lab"
      
    auto_joins:
      enabled: true
      rules:
        - from: "patient"
          to: "admissiondx"
          condition: "pt.patientunitstayid = dx.patientunitstayid"

# 开发配置
development:
  # 调试模式
  debug:
    enabled: true
    log_sql: true
    log_performance: true
    
  # 测试数据
  sample_data:
    enabled: true
    max_rows: 100
    
  # 开发工具
  dev_tools:
    enabled: true
    sql_console: true
    query_profiler: true
