# 医学数据分析平台增强架构

## 概述

本文档描述了医学数据分析平台的增强架构设计，该架构支持多数据源、可扩展的查询构建和高性能的数据处理。

## 核心架构

### 分层设计

```
┌─────────────────────────────────────────────────────────────┐
│                    API Layer                                │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │   REST APIs     │  │   GraphQL       │                  │
│  └─────────────────┘  └─────────────────┘                  │
├─────────────────────────────────────────────────────────────┤
│                  Service Layer                              │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │  Query Service  │  │  Export Service │  ...             │
│  └─────────────────┘  └─────────────────┘                  │
├─────────────────────────────────────────────────────────────┤
│              Enhanced Query Builder                         │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │   DSL Parser    │  │  Query Optimizer │                  │
│  └─────────────────┘  └─────────────────┘                  │
├─────────────────────────────────────────────────────────────┤
│              Dataset Adapter Layer                          │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │  MIMIC Adapter  │  │  eICU Adapter   │  ...             │
│  └─────────────────┘  └─────────────────┘                  │
├─────────────────────────────────────────────────────────────┤
│            Connection Manager                               │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │   MIMIC DB      │  │    eICU DB      │  ...             │
│  └─────────────────┘  └─────────────────┘                  │
└─────────────────────────────────────────────────────────────┘
```

## 核心组件

### 1. 连接管理器 (Connection Manager)

**位置**: `pkg/database/manager.go`

**功能**:
- 管理多个数据库连接
- 连接池管理和优化
- 健康检查和故障恢复
- 动态数据源添加/移除

**特性**:
- 支持多种数据库类型
- 连接池配置和监控
- 自动重连机制
- 负载均衡支持

### 2. 数据源适配器 (Dataset Adapters)

**位置**: `internal/adapter/`

**接口定义**: `internal/adapter/interface.go`

**功能**:
- 统一的数据访问接口
- 数据集特定的查询优化
- 字段和表映射
- 元数据管理

**已实现适配器**:
- **MIMIC-IV Adapter** (`mimic_adapter.go`)
  - 支持MIMIC-IV数据库的所有表
  - 智能JOIN规则
  - 字段映射和别名
  - 查询优化建议

**待实现适配器**:
- eICU Adapter
- NHANES Adapter
- PIC Adapter

### 3. 增强查询构建器 (Enhanced Query Builder)

**位置**: `internal/query/builder.go`

**功能**:
- DSL到SQL的转换
- 查询优化和重写
- 缓存管理
- 性能分析

**特性**:
- 支持复杂查询条件
- 自动查询优化
- 结果缓存
- 查询计划生成

### 4. 预览服务 (Preview Service)

**位置**: `internal/service/preview_service.go`

**功能**:
- 数据表预览
- 查询结果预览
- 数据集概览
- 样本数据获取

**API端点**:
- `POST /api/preview/table` - 表数据预览
- `POST /api/preview/query` - 查询预览
- `GET /api/preview/dataset/:dataset/overview` - 数据集概览
- `POST /api/preview/sample` - 样本数据

### 5. 增强导出服务 (Enhanced Export Service)

**位置**: `internal/service/enhanced_export_v2.go`

**功能**:
- 多格式导出支持
- 大数据集处理
- 异步导出处理
- 导出进度跟踪

**支持格式**:
- CSV (带BOM支持)
- JSON (格式化选项)
- Excel (多工作表)
- Parquet (高压缩比)

## 配置系统

### 多数据源配置

```yaml
datasources:
  mimic_iv:
    enabled: true
    type: "postgresql"
    host: "mimic-db.example.com"
    port: 5432
    user: "mimic_readonly"
    password: "mimic_secure_password"
    dbname: "mimic_iv"
    sslmode: "require"
    schema: "mimiciv_hosp,mimiciv_icu,mimiciv_ed"
    max_connections: 20
    connection_timeout: 60
    query_timeout: 600
```

### 查询引擎配置

```yaml
query_engine:
  cache:
    enabled: true
    default_timeout: 300
    max_entries: 1000
  optimization:
    enabled: true
    max_limit: 50000
    default_limit: 1000
```

## API 接口

### 新增API端点

#### 预览相关
- `POST /api/preview/table` - 表预览
- `POST /api/preview/query` - 查询预览
- `GET /api/preview/dataset/:dataset/overview` - 数据集概览

#### 增强功能
- `POST /api/enhanced/export` - 创建导出任务
- `GET /api/enhanced/export/:id/status` - 导出状态
- `POST /api/enhanced/query/validate` - 查询验证
- `POST /api/enhanced/query/plan` - 查询计划

#### 系统监控
- `GET /api/health` - 健康检查
- `GET /api/datasources/status` - 数据源状态

## 部署指南

### 1. 环境准备

```bash
# 安装依赖
go mod download

# 创建配置文件
cp configs/config.enhanced.yaml configs/config.yaml

# 编辑配置文件，设置数据库连接信息
vim configs/config.yaml
```

### 2. 数据库配置

确保以下数据库可访问：
- 主数据库（平台元数据）
- MIMIC-IV数据库
- eICU数据库（可选）
- NHANES数据库（可选）

### 3. 启动服务

```bash
# 开发模式
make run

# 生产模式
make build
./bin/server
```

### 4. 验证部署

```bash
# 健康检查
curl http://localhost:8000/api/health

# 数据源状态
curl http://localhost:8000/api/datasources/status
```

## 性能优化

### 1. 查询优化

- **自动LIMIT添加**: 防止大结果集
- **字段去重**: 移除重复选择字段
- **索引提示**: 基于数据集特性的优化建议

### 2. 缓存策略

- **查询结果缓存**: 相同查询的结果缓存
- **元数据缓存**: 表结构和字段信息缓存
- **连接池**: 数据库连接复用

### 3. 并发控制

- **查询队列**: 限制并发查询数量
- **超时控制**: 防止长时间运行的查询
- **资源监控**: 实时监控系统资源使用

## 扩展指南

### 添加新数据源

1. **实现适配器接口**:
```go
type DatasetAdapter interface {
    GetName() string
    GetDescription() string
    BuildQuery(dsl *model.QueryDSL) (string, []interface{}, error)
    ExecuteQuery(ctx context.Context, sql string, args []interface{}) ([]map[string]interface{}, error)
    // ... 其他方法
}
```

2. **注册适配器**:
```go
// 在 enhanced_app.go 中添加
case "new_dataset":
    datasetAdapter = adapter.NewCustomAdapter(db)
```

3. **配置数据源**:
```yaml
datasources:
  new_dataset:
    enabled: true
    type: "postgresql"
    # ... 其他配置
```

### 添加新导出格式

1. **实现导出方法**:
```go
func (s *EnhancedExportServiceV2) exportToNewFormat(data []map[string]interface{}, fields []string, exportID string, options *ExportOptions) (string, error) {
    // 实现导出逻辑
}
```

2. **注册格式**:
```go
supportFormats: []string{"csv", "json", "excel", "parquet", "new_format"}
```

## 监控和日志

### 日志配置

```yaml
app:
  log:
    enable_structured: true  # JSON格式日志
    level: "info"
```

### 监控指标

- 查询执行时间
- 数据库连接状态
- 缓存命中率
- 导出任务状态
- 系统资源使用

### 健康检查

系统提供多层次的健康检查：
- 数据库连接状态
- 适配器可用性
- 缓存系统状态
- 文件系统状态

## 安全考虑

### 1. 数据库安全

- 使用只读用户访问医学数据库
- SSL/TLS加密连接
- 连接超时和重试机制

### 2. API安全

- JWT认证
- CORS配置
- 速率限制
- 输入验证

### 3. 数据保护

- 导出文件自动过期
- 敏感信息脱敏
- 审计日志记录

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查网络连接
   - 验证数据库凭据
   - 查看防火墙设置

2. **查询超时**
   - 增加查询超时时间
   - 优化查询条件
   - 检查数据库性能

3. **导出失败**
   - 检查磁盘空间
   - 验证文件权限
   - 查看导出日志

### 日志分析

```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
grep "ERROR" logs/app.log

# 查看特定数据源的日志
grep "mimic_iv" logs/app.log
```

## 未来规划

### 短期目标

- [ ] 完成eICU适配器实现
- [ ] 添加Excel和Parquet导出支持
- [ ] 实现Redis缓存支持
- [ ] 添加查询性能分析

### 长期目标

- [ ] 支持分布式查询
- [ ] 机器学习模型集成
- [ ] 实时数据流处理
- [ ] 可视化查询构建器
