package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/chungzy/medical-data-platform/internal/model"
	"github.com/chungzy/medical-data-platform/internal/repository"
	"github.com/chungzy/medical-data-platform/pkg/logger"
	"github.com/google/uuid"
)

type MedicalQueryService struct {
	medicalQueryRepo repository.MedicalQueryRepository
	queryRepo        repository.QueryRepository
	dictionaryRepo   repository.DictionaryRepository
	datasetRepo      repository.DatasetRepository
	fieldService     *MedicalFieldService
}

func NewMedicalQueryService(
	medicalQueryRepo repository.MedicalQueryRepository,
	queryRepo repository.QueryRepository,
	dictionaryRepo repository.DictionaryRepository,
	datasetRepo repository.DatasetRepository,
	fieldService *MedicalFieldService,
) *MedicalQueryService {
	return &MedicalQueryService{
		medicalQueryRepo: medicalQueryRepo,
		queryRepo:        queryRepo,
		dictionaryRepo:   dictionaryRepo,
		datasetRepo:      datasetRepo,
		fieldService:     fieldService,
	}
}

// GetMedicalCategories 获取医学字段分类
func (mqs *MedicalQueryService) GetMedicalCategories(ctx context.Context) ([]model.MedicalFieldCategory, error) {
	logger.Info("Getting medical field categories")

	// 优先使用专门的字段服务
	if mqs.fieldService != nil {
		categories, err := mqs.fieldService.GetMedicalFieldCategories(ctx, "mimic-iv")
		if err == nil && len(categories) > 0 {
			logger.Info("Retrieved medical categories from field service", map[string]interface{}{
				"category_count": len(categories),
			})
			return categories, nil
		}
		logger.Warn("Failed to get categories from field service", map[string]interface{}{
			"error": err,
		})
	}

	// 次选：从数据库获取字段分类
	categories, err := mqs.getMedicalCategoriesFromDB(ctx)
	if err == nil && len(categories) > 0 {
		logger.Info("Retrieved medical categories from database", map[string]interface{}{
			"category_count": len(categories),
		})
		return categories, nil
	}

	logger.Warn("Failed to get categories from database, using mock data", map[string]interface{}{
		"error": err,
	})

	// 降级到预定义的医学分类
	categories = []model.MedicalFieldCategory{
		{
			ID:          "demographics",
			Name:        "基本信息",
			Description: "患者基本人口学信息",
			Icon:        "users",
			Fields: []model.MedicalField{
				{
					ID:          "age",
					Name:        "年龄",
					NameEn:      "anchor_age",
					Type:        "number",
					Description: "患者年龄",
					Table:       "patients",
					Category:    "demographics",
					Unit:        stringPtr("岁"),
					Examples:    []string{"65", "76", "89"},
				},
				{
					ID:          "gender",
					Name:        "性别",
					NameEn:      "gender",
					Type:        "string",
					Description: "患者性别",
					Table:       "patients",
					Category:    "demographics",
					Examples:    []string{"M", "F"},
				},
			},
		},
		{
			ID:          "admission",
			Name:        "住院信息",
			Description: "住院相关信息",
			Icon:        "heart",
			Fields: []model.MedicalField{
				{
					ID:          "admission_type",
					Name:        "入院类型",
					NameEn:      "admission_type",
					Type:        "string",
					Description: "入院类型",
					Table:       "admissions",
					Category:    "admission",
					Examples:    []string{"EMERGENCY", "ELECTIVE", "URGENT"},
				},
				{
					ID:          "insurance",
					Name:        "保险类型",
					NameEn:      "insurance",
					Type:        "string",
					Description: "保险类型",
					Table:       "admissions",
					Category:    "admission",
				},
				{
					ID:          "admission_location",
					Name:        "入院科室",
					NameEn:      "admission_location",
					Type:        "string",
					Description: "入院科室",
					Table:       "admissions",
					Category:    "admission",
				},
			},
		},
		{
			ID:          "laboratory",
			Name:        "检验结果",
			Description: "实验室检查结果",
			Icon:        "test-tube",
			Fields: []model.MedicalField{
				{
					ID:          "lab_value",
					Name:        "检验值",
					NameEn:      "valuenum",
					Type:        "number",
					Description: "检验数值",
					Table:       "labevents",
					Category:    "laboratory",
				},
				{
					ID:          "lab_item",
					Name:        "检验项目",
					NameEn:      "itemid",
					Type:        "number",
					Description: "检验项目ID",
					Table:       "labevents",
					Category:    "laboratory",
				},
			},
		},
		{
			ID:          "diagnosis",
			Name:        "诊断信息",
			Description: "疾病诊断相关信息",
			Icon:        "brain",
			Fields: []model.MedicalField{
				{
					ID:          "icd_code",
					Name:        "ICD诊断码",
					NameEn:      "icd_code",
					Type:        "string",
					Description: "ICD诊断代码",
					Table:       "diagnoses_icd",
					Category:    "diagnosis",
				},
			},
		},
		{
			ID:          "medication",
			Name:        "用药信息",
			Description: "药物使用相关信息",
			Icon:        "pill",
			Fields: []model.MedicalField{
				{
					ID:          "drug",
					Name:        "药物名称",
					NameEn:      "drug",
					Type:        "string",
					Description: "药物名称",
					Table:       "prescriptions",
					Category:    "medication",
				},
			},
		},
	}

	return categories, nil
}

// ValidateCohort 验证人群筛选条件
func (mqs *MedicalQueryService) ValidateCohort(ctx context.Context, req *model.CohortValidationRequest) (*model.CohortValidationResponse, error) {
	logger.Info("Validating cohort criteria", map[string]interface{}{
		"criteria_count": len(req.Criteria),
	})

	startTime := time.Now()

	// 验证逻辑
	var warnings []string
	var errors []string
	isValid := true

	// 检查每个条件
	for i, criteria := range req.Criteria {
		// 验证字段是否存在
		if criteria.Field == "" {
			errors = append(errors, fmt.Sprintf("条件 %d: 字段不能为空", i+1))
			isValid = false
		}

		// 验证操作符
		validOperators := []string{"=", "!=", ">", "<", ">=", "<=", "LIKE", "IN"}
		if !contains(validOperators, criteria.Operator) {
			errors = append(errors, fmt.Sprintf("条件 %d: 无效的操作符 %s", i+1, criteria.Operator))
			isValid = false
		}

		// 验证值
		if criteria.Value == nil || criteria.Value == "" {
			errors = append(errors, fmt.Sprintf("条件 %d: 值不能为空", i+1))
			isValid = false
		}
	}

	// 模拟估算患者数量
	estimatedCount := 1250 // 这里应该根据实际条件查询数据库

	if estimatedCount > 10000 {
		warnings = append(warnings, "查询结果可能较大，建议添加更多筛选条件")
	}

	validationTime := int(time.Since(startTime).Milliseconds())

	return &model.CohortValidationResponse{
		IsValid:        isValid,
		EstimatedCount: estimatedCount,
		Warnings:       warnings,
		Errors:         errors,
		ValidationTime: validationTime,
	}, nil
}

// EstimateResults 估算查询结果
func (mqs *MedicalQueryService) EstimateResults(ctx context.Context, req *model.MedicalQueryRequest) (*model.ResultEstimationResponse, error) {
	logger.Info("Estimating query results", map[string]interface{}{
		"study_name":      req.StudyName,
		"cohort_criteria": len(req.CohortCriteria),
		"data_dimensions": len(req.DataDimensions),
	})

	// 计算复杂度分数
	complexityScore := calculateComplexityScore(req)

	// 估算行数
	estimatedRows := 1250 // 基于人群筛选条件估算

	// 估算数据大小
	estimatedSize := fmt.Sprintf("%.1fMB", float64(estimatedRows*len(req.DataDimensions)*50)/1024/1024)

	// 估算执行时间
	executionTime := complexityScore * 100 // 毫秒

	var warnings []string
	var recommendations []string

	if complexityScore > 7 {
		warnings = append(warnings, "查询复杂度较高，可能需要较长执行时间")
		recommendations = append(recommendations, "建议减少数据维度或添加更多筛选条件")
	}

	if estimatedRows > 5000 {
		warnings = append(warnings, "结果集较大，建议设置合适的记录数限制")
	}

	return &model.ResultEstimationResponse{
		EstimatedRows:   estimatedRows,
		EstimatedSize:   estimatedSize,
		ComplexityScore: complexityScore,
		ExecutionTime:   executionTime,
		Warnings:        warnings,
		Recommendations: recommendations,
		Metadata: map[string]interface{}{
			"tables_involved": getInvolvedTables(req),
			"joins_required":  len(req.DataDimensions) - 1,
		},
	}, nil
}

// ExecuteMedicalQuery 执行医学查询
func (mqs *MedicalQueryService) ExecuteMedicalQuery(ctx context.Context, userID uuid.UUID, req *model.MedicalQueryRequest) (*model.MedicalQueryResponse, error) {
	logger.Info("Executing medical query", map[string]interface{}{
		"user_id":    userID,
		"study_name": req.StudyName,
	})

	// 生成查询ID
	queryID := uuid.New()

	// 构建SQL查询
	sql, args, err := mqs.buildMedicalSQL(req)
	if err != nil {
		return nil, fmt.Errorf("failed to build SQL: %w", err)
	}

	// 保存查询历史
	queryConfig, _ := json.Marshal(req)
	history := &model.MedicalQueryHistory{
		ID:          queryID,
		UserID:      userID,
		StudyName:   req.StudyName,
		QueryConfig: queryConfig,
		Status:      "running",
		CreatedAt:   time.Now(),
	}

	if err := mqs.medicalQueryRepo.SaveQueryHistory(ctx, history); err != nil {
		logger.Error("Failed to save query history", err)
		// 不阻断查询执行
	}

	// 执行查询
	startTime := time.Now()
	data, total, err := mqs.queryRepo.ExecuteQueryWithCount(ctx, sql, args)
	executionTime := int(time.Since(startTime).Milliseconds())

	// 更新查询历史状态
	history.ExecutionTime = &executionTime
	if err != nil {
		history.Status = "failed"
		errorMsg := err.Error()
		history.ErrorMessage = &errorMsg
	} else {
		history.Status = "completed"
		history.RecordCount = &total
	}

	mqs.medicalQueryRepo.UpdateQueryHistory(ctx, history)

	if err != nil {
		return nil, fmt.Errorf("failed to execute query: %w", err)
	}

	return &model.MedicalQueryResponse{
		QueryID:       queryID,
		StudyName:     req.StudyName,
		Data:          data,
		Total:         total,
		ExecutionTime: executionTime,
		Status:        "completed",
		Metadata: map[string]interface{}{
			"sql":             sql,
			"complexity":      calculateComplexityScore(req),
			"tables_involved": getInvolvedTables(req),
		},
	}, nil
}

// 辅助函数
func stringPtr(s string) *string {
	return &s
}

func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

func calculateComplexityScore(req *model.MedicalQueryRequest) int {
	score := 1
	score += len(req.CohortCriteria)
	score += len(req.DataDimensions)
	if req.TimeRange.Start != nil || req.TimeRange.End != nil {
		score += 1
	}
	if score > 10 {
		score = 10
	}
	return score
}

func getInvolvedTables(req *model.MedicalQueryRequest) []string {
	tables := make(map[string]bool)
	tables["patients"] = true // 总是包含患者表

	for _, dimension := range req.DataDimensions {
		for _, field := range dimension.Fields {
			tables[field.Table] = true
		}
	}

	var result []string
	for table := range tables {
		result = append(result, table)
	}
	return result
}

func (mqs *MedicalQueryService) buildMedicalSQL(req *model.MedicalQueryRequest) (string, []interface{}, error) {
	var sql strings.Builder
	var args []interface{}
	argIndex := 1

	// 构建基础查询
	sql.WriteString("SELECT DISTINCT p.subject_id")

	// 添加选择的字段
	for _, dimension := range req.DataDimensions {
		for _, field := range dimension.Fields {
			sql.WriteString(fmt.Sprintf(", %s.%s", getTableAlias(field.Table), field.NameEn))
		}
	}

	// FROM 子句
	sql.WriteString(" FROM mimiciv_hosp.patients p")

	// 添加必要的 JOIN
	joinedTables := make(map[string]bool)
	joinedTables["patients"] = true

	for _, dimension := range req.DataDimensions {
		for _, field := range dimension.Fields {
			if !joinedTables[field.Table] {
				sql.WriteString(mqs.getJoinClause(field.Table))
				joinedTables[field.Table] = true
			}
		}
	}

	// WHERE 子句
	if len(req.CohortCriteria) > 0 {
		sql.WriteString(" WHERE ")
		for i, criteria := range req.CohortCriteria {
			if i > 0 {
				logic := "AND"
				if criteria.Logic != nil {
					logic = *criteria.Logic
				}
				sql.WriteString(fmt.Sprintf(" %s ", logic))
			}

			// 这里需要根据字段映射构建条件
			sql.WriteString(fmt.Sprintf("p.%s %s $%d", criteria.Field, criteria.Operator, argIndex))
			args = append(args, criteria.Value)
			argIndex++
		}
	}

	// 限制记录数
	if req.MaxRecords > 0 {
		sql.WriteString(fmt.Sprintf(" LIMIT %d", req.MaxRecords))
	}

	return sql.String(), args, nil
}

func getTableAlias(table string) string {
	aliases := map[string]string{
		"patients":      "p",
		"admissions":    "a",
		"labevents":     "l",
		"diagnoses_icd": "d",
		"prescriptions": "pr",
	}
	if alias, exists := aliases[table]; exists {
		return alias
	}
	return table
}

func (mqs *MedicalQueryService) getJoinClause(table string) string {
	joinClauses := map[string]string{
		"admissions":    " LEFT JOIN mimiciv_hosp.admissions a ON p.subject_id = a.subject_id",
		"labevents":     " LEFT JOIN mimiciv_hosp.labevents l ON p.subject_id = l.subject_id",
		"diagnoses_icd": " LEFT JOIN mimiciv_hosp.diagnoses_icd d ON p.subject_id = d.subject_id",
		"prescriptions": " LEFT JOIN mimiciv_hosp.prescriptions pr ON p.subject_id = pr.subject_id",
	}
	if clause, exists := joinClauses[table]; exists {
		return clause
	}
	return ""
}

// GetQueryTemplates 获取查询模板
func (mqs *MedicalQueryService) GetQueryTemplates(ctx context.Context, page, limit int, category string) ([]model.MedicalQueryTemplate, int, error) {
	logger.Info("Getting query templates", map[string]interface{}{
		"page":     page,
		"limit":    limit,
		"category": category,
	})

	templates, total, err := mqs.medicalQueryRepo.GetQueryTemplates(ctx, page, limit, category)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get query templates: %w", err)
	}

	return templates, total, nil
}

// SaveQueryTemplate 保存查询模板
func (mqs *MedicalQueryService) SaveQueryTemplate(ctx context.Context, userID uuid.UUID, req *model.SaveTemplateRequest) (*model.MedicalQueryTemplate, error) {
	logger.Info("Saving query template", map[string]interface{}{
		"user_id": userID,
		"name":    req.Name,
	})

	queryConfig, err := json.Marshal(req.QueryConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal query config: %w", err)
	}

	template := &model.MedicalQueryTemplate{
		ID:          uuid.New(),
		Name:        req.Name,
		Description: req.Description,
		Category:    req.Category,
		QueryConfig: queryConfig,
		CreatedBy:   userID,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		IsPublic:    req.IsPublic,
		UsageCount:  0,
		Tags:        req.Tags,
	}

	if err := mqs.medicalQueryRepo.SaveQueryTemplate(ctx, template); err != nil {
		return nil, fmt.Errorf("failed to save query template: %w", err)
	}

	return template, nil
}

// GetMedicalQueryHistory 获取医学查询历史
func (mqs *MedicalQueryService) GetMedicalQueryHistory(ctx context.Context, userID uuid.UUID, page, limit int) ([]model.MedicalQueryHistory, int, error) {
	logger.Info("Getting medical query history", map[string]interface{}{
		"user_id": userID,
		"page":    page,
		"limit":   limit,
	})

	history, total, err := mqs.medicalQueryRepo.GetQueryHistory(ctx, userID, page, limit)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get query history: %w", err)
	}

	return history, total, nil
}

// ExportQueryResults 导出查询结果
func (mqs *MedicalQueryService) ExportQueryResults(ctx context.Context, userID uuid.UUID, queryID uuid.UUID, format string) (*model.ExportResult, error) {
	logger.Info("Exporting query results", map[string]interface{}{
		"user_id":  userID,
		"query_id": queryID,
		"format":   format,
	})

	// 获取查询历史
	history, err := mqs.medicalQueryRepo.GetQueryHistoryByID(ctx, queryID)
	if err != nil {
		return nil, fmt.Errorf("failed to get query history: %w", err)
	}

	// 验证用户权限
	if history.UserID != userID {
		return nil, fmt.Errorf("unauthorized access to query results")
	}

	// 重新执行查询获取完整数据
	var queryConfig model.MedicalQueryRequest
	if err := json.Unmarshal(history.QueryConfig, &queryConfig); err != nil {
		return nil, fmt.Errorf("failed to parse query config: %w", err)
	}

	sql, args, err := mqs.buildMedicalSQL(&queryConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to build SQL: %w", err)
	}

	data, err := mqs.queryRepo.ExecuteQuery(ctx, sql, args)
	if err != nil {
		return nil, fmt.Errorf("failed to execute query: %w", err)
	}

	// 生成导出文件
	fileName := fmt.Sprintf("medical_query_%s_%d.%s", queryID.String()[:8], time.Now().Unix(), format)
	filePath := fmt.Sprintf("/tmp/exports/%s", fileName)

	// 这里应该实现实际的文件导出逻辑
	// 暂时返回模拟结果
	exportResult := &model.ExportResult{
		QueryID:     queryID,
		Format:      format,
		FilePath:    filePath,
		FileSize:    int64(len(data) * 100), // 模拟文件大小
		RecordCount: len(data),
		ExportedAt:  time.Now(),
		DownloadURL: fmt.Sprintf("/api/exports/download/%s", fileName),
		ExpiresAt:   time.Now().Add(24 * time.Hour),
	}

	return exportResult, nil
}

// getMedicalCategoriesFromDB 从数据库获取医学字段分类
func (mqs *MedicalQueryService) getMedicalCategoriesFromDB(ctx context.Context) ([]model.MedicalFieldCategory, error) {
	// 获取所有数据集
	datasets, err := mqs.datasetRepo.GetAll(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get datasets: %w", err)
	}

	// 构建分类映射
	categoryMap := make(map[string]*model.MedicalFieldCategory)

	// 预定义分类结构
	categories := []model.MedicalFieldCategory{
		{ID: "demographics", Name: "基本信息", Description: "患者基本人口学信息", Icon: "users"},
		{ID: "admission", Name: "住院信息", Description: "住院相关信息", Icon: "heart"},
		{ID: "laboratory", Name: "检验结果", Description: "实验室检查结果", Icon: "test-tube"},
		{ID: "diagnosis", Name: "诊断信息", Description: "疾病诊断相关信息", Icon: "brain"},
		{ID: "medication", Name: "用药信息", Description: "药物使用相关信息", Icon: "pill"},
		{ID: "vitals", Name: "生命体征", Description: "生命体征监护数据", Icon: "activity"},
	}

	// 初始化分类映射
	for i := range categories {
		categoryMap[categories[i].ID] = &categories[i]
		categories[i].Fields = []model.MedicalField{}
	}

	// 从每个数据集获取字段信息
	for _, dataset := range datasets {
		fields, err := mqs.datasetRepo.GetFields(ctx, dataset.ID)
		if err != nil {
			logger.Warn("Failed to get fields for dataset", map[string]interface{}{
				"dataset_id": dataset.ID,
				"error":      err,
			})
			continue
		}

		// 将字段分配到相应分类
		for _, field := range fields {
			categoryID := mqs.mapFieldToCategory(field.Category)
			if category, exists := categoryMap[categoryID]; exists {
				medicalField := model.MedicalField{
					ID:          field.ID,
					Name:        field.Name,
					NameEn:      field.NameEn,
					Type:        mqs.mapDataType(field.DataType),
					Description: field.Description,
					Table:       "", // DataField模型中没有Table字段，可以从DatasetID推导
					Category:    categoryID,
					Examples:    mqs.convertExamples(field.Examples),
					IsRequired:  false, // DataField模型中没有IsRequired字段，默认为false
				}
				category.Fields = append(category.Fields, medicalField)
			}
		}
	}

	// 过滤掉没有字段的分类
	var result []model.MedicalFieldCategory
	for _, category := range categories {
		if len(category.Fields) > 0 {
			result = append(result, category)
		}
	}

	return result, nil
}

// mapFieldToCategory 将数据库字段分类映射到医学分类
func (mqs *MedicalQueryService) mapFieldToCategory(dbCategory string) string {
	categoryMap := map[string]string{
		"人口统计学": "demographics",
		"标识符":   "demographics",
		"时间":    "admission",
		"分类":    "admission",
		"临床":    "diagnosis",
		"数值":    "laboratory",
		"单位":    "laboratory",
		"标志":    "vitals",
	}

	if mapped, exists := categoryMap[dbCategory]; exists {
		return mapped
	}
	return "demographics" // 默认分类
}

// mapDataType 映射数据类型
func (mqs *MedicalQueryService) mapDataType(dbType string) string {
	typeMap := map[string]string{
		"INTEGER":   "number",
		"NUMERIC":   "number",
		"VARCHAR":   "string",
		"TEXT":      "string",
		"TIMESTAMP": "datetime",
		"BOOLEAN":   "boolean",
	}

	if mapped, exists := typeMap[dbType]; exists {
		return mapped
	}
	return "string" // 默认类型
}

// convertExamples 转换示例数据
func (mqs *MedicalQueryService) convertExamples(examples json.RawMessage) []string {
	if len(examples) == 0 {
		return nil
	}

	var result []string
	if err := json.Unmarshal(examples, &result); err != nil {
		// 如果解析失败，返回空切片
		return nil
	}
	return result
}
