"use client"

import { useState, useEffect, use<PERSON>emo } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { 
  Database, 
  Search, 
  Filter, 
  Play, 
  Save, 
  Download,
  Settings,
  History,
  Share,
  Layers,
  Target,
  Zap
} from "lucide-react"
import { DatasetSelector } from "./dataset-selector"
import { CohortBuilder } from "./cohort-builder"
import { FieldSelector } from "./field-selector"
import { FilterBuilder } from "./filter-builder"
import { QueryPreview } from "./query-preview"
import { QueryResults } from "./query-results"

interface Dataset {
  id: string
  name: string
  description: string
  version: string
  tableCount: number
  features: string[]
  status: "healthy" | "warning" | "error"
}

interface QueryBuilderState {
  selectedDataset: Dataset | null
  cohort: any
  selectedFields: any[]
  filters: any[]
  timeRange: any
  groupBy: string[]
  orderBy: any[]
  limit: number
}

interface AdvancedQueryBuilderProps {
  datasets: Dataset[]
  onQueryExecute: (query: any) => Promise<any>
  onQuerySave: (query: any) => Promise<void>
}

export function AdvancedQueryBuilder({ 
  datasets, 
  onQueryExecute, 
  onQuerySave 
}: AdvancedQueryBuilderProps) {
  const [activeTab, setActiveTab] = useState("dataset")
  const [queryState, setQueryState] = useState<QueryBuilderState>({
    selectedDataset: null,
    cohort: null,
    selectedFields: [],
    filters: [],
    timeRange: null,
    groupBy: [],
    orderBy: [],
    limit: 1000
  })
  const [isExecuting, setIsExecuting] = useState(false)
  const [queryResults, setQueryResults] = useState<any>(null)
  const [queryHistory, setQueryHistory] = useState<any[]>([])

  // 计算查询完整性
  const queryCompleteness = useMemo(() => {
    let score = 0
    let total = 5

    if (queryState.selectedDataset) score += 1
    if (queryState.cohort) score += 1
    if (queryState.selectedFields.length > 0) score += 1
    if (queryState.filters.length > 0) score += 1
    if (queryState.timeRange) score += 1

    return Math.round((score / total) * 100)
  }, [queryState])

  // 验证查询是否可执行
  const canExecuteQuery = useMemo(() => {
    return queryState.selectedDataset && 
           queryState.selectedFields.length > 0 && 
           queryCompleteness >= 60
  }, [queryState, queryCompleteness])

  // 处理数据集选择
  const handleDatasetSelect = (dataset: Dataset) => {
    setQueryState(prev => ({
      ...prev,
      selectedDataset: dataset,
      // 重置其他状态，因为不同数据集的字段可能不同
      selectedFields: [],
      filters: [],
      cohort: null
    }))
    setActiveTab("cohort")
  }

  // 处理查询执行
  const handleExecuteQuery = async () => {
    if (!canExecuteQuery) return

    setIsExecuting(true)
    try {
      const query = buildQueryDSL()
      const results = await onQueryExecute(query)
      setQueryResults(results)
      
      // 添加到历史记录
      setQueryHistory(prev => [
        {
          id: Date.now().toString(),
          timestamp: new Date(),
          dataset: queryState.selectedDataset?.name,
          fieldsCount: queryState.selectedFields.length,
          filtersCount: queryState.filters.length,
          resultCount: results.total,
          executionTime: results.executionTime
        },
        ...prev.slice(0, 9) // 保留最近10条
      ])
      
      setActiveTab("results")
    } catch (error) {
      console.error("Query execution failed:", error)
    } finally {
      setIsExecuting(false)
    }
  }

  // 构建查询DSL
  const buildQueryDSL = () => {
    return {
      dataset_id: queryState.selectedDataset?.id || "",
      study_name: `研究_${Date.now()}`,
      description: "通过高级查询构建器创建的查询",
      cohort: queryState.cohort,
      fields: queryState.selectedFields,
      filters: queryState.filters,
      time_range: queryState.timeRange,
      group_by: queryState.groupBy,
      order_by: queryState.orderBy,
      limit: queryState.limit
    }
  }

  // 获取当前步骤状态
  const getStepStatus = (step: string) => {
    switch (step) {
      case "dataset":
        return queryState.selectedDataset ? "complete" : "current"
      case "cohort":
        return !queryState.selectedDataset ? "disabled" : 
               queryState.cohort ? "complete" : "current"
      case "fields":
        return !queryState.selectedDataset ? "disabled" :
               queryState.selectedFields.length > 0 ? "complete" : "current"
      case "filters":
        return !queryState.selectedDataset ? "disabled" :
               queryState.filters.length > 0 ? "complete" : "current"
      case "preview":
        return !canExecuteQuery ? "disabled" : "current"
      case "results":
        return queryResults ? "complete" : "disabled"
      default:
        return "disabled"
    }
  }

  return (
    <div className="h-full flex flex-col">
      {/* 顶部工具栏 */}
      <div className="flex items-center justify-between p-4 border-b bg-white">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Database className="h-5 w-5 text-blue-600" />
            <h1 className="text-xl font-semibold">高级查询构建器</h1>
          </div>
          
          {queryState.selectedDataset && (
            <Badge variant="outline" className="flex items-center gap-1">
              <Layers className="h-3 w-3" />
              {queryState.selectedDataset.name}
            </Badge>
          )}
        </div>

        <div className="flex items-center gap-2">
          {/* 查询完整性指示器 */}
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Target className="h-4 w-4" />
            <span>完整性: {queryCompleteness}%</span>
            <div className="w-20 h-2 bg-gray-200 rounded-full overflow-hidden">
              <div 
                className="h-full bg-gradient-to-r from-red-500 via-yellow-500 to-green-500 transition-all duration-300"
                style={{ width: `${queryCompleteness}%` }}
              />
            </div>
          </div>

          <Separator orientation="vertical" className="h-6" />

          <Button variant="outline" size="sm">
            <History className="h-4 w-4 mr-1" />
            历史
          </Button>
          
          <Button variant="outline" size="sm">
            <Save className="h-4 w-4 mr-1" />
            保存
          </Button>
          
          <Button 
            onClick={handleExecuteQuery}
            disabled={!canExecuteQuery || isExecuting}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isExecuting ? (
              <Zap className="h-4 w-4 mr-1 animate-spin" />
            ) : (
              <Play className="h-4 w-4 mr-1" />
            )}
            {isExecuting ? "执行中..." : "执行查询"}
          </Button>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 flex">
        {/* 左侧步骤导航 */}
        <div className="w-64 border-r bg-gray-50 p-4">
          <div className="space-y-2">
            {[
              { id: "dataset", label: "选择数据集", icon: Database },
              { id: "cohort", label: "定义人群", icon: Target },
              { id: "fields", label: "选择字段", icon: Search },
              { id: "filters", label: "设置过滤", icon: Filter },
              { id: "preview", label: "预览查询", icon: Settings },
              { id: "results", label: "查询结果", icon: Download }
            ].map((step, index) => {
              const status = getStepStatus(step.id)
              const Icon = step.icon
              
              return (
                <button
                  key={step.id}
                  onClick={() => status !== "disabled" && setActiveTab(step.id)}
                  disabled={status === "disabled"}
                  className={`w-full flex items-center gap-3 p-3 rounded-lg text-left transition-all ${
                    activeTab === step.id
                      ? "bg-blue-100 text-blue-700 border border-blue-200"
                      : status === "complete"
                      ? "bg-green-50 text-green-700 hover:bg-green-100"
                      : status === "disabled"
                      ? "text-gray-400 cursor-not-allowed"
                      : "text-gray-600 hover:bg-white hover:shadow-sm"
                  }`}
                >
                  <div className="flex items-center justify-center w-6 h-6">
                    {status === "complete" ? (
                      <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                        <div className="w-2 h-2 bg-white rounded-full" />
                      </div>
                    ) : (
                      <Icon className="h-4 w-4" />
                    )}
                  </div>
                  <div>
                    <div className="font-medium text-sm">{step.label}</div>
                    <div className="text-xs opacity-75">
                      步骤 {index + 1}
                    </div>
                  </div>
                </button>
              )
            })}
          </div>

          {/* 查询历史 */}
          {queryHistory.length > 0 && (
            <div className="mt-6">
              <h3 className="text-sm font-medium text-gray-700 mb-2">最近查询</h3>
              <ScrollArea className="h-32">
                <div className="space-y-1">
                  {queryHistory.slice(0, 3).map((query) => (
                    <div key={query.id} className="p-2 text-xs bg-white rounded border">
                      <div className="font-medium">{query.dataset}</div>
                      <div className="text-gray-500">
                        {query.resultCount} 条结果 • {query.executionTime}ms
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </div>
          )}
        </div>

        {/* 右侧内容区域 */}
        <div className="flex-1 p-6">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsContent value="dataset" className="mt-0">
              <DatasetSelector
                datasets={datasets}
                selectedDataset={queryState.selectedDataset}
                onDatasetSelect={handleDatasetSelect}
              />
            </TabsContent>

            <TabsContent value="cohort" className="mt-0">
              <CohortBuilder
                dataset={queryState.selectedDataset}
                cohort={queryState.cohort}
                onCohortChange={(cohort) => setQueryState(prev => ({ ...prev, cohort }))}
              />
            </TabsContent>

            <TabsContent value="fields" className="mt-0">
              <FieldSelector
                dataset={queryState.selectedDataset}
                selectedFields={queryState.selectedFields}
                onFieldsChange={(fields) => setQueryState(prev => ({ ...prev, selectedFields: fields }))}
              />
            </TabsContent>

            <TabsContent value="filters" className="mt-0">
              <FilterBuilder
                dataset={queryState.selectedDataset}
                filters={queryState.filters}
                onFiltersChange={(filters) => setQueryState(prev => ({ ...prev, filters }))}
                availableFields={queryState.selectedFields.map(sf => sf.field)}
              />
            </TabsContent>

            <TabsContent value="preview" className="mt-0">
              <QueryPreview
                queryDSL={buildQueryDSL()}
                onExecute={handleExecuteQuery}
                isExecuting={isExecuting}
              />
            </TabsContent>

            <TabsContent value="results" className="mt-0">
              <QueryResults
                results={queryResults}
                onExport={(format) => console.log("Export:", format)}
                onSaveQuery={() => onQuerySave(buildQueryDSL())}
              />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
