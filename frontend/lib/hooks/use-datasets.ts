"use client"

import { useState, useEffect } from "react"
import { apiClient } from "@/lib/api"

export interface Dataset {
  id: string
  name: string
  description: string
  version: string
  patientCount: number
  status: "active" | "maintenance" | "inactive"
  lastUpdated: string
  category?: string
  tags?: string[]
  metadata?: {
    size: string
    tables: number
    fields: number
    timeRange?: {
      start: string
      end: string
    }
    schemas?: string[]
    coreTables?: string[]
  }
}

export function useDatasets() {
  const [datasets, setDatasets] = useState<Dataset[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchDatasets()
  }, [])

  const fetchDatasets = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await apiClient.getDatasets()

      if (response.data) {
        // 检查响应数据结构
        const datasets = Array.isArray(response.data) ? response.data : ((response.data as any)?.datasets || [])
        setDatasets(datasets)
        setError(null)
      } else {
        // 如果API返回空数据，使用模拟数据
        console.warn("API returned empty data, using mock datasets")
        setDatasets(getMockDatasets())
        setError("数据源暂时不可用，正在显示示例数据")
      }
    } catch (err) {
      console.error("Failed to fetch datasets:", err)

      // 根据错误类型设置不同的错误信息
      let errorMessage = "数据加载失败"
      if (err instanceof Error) {
        if (err.message.includes("Network")) {
          errorMessage = "网络连接失败，请检查网络连接后重试"
        } else if (err.message.includes("timeout")) {
          errorMessage = "请求超时，请稍后重试"
        } else if (err.message.includes("401") || err.message.includes("403")) {
          errorMessage = "访问权限不足，请联系管理员"
        } else if (err.message.includes("500")) {
          errorMessage = "服务器内部错误，请稍后重试"
        } else {
          errorMessage = `数据加载失败: ${err.message}`
        }
      }

      // 使用模拟数据作为后备，但保留错误信息
      setDatasets(getMockDatasets())
      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const getDataset = async (id: string) => {
    try {
      const response = await apiClient.getDataset(id)
      return response.data
    } catch (err) {
      console.error("Failed to fetch dataset:", err)
      return null
    }
  }

  return {
    datasets,
    loading,
    error,
    refetch: fetchDatasets,
    getDataset,
  }
}

// 模拟数据，用于开发和演示
function getMockDatasets(): Dataset[] {
  return [
    {
      id: "mimic-iv",
      name: "MIMIC-IV",
      description: "Medical Information Mart for Intensive Care IV - 真实医学数据库",
      version: "v2.2",
      patientCount: 382278,
      status: "active",
      lastUpdated: "2024-01-26T10:00:00Z",
      category: "critical-care",
      tags: ["ICU", "重症监护", "生命体征", "实验室检查", "真实数据"],
      metadata: {
        size: "2.4 TB",
        tables: 10,
        fields: 200,
        timeRange: {
          start: "2008-01-01",
          end: "2019-12-31",
        },
        schemas: ["mimiciv_hosp", "mimiciv_icu", "mimiciv_derived"],
        coreTables: [
          "patients", "admissions", "labevents", "diagnoses_icd",
          "prescriptions", "icustays", "chartevents"
        ],
      },
    },
    {
      id: "eicu",
      name: "eICU-CRD",
      description: "多中心重症监护协作研究数据库",
      version: "v2.0",
      patientCount: 200859,
      status: "active",
      lastUpdated: "2024-01-10T15:30:00Z",
      category: "critical-care",
      tags: ["多中心", "ICU", "协作研究"],
      metadata: {
        size: "28.5 GB",
        tables: 31,
        fields: 692,
        timeRange: {
          start: "2014-01-01",
          end: "2015-12-31",
        },
      },
    },
    {
      id: "nhanes",
      name: "NHANES",
      description: "美国国家健康与营养调查",
      version: "2017-2020",
      patientCount: 15560,
      status: "active",
      lastUpdated: "2024-01-08T09:15:00Z",
      category: "population-health",
      tags: ["营养调查", "人口健康", "流行病学"],
      metadata: {
        size: "3.2 GB",
        tables: 12,
        fields: 234,
        timeRange: {
          start: "2017-01-01",
          end: "2020-03-31",
        },
      },
    },
    {
      id: "pic",
      name: "PIC",
      description: "儿科重症监护数据库",
      version: "v1.1",
      patientCount: 12881,
      status: "maintenance",
      lastUpdated: "2024-01-05T14:20:00Z",
      category: "pediatric",
      tags: ["儿科", "PICU", "儿童重症"],
      metadata: {
        size: "2.8 GB",
        tables: 18,
        fields: 456,
        timeRange: {
          start: "2016-01-01",
          end: "2021-12-31",
        },
      },
    },
    {
      id: "mimiciii",
      name: "MIMIC-III",
      description: "重症监护医学信息数据库第三版",
      version: "v1.4",
      patientCount: 46520,
      status: "active",
      lastUpdated: "2023-12-20T11:45:00Z",
      category: "critical-care",
      tags: ["ICU", "历史数据", "经典数据集"],
      metadata: {
        size: "6.3 GB",
        tables: 26,
        fields: 654,
        timeRange: {
          start: "2001-01-01",
          end: "2012-12-31",
        },
      },
    },
    {
      id: "physionet-2012",
      name: "PhysioNet Challenge 2012",
      description: "ICU死亡率预测挑战数据集",
      version: "v1.0",
      patientCount: 12000,
      status: "active",
      lastUpdated: "2023-11-15T16:30:00Z",
      category: "challenge",
      tags: ["机器学习", "死亡率预测", "挑战赛"],
      metadata: {
        size: "890 MB",
        tables: 3,
        fields: 42,
        timeRange: {
          start: "2012-01-01",
          end: "2012-12-31",
        },
      },
    },
  ]
}
