-- 更新模拟数据以匹配真实MIMIC-IV结构
-- 基于真实PostgreSQL数据库中的MIMIC-IV数据

-- 清除旧的模拟数据
DELETE FROM data_fields WHERE dataset_id IN ('mimic-iv', 'mimic_iv');
DELETE FROM datasets WHERE id IN ('mimic-iv', 'mimic_iv');

-- 插入更新的MIMIC-IV数据集信息
INSERT INTO datasets (id, name, description, version, status, created_at, updated_at) VALUES
('mimic-iv', 'MIMIC-IV', 'Medical Information Mart for Intensive Care IV - 真实医学数据库', '2.2', 'active', NOW(), NOW());

-- 插入真实的MIMIC-IV表结构对应的字段信息
-- 基于mimiciv_hosp schema的核心表

-- patients表字段
INSERT INTO data_fields (id, dataset_id, name, name_en, code, data_type, category, description, unit, value_range, examples) VALUES
('mimic-iv.patients.subject_id', 'mimic-iv', '患者ID', 'subject_id', 'SUBJ_ID', 'INTEGER', '人口统计学', '患者唯一标识符', '', '', '["10000032", "10000980", "10001217"]'),
('mimic-iv.patients.gender', 'mimic-iv', '性别', 'gender', 'GENDER', 'VARCHAR', '人口统计学', '患者性别', '', 'M/F', '["M", "F"]'),
('mimic-iv.patients.anchor_age', 'mimic-iv', '锚定年龄', 'anchor_age', 'AGE', 'INTEGER', '人口统计学', '患者在锚定年份的年龄', '岁', '18-89', '["65", "72", "45"]'),
('mimic-iv.patients.anchor_year', 'mimic-iv', '锚定年份', 'anchor_year', 'YEAR', 'INTEGER', '时间', '患者数据的锚定年份', '年', '2008-2019', '["2180", "2181", "2182"]'),
('mimic-iv.patients.anchor_year_group', 'mimic-iv', '锚定年份组', 'anchor_year_group', 'YEAR_GRP', 'VARCHAR', '分类', '锚定年份的分组', '', '', '["2008 - 2010", "2011 - 2013"]'),
('mimic-iv.patients.dod', 'mimic-iv', '死亡日期', 'dod', 'DOD', 'DATE', '时间', '患者死亡日期', '', '', '["2180-07-15", "2181-03-22"]');

-- admissions表字段
INSERT INTO data_fields (id, dataset_id, name, name_en, code, data_type, category, description, unit, value_range, examples) VALUES
('mimic-iv.admissions.subject_id', 'mimic-iv', '患者ID', 'subject_id', 'SUBJ_ID', 'INTEGER', '人口统计学', '患者唯一标识符', '', '', '["10000032", "10000980"]'),
('mimic-iv.admissions.hadm_id', 'mimic-iv', '住院ID', 'hadm_id', 'HADM_ID', 'INTEGER', '时间', '住院唯一标识符', '', '', '["29079034", "26951159"]'),
('mimic-iv.admissions.admittime', 'mimic-iv', '入院时间', 'admittime', 'ADMIT_TIME', 'TIMESTAMP', '时间', '患者入院时间', '', '', '["2180-07-15 14:00:00", "2181-03-22 09:30:00"]'),
('mimic-iv.admissions.dischtime', 'mimic-iv', '出院时间', 'dischtime', 'DISCH_TIME', 'TIMESTAMP', '时间', '患者出院时间', '', '', '["2180-07-20 16:45:00", "2181-03-25 11:15:00"]'),
('mimic-iv.admissions.deathtime', 'mimic-iv', '死亡时间', 'deathtime', 'DEATH_TIME', 'TIMESTAMP', '时间', '患者死亡时间', '', '', '["2180-07-19 23:45:00"]'),
('mimic-iv.admissions.admission_type', 'mimic-iv', '入院类型', 'admission_type', 'ADM_TYPE', 'VARCHAR', '分类', '入院类型分类', '', '', '["EMERGENCY", "ELECTIVE", "URGENT"]'),
('mimic-iv.admissions.admission_location', 'mimic-iv', '入院来源', 'admission_location', 'ADM_LOC', 'VARCHAR', '分类', '患者入院来源', '', '', '["EMERGENCY ROOM", "PHYSICIAN REFERRAL"]'),
('mimic-iv.admissions.discharge_location', 'mimic-iv', '出院去向', 'discharge_location', 'DISCH_LOC', 'VARCHAR', '分类', '患者出院去向', '', '', '["HOME", "REHAB", "SNF"]'),
('mimic-iv.admissions.insurance', 'mimic-iv', '保险类型', 'insurance', 'INSURANCE', 'VARCHAR', '分类', '患者保险类型', '', '', '["Medicare", "Medicaid", "Private"]'),
('mimic-iv.admissions.language', 'mimic-iv', '语言', 'language', 'LANGUAGE', 'VARCHAR', '分类', '患者主要语言', '', '', '["ENGLISH", "SPANISH", "PORTUGUESE"]'),
('mimic-iv.admissions.marital_status', 'mimic-iv', '婚姻状况', 'marital_status', 'MARITAL', 'VARCHAR', '分类', '患者婚姻状况', '', '', '["MARRIED", "SINGLE", "DIVORCED"]'),
('mimic-iv.admissions.race', 'mimic-iv', '种族', 'race', 'RACE', 'VARCHAR', '分类', '患者种族信息', '', '', '["WHITE", "BLACK", "HISPANIC"]');

-- labevents表字段
INSERT INTO data_fields (id, dataset_id, name, name_en, code, data_type, category, description, unit, value_range, examples) VALUES
('mimic-iv.labevents.subject_id', 'mimic-iv', '患者ID', 'subject_id', 'SUBJ_ID', 'INTEGER', '人口统计学', '患者唯一标识符', '', '', '["10000032", "10000980"]'),
('mimic-iv.labevents.hadm_id', 'mimic-iv', '住院ID', 'hadm_id', 'HADM_ID', 'INTEGER', '时间', '住院唯一标识符', '', '', '["29079034", "26951159"]'),
('mimic-iv.labevents.specimen_id', 'mimic-iv', '标本ID', 'specimen_id', 'SPEC_ID', 'INTEGER', '标识符', '标本唯一标识符', '', '', '["90001234", "90005678"]'),
('mimic-iv.labevents.itemid', 'mimic-iv', '检验项目ID', 'itemid', 'ITEM_ID', 'INTEGER', '数值', '检验项目标识符', '', '', '["50868", "50882", "50912"]'),
('mimic-iv.labevents.charttime', 'mimic-iv', '检验时间', 'charttime', 'CHART_TIME', 'TIMESTAMP', '时间', '检验采样时间', '', '', '["2180-07-16 08:00:00", "2181-03-23 14:30:00"]'),
('mimic-iv.labevents.storetime', 'mimic-iv', '存储时间', 'storetime', 'STORE_TIME', 'TIMESTAMP', '时间', '结果存储时间', '', '', '["2180-07-16 10:15:00", "2181-03-23 16:45:00"]'),
('mimic-iv.labevents.value', 'mimic-iv', '检验值', 'value', 'VALUE', 'VARCHAR', '数值', '检验结果值', '', '', '["7.4", "140", "NEGATIVE"]'),
('mimic-iv.labevents.valuenum', 'mimic-iv', '数值结果', 'valuenum', 'VALUE_NUM', 'NUMERIC', '数值', '数值型检验结果', '', '', '["7.4", "140.5", "98.6"]'),
('mimic-iv.labevents.valueuom', 'mimic-iv', '单位', 'valueuom', 'UOM', 'VARCHAR', '单位', '检验结果单位', '', '', '["mEq/L", "mg/dL", "mmHg"]'),
('mimic-iv.labevents.ref_range_lower', 'mimic-iv', '参考下限', 'ref_range_lower', 'REF_LOW', 'NUMERIC', '数值', '参考范围下限', '', '', '["7.35", "135", "80"]'),
('mimic-iv.labevents.ref_range_upper', 'mimic-iv', '参考上限', 'ref_range_upper', 'REF_HIGH', 'NUMERIC', '数值', '参考范围上限', '', '', '["7.45", "145", "120"]'),
('mimic-iv.labevents.flag', 'mimic-iv', '异常标志', 'flag', 'FLAG', 'VARCHAR', '标志', '检验结果异常标志', '', '', '["abnormal", "delta", ""]'),
('mimic-iv.labevents.priority', 'mimic-iv', '优先级', 'priority', 'PRIORITY', 'VARCHAR', '分类', '检验优先级', '', '', '["ROUTINE", "STAT", "URGENT"]'),
('mimic-iv.labevents.comments', 'mimic-iv', '备注', 'comments', 'COMMENTS', 'TEXT', '分类', '检验备注信息', '', '', '["hemolyzed", "lipemic", ""]');

-- diagnoses_icd表字段
INSERT INTO data_fields (id, dataset_id, name, name_en, code, data_type, category, description, unit, value_range, examples) VALUES
('mimic-iv.diagnoses_icd.subject_id', 'mimic-iv', '患者ID', 'subject_id', 'SUBJ_ID', 'INTEGER', '人口统计学', '患者唯一标识符', '', '', '["10000032", "10000980"]'),
('mimic-iv.diagnoses_icd.hadm_id', 'mimic-iv', '住院ID', 'hadm_id', 'HADM_ID', 'INTEGER', '时间', '住院唯一标识符', '', '', '["29079034", "26951159"]'),
('mimic-iv.diagnoses_icd.seq_num', 'mimic-iv', '序号', 'seq_num', 'SEQ_NUM', 'INTEGER', '数值', '诊断序号', '', '', '["1", "2", "3"]'),
('mimic-iv.diagnoses_icd.icd_code', 'mimic-iv', 'ICD代码', 'icd_code', 'ICD_CODE', 'VARCHAR', '临床', 'ICD诊断代码', '', '', '["I50.9", "N18.6", "E11.9"]'),
('mimic-iv.diagnoses_icd.icd_version', 'mimic-iv', 'ICD版本', 'icd_version', 'ICD_VER', 'INTEGER', '分类', 'ICD编码版本', '', '', '["9", "10"]');

-- prescriptions表字段
INSERT INTO data_fields (id, dataset_id, name, name_en, code, data_type, category, description, unit, value_range, examples) VALUES
('mimic-iv.prescriptions.subject_id', 'mimic-iv', '患者ID', 'subject_id', 'SUBJ_ID', 'INTEGER', '人口统计学', '患者唯一标识符', '', '', '["10000032", "10000980"]'),
('mimic-iv.prescriptions.hadm_id', 'mimic-iv', '住院ID', 'hadm_id', 'HADM_ID', 'INTEGER', '时间', '住院唯一标识符', '', '', '["29079034", "26951159"]'),
('mimic-iv.prescriptions.pharmacy_id', 'mimic-iv', '药房ID', 'pharmacy_id', 'PHARM_ID', 'INTEGER', '标识符', '药房处方标识符', '', '', '["500001", "500002"]'),
('mimic-iv.prescriptions.starttime', 'mimic-iv', '开始时间', 'starttime', 'START_TIME', 'TIMESTAMP', '时间', '用药开始时间', '', '', '["2180-07-16 08:00:00", "2181-03-23 14:30:00"]'),
('mimic-iv.prescriptions.stoptime', 'mimic-iv', '结束时间', 'stoptime', 'STOP_TIME', 'TIMESTAMP', '时间', '用药结束时间', '', '', '["2180-07-20 08:00:00", "2181-03-25 14:30:00"]'),
('mimic-iv.prescriptions.drug_type', 'mimic-iv', '药物类型', 'drug_type', 'DRUG_TYPE', 'VARCHAR', '分类', '药物类型分类', '', '', '["MAIN", "ADDITIVE", "BASE"]'),
('mimic-iv.prescriptions.drug', 'mimic-iv', '药物名称', 'drug', 'DRUG', 'VARCHAR', '临床', '药物通用名称', '', '', '["Metoprolol", "Furosemide", "Insulin"]'),
('mimic-iv.prescriptions.formulary_drug_cd', 'mimic-iv', '处方药代码', 'formulary_drug_cd', 'FORM_CD', 'VARCHAR', '临床', '处方药物代码', '', '', '["METO25", "FURO40", "INSU"]'),
('mimic-iv.prescriptions.gsn', 'mimic-iv', 'GSN代码', 'gsn', 'GSN', 'VARCHAR', '临床', '通用序列号', '', '', '["002542", "008702", "001234"]'),
('mimic-iv.prescriptions.ndc', 'mimic-iv', 'NDC代码', 'ndc', 'NDC', 'VARCHAR', '临床', '国家药物代码', '', '', '["0781-1506-10", "0054-3115-63"]'),
('mimic-iv.prescriptions.prod_strength', 'mimic-iv', '药物强度', 'prod_strength', 'STRENGTH', 'VARCHAR', '数值', '药物浓度强度', '', '', '["25mg", "40mg", "100units/mL"]'),
('mimic-iv.prescriptions.form_rx', 'mimic-iv', '剂型', 'form_rx', 'FORM', 'VARCHAR', '分类', '药物剂型', '', '', '["TAB", "INJ", "SOLN"]'),
('mimic-iv.prescriptions.route', 'mimic-iv', '给药途径', 'route', 'ROUTE', 'VARCHAR', '分类', '药物给药途径', '', '', '["PO", "IV", "SC"]'),
('mimic-iv.prescriptions.dose_val_rx', 'mimic-iv', '剂量值', 'dose_val_rx', 'DOSE_VAL', 'VARCHAR', '数值', '处方剂量值', '', '', '["25", "40", "10"]'),
('mimic-iv.prescriptions.dose_unit_rx', 'mimic-iv', '剂量单位', 'dose_unit_rx', 'DOSE_UNIT', 'VARCHAR', '单位', '处方剂量单位', '', '', '["mg", "mL", "units"]');

-- 更新查询模板以使用真实表名
UPDATE query_templates SET 
    query_config = jsonb_set(
        query_config,
        '{tables}',
        '["mimiciv_hosp.patients", "mimiciv_hosp.admissions", "mimiciv_hosp.labevents"]'::jsonb
    )
WHERE name = 'ICU患者基本分析';

UPDATE query_templates SET 
    query_config = jsonb_set(
        query_config,
        '{tables}',
        '["mimiciv_icu.icustays", "mimiciv_icu.chartevents"]'::jsonb
    )
WHERE name = '机械通气患者分析';

UPDATE query_templates SET 
    query_config = jsonb_set(
        query_config,
        '{tables}',
        '["mimiciv_hosp.patients", "mimiciv_hosp.admissions", "mimiciv_hosp.diagnoses_icd"]'::jsonb
    )
WHERE name = '心血管疾病患者队列';

-- 插入表信息记录
INSERT INTO dataset_tables (dataset_id, table_name, description, record_count) VALUES
('mimic-iv', 'patients', '患者基本信息表', 382278),
('mimic-iv', 'admissions', '住院记录表', 523740),
('mimic-iv', 'labevents', '检验结果表', 122103667),
('mimic-iv', 'diagnoses_icd', 'ICD诊断表', 4756326),
('mimic-iv', 'prescriptions', '处方信息表', 15416838),
('mimic-iv', 'procedures_icd', 'ICD手术表', 669618),
('mimic-iv', 'icustays', 'ICU住院表', 76540),
('mimic-iv', 'chartevents', '图表事件表', 329499788),
('mimic-iv', 'inputevents', '输入事件表', 8978893),
('mimic-iv', 'outputevents', '输出事件表', 4234967)
ON CONFLICT (dataset_id, table_name) DO UPDATE SET
    description = EXCLUDED.description,
    record_count = EXCLUDED.record_count;
