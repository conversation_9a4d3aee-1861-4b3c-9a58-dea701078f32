import { apiClient } from '../api'

// 医学查询相关的API接口

export interface MedicalFieldCategory {
  id: string
  name: string
  description: string
  icon: string
  fields: MedicalField[]
}

export interface MedicalField {
  id: string
  name: string
  nameEn: string
  type: string
  description: string
  table: string
  category: string
  unit?: string
  examples?: string[]
  isRequired?: boolean
}

export interface CohortCriteria {
  id: string
  category: string
  field: string
  operator: string
  value: string | number
  description: string
  logic?: 'AND' | 'OR'
}

export interface DataDimension {
  category: string
  fields: MedicalField[]
  isSelected: boolean
}

export interface MedicalTimeRange {
  start?: string
  end?: string
  type: 'admission' | 'discharge' | 'lab' | 'custom'
}

export interface MedicalQueryRequest {
  studyName: string
  cohortCriteria: CohortCriteria[]
  dataDimensions: DataDimension[]
  timeRange: MedicalTimeRange
  outputFormat: 'csv' | 'excel' | 'json'
  maxRecords: number
}

export interface MedicalQueryResponse {
  queryId: string
  studyName: string
  data: Record<string, any>[]
  total: number
  executionTime: number
  status: string
  metadata: Record<string, any>
}

export interface CohortValidationRequest {
  criteria: CohortCriteria[]
}

export interface CohortValidationResponse {
  isValid: boolean
  estimatedCount: number
  warnings?: string[]
  errors?: string[]
  validationTime: number
}

export interface ResultEstimationResponse {
  estimatedRows: number
  estimatedSize: string
  complexityScore: number
  executionTime: number
  warnings?: string[]
  recommendations?: string[]
  metadata: Record<string, any>
}

export interface MedicalQueryTemplate {
  id: string
  name: string
  description: string
  category: string
  queryConfig: any
  createdBy: string
  createdAt: string
  updatedAt: string
  isPublic: boolean
  usageCount: number
  tags: string[]
}

export interface SaveTemplateRequest {
  name: string
  description: string
  category: string
  queryConfig: MedicalQueryRequest
  isPublic: boolean
  tags: string[]
}

export interface MedicalQueryHistory {
  id: string
  userId: string
  studyName: string
  queryConfig: any
  status: string
  recordCount?: number
  executionTime?: number
  errorMessage?: string
  createdAt: string
}

export interface ExportResult {
  queryId: string
  format: string
  filePath: string
  fileSize: number
  recordCount: number
  exportedAt: string
  downloadUrl: string
  expiresAt: string
}

export interface PaginationInfo {
  page: number
  limit: number
  total: number
  totalPages: number
}

export interface QueryTemplatesResponse {
  templates: MedicalQueryTemplate[]
  pagination: PaginationInfo
}

export interface MedicalQueryHistoryResponse {
  queries: MedicalQueryHistory[]
  pagination: PaginationInfo
}

// API 函数

/**
 * 获取医学字段分类
 */
export const getMedicalCategories = async (): Promise<MedicalFieldCategory[]> => {
  const response = await apiClient.get('/medical-query/categories')
  return response.data.categories
}

/**
 * 验证人群筛选条件
 */
export const validateCohort = async (request: CohortValidationRequest): Promise<CohortValidationResponse> => {
  const response = await apiClient.post('/medical-query/validate-cohort', request)
  return response.data
}

/**
 * 估算查询结果
 */
export const estimateResults = async (request: MedicalQueryRequest): Promise<ResultEstimationResponse> => {
  const response = await apiClient.post('/medical-query/estimate', request)
  return response.data
}

/**
 * 执行医学查询
 */
export const executeMedicalQuery = async (request: MedicalQueryRequest): Promise<MedicalQueryResponse> => {
  const response = await apiClient.post('/medical-query/execute', request)
  return response.data
}

/**
 * 获取查询模板
 */
export const getQueryTemplates = async (
  page: number = 1,
  limit: number = 20,
  category?: string
): Promise<QueryTemplatesResponse> => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
  })
  
  if (category) {
    params.append('category', category)
  }
  
  const response = await apiClient.get(`/medical-query/templates?${params}`)
  return response.data
}

/**
 * 保存查询模板
 */
export const saveQueryTemplate = async (request: SaveTemplateRequest): Promise<MedicalQueryTemplate> => {
  const response = await apiClient.post('/medical-query/templates', request)
  return response.data
}

/**
 * 获取医学查询历史
 */
export const getMedicalQueryHistory = async (
  page: number = 1,
  limit: number = 20
): Promise<MedicalQueryHistoryResponse> => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
  })
  
  const response = await apiClient.get(`/medical-query/history?${params}`)
  return response.data
}

/**
 * 导出查询结果
 */
export const exportQueryResults = async (
  queryId: string,
  format: 'csv' | 'excel' | 'json' = 'csv'
): Promise<ExportResult> => {
  const response = await apiClient.post(`/medical-query/${queryId}/export?format=${format}`)
  return response.data
}

/**
 * 下载导出文件
 */
export const downloadExportFile = (downloadUrl: string, filename?: string) => {
  const link = document.createElement('a')
  link.href = downloadUrl
  if (filename) {
    link.download = filename
  }
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 工具函数

/**
 * 格式化查询复杂度分数
 */
export const formatComplexityScore = (score: number): string => {
  if (score <= 3) return '简单'
  if (score <= 6) return '中等'
  if (score <= 8) return '复杂'
  return '非常复杂'
}

/**
 * 格式化文件大小
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 格式化执行时间
 */
export const formatExecutionTime = (milliseconds: number): string => {
  if (milliseconds < 1000) {
    return `${milliseconds}ms`
  } else if (milliseconds < 60000) {
    return `${(milliseconds / 1000).toFixed(1)}s`
  } else {
    const minutes = Math.floor(milliseconds / 60000)
    const seconds = ((milliseconds % 60000) / 1000).toFixed(0)
    return `${minutes}m ${seconds}s`
  }
}

/**
 * 验证查询配置
 */
export const validateQueryConfig = (config: MedicalQueryRequest): string[] => {
  const errors: string[] = []
  
  if (!config.studyName.trim()) {
    errors.push('研究名称不能为空')
  }
  
  if (config.dataDimensions.length === 0) {
    errors.push('至少需要选择一个数据维度')
  }
  
  if (config.maxRecords <= 0) {
    errors.push('最大记录数必须大于0')
  }
  
  if (config.maxRecords > 50000) {
    errors.push('最大记录数不能超过50,000')
  }
  
  return errors
}
