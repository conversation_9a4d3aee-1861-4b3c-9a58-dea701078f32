#!/bin/bash

# Medical Data Platform Server - Deployment Package Test Script
# 医疗数据平台服务器部署包测试脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
OUTPUT_DIR="$PROJECT_ROOT/output"

# 测试部署包完整性
test_deployment_package() {
    log_info "测试部署包完整性..."
    
    local errors=0
    
    # 检查输出目录是否存在
    if [[ ! -d "$OUTPUT_DIR" ]]; then
        log_error "部署包目录不存在: $OUTPUT_DIR"
        return 1
    fi
    
    # 检查必需的文件和目录
    local required_items=(
        "bin/server"
        "configs"
        "scripts"
        "migrations"
        "logs"
        "exports"
        "tmp"
        "DEPLOYMENT.md"
    )
    
    log_info "检查必需的文件和目录..."
    for item in "${required_items[@]}"; do
        if [[ -e "$OUTPUT_DIR/$item" ]]; then
            log_success "✓ $item"
        else
            log_error "✗ $item 不存在"
            errors=$((errors + 1))
        fi
    done
    
    # 检查二进制文件权限
    log_info "检查二进制文件权限..."
    if [[ -x "$OUTPUT_DIR/bin/server" ]]; then
        log_success "✓ 二进制文件具有执行权限"
    else
        log_error "✗ 二进制文件没有执行权限"
        errors=$((errors + 1))
    fi
    
    # 检查脚本文件权限
    log_info "检查脚本文件权限..."
    local script_files=(
        "scripts/start.sh"
        "scripts/stop.sh"
        "scripts/restart.sh"
        "scripts/config.sh"
    )
    
    for script in "${script_files[@]}"; do
        if [[ -x "$OUTPUT_DIR/$script" ]]; then
            log_success "✓ $script 具有执行权限"
        else
            log_error "✗ $script 没有执行权限"
            errors=$((errors + 1))
        fi
    done
    
    # 检查配置文件
    log_info "检查配置文件..."
    local config_files=(
        "configs/config.yaml"
        "configs/config.example.yaml"
    )
    
    for config in "${config_files[@]}"; do
        if [[ -f "$OUTPUT_DIR/$config" ]]; then
            log_success "✓ $config"
        else
            log_warning "⚠ $config 不存在"
        fi
    done
    
    # 检查操作脚本
    log_info "检查操作脚本..."
    local operation_scripts=(
        "scripts/backup.sh"
        "scripts/setup_database.sh"
    )
    
    for script in "${operation_scripts[@]}"; do
        if [[ -f "$OUTPUT_DIR/$script" ]]; then
            log_success "✓ $script"
        else
            log_warning "⚠ $script 不存在"
        fi
    done
    
    # 检查迁移文件
    log_info "检查数据库迁移文件..."
    if [[ -d "$OUTPUT_DIR/migrations" ]]; then
        local migration_count=$(find "$OUTPUT_DIR/migrations" -name "*.sql" | wc -l)
        if [[ $migration_count -gt 0 ]]; then
            log_success "✓ 找到 $migration_count 个迁移文件"
        else
            log_warning "⚠ 没有找到迁移文件"
        fi
    fi
    
    # 检查文档
    log_info "检查文档..."
    local doc_files=(
        "README.md"
        "DEPLOYMENT.md"
    )
    
    for doc in "${doc_files[@]}"; do
        if [[ -f "$OUTPUT_DIR/$doc" ]]; then
            log_success "✓ $doc"
        else
            log_warning "⚠ $doc 不存在"
        fi
    done
    
    # 总结
    echo ""
    if [[ $errors -eq 0 ]]; then
        log_success "部署包完整性检查通过！"
        return 0
    else
        log_error "部署包完整性检查失败，发现 $errors 个错误"
        return 1
    fi
}

# 显示部署包信息
show_package_info() {
    log_info "部署包信息:"
    echo ""
    echo "位置: $OUTPUT_DIR"
    echo ""
    
    if [[ -d "$OUTPUT_DIR" ]]; then
        echo "目录结构:"
        tree "$OUTPUT_DIR" 2>/dev/null || find "$OUTPUT_DIR" -type d | head -20
        echo ""
        
        echo "文件统计:"
        echo "  总文件数: $(find "$OUTPUT_DIR" -type f | wc -l)"
        echo "  总目录数: $(find "$OUTPUT_DIR" -type d | wc -l)"
        
        if [[ -f "$OUTPUT_DIR/bin/server" ]]; then
            echo "  二进制文件大小: $(ls -lh "$OUTPUT_DIR/bin/server" | awk '{print $5}')"
        fi
        
        echo ""
    fi
}

# 主函数
main() {
    echo "========================================"
    echo "  医疗数据平台 - 部署包测试"
    echo "========================================"
    echo ""
    
    case "${1:-test}" in
        test)
            test_deployment_package
            ;;
        info)
            show_package_info
            ;;
        both)
            show_package_info
            echo ""
            test_deployment_package
            ;;
        *)
            echo "Usage: $0 [test|info|both]"
            echo ""
            echo "Commands:"
            echo "  test  - 测试部署包完整性（默认）"
            echo "  info  - 显示部署包信息"
            echo "  both  - 显示信息并测试"
            exit 1
            ;;
    esac
}

main "$@"
