package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/chungzy/medical-data-platform/internal/model"
	"github.com/chungzy/medical-data-platform/internal/repository"
	"github.com/chungzy/medical-data-platform/pkg/database"
	"github.com/chungzy/medical-data-platform/pkg/logger"
	"github.com/chungzy/medical-data-platform/pkg/query"
	"github.com/google/uuid"
)

// QueryExecutionService 查询执行服务
type QueryExecutionService struct {
	queryEngine  *query.QueryEngine
	queryRepo    repository.QueryRepository
	medicalRepo  repository.MedicalQueryRepository
	schemaRepo   repository.SchemaRepository
	auditService *QueryAuditService
}

// NewQueryExecutionService 创建查询执行服务
func NewQueryExecutionService(
	dsm *database.DataSourceManager,
	queryRepo repository.QueryRepository,
	medicalRepo repository.MedicalQueryRepository,
	schemaRepo repository.SchemaRepository,
) *QueryExecutionService {
	// 创建缓存
	cache := query.NewMemoryCache(1000, 1*time.Hour)

	// 创建查询引擎
	queryEngine := query.NewQueryEngine(dsm, cache)

	// 创建审计服务
	auditService := NewQueryAuditService(queryRepo)

	return &QueryExecutionService{
		queryEngine:  queryEngine,
		queryRepo:    queryRepo,
		medicalRepo:  medicalRepo,
		schemaRepo:   schemaRepo,
		auditService: auditService,
	}
}

// ExecuteMedicalQuery 执行医学查询
func (qes *QueryExecutionService) ExecuteMedicalQuery(ctx context.Context, dsl *query.MedicalQueryDSL, userID string) (*model.MedicalQueryResponse, error) {
	startTime := time.Now()

	// 生成查询ID
	if dsl.QueryID == "" {
		dsl.QueryID = uuid.New().String()
	}

	logger.Info("Starting medical query execution", map[string]interface{}{
		"query_id":   dsl.QueryID,
		"study_name": dsl.StudyName,
		"user_id":    userID,
		"dataset_id": dsl.DatasetID,
	})

	// 记录查询开始
	queryID := uuid.MustParse(dsl.QueryID)
	userUUID := uuid.MustParse(userID)
	queryName := dsl.StudyName

	queryRecord := &model.QueryHistory{
		ID:        queryID,
		UserID:    userUUID,
		Name:      &queryName,
		DatasetID: dsl.DatasetID,
		Status:    "running",
		CreatedAt: startTime,
	}

	// 序列化DSL
	if dslBytes, err := json.Marshal(dsl); err == nil {
		queryRecord.QueryConfig = json.RawMessage(dslBytes)
	}

	// 保存查询记录
	if err := qes.queryRepo.SaveQueryHistory(ctx, queryRecord); err != nil {
		logger.Error("Failed to create query history", err, map[string]interface{}{
			"query_id": dsl.QueryID,
			"user_id":  userID,
		})
	}

	// 执行查询
	result, err := qes.queryEngine.ExecuteQuery(ctx, dsl)
	if err != nil {
		// 更新查询状态为失败
		queryRecord.Status = "failed"
		executionTime := int(time.Since(startTime).Milliseconds())
		queryRecord.ExecutionTime = &executionTime

		qes.queryRepo.SaveQueryHistory(ctx, queryRecord)

		// 记录审计日志
		qes.auditService.LogQueryExecution(ctx, &QueryAuditLog{
			QueryID:       dsl.QueryID,
			UserID:        userID,
			Action:        "execute_query",
			Status:        "failed",
			ErrorMessage:  err.Error(),
			ExecutionTime: time.Since(startTime),
			Timestamp:     time.Now(),
		})

		return nil, fmt.Errorf("query execution failed: %w", err)
	}

	// 更新查询状态为成功
	queryRecord.Status = "completed"
	executionTime := int(result.ExecutionTime.Milliseconds())
	queryRecord.ExecutionTime = &executionTime
	recordCount := result.Total
	queryRecord.RecordCount = &recordCount

	qes.queryRepo.SaveQueryHistory(ctx, queryRecord)

	// 记录审计日志
	qes.auditService.LogQueryExecution(ctx, &QueryAuditLog{
		QueryID:       dsl.QueryID,
		UserID:        userID,
		Action:        "execute_query",
		Status:        "success",
		ExecutionTime: result.ExecutionTime,
		RowsReturned:  result.Total,
		TablesUsed:    result.Metadata.TablesUsed,
		FieldsUsed:    result.Metadata.FieldsUsed,
		Timestamp:     time.Now(),
	})

	// 转换为医学查询响应
	response := &model.MedicalQueryResponse{
		QueryID:       uuid.MustParse(result.QueryID),
		StudyName:     result.StudyName,
		Data:          result.Data,
		Total:         result.Total,
		ExecutionTime: int(result.ExecutionTime.Milliseconds()),
		Status:        result.Status,
		Metadata:      make(map[string]interface{}),
	}

	// 添加数据源信息
	if dsStatus, exists := qes.queryEngine.GetDataSourceStatus(dsl.DatasetID); exists {
		response.DataSource = &model.DataSourceInfo{
			ID:          dsl.DatasetID,
			Name:        dsStatus.Name,
			IsHealthy:   dsStatus.IsHealthy,
			Description: dsStatus.Description,
		}
	}

	// 添加元数据
	response.Metadata["tables_used"] = result.Metadata.TablesUsed
	response.Metadata["fields_used"] = result.Metadata.FieldsUsed
	response.Metadata["rows_scanned"] = result.Metadata.RowsScanned
	response.Metadata["cache_hit"] = result.Metadata.CacheHit
	response.Metadata["performance"] = result.Metadata.Performance

	logger.Info("Medical query execution completed", map[string]interface{}{
		"query_id":       dsl.QueryID,
		"execution_time": result.ExecutionTime,
		"rows_returned":  result.Total,
		"status":         result.Status,
		"user_id":        userID,
	})

	return response, nil
}

// GetQueryHistory 获取查询历史
func (qes *QueryExecutionService) GetQueryHistory(ctx context.Context, userID string, limit, offset int) ([]*model.QueryHistory, error) {
	userUUID := uuid.MustParse(userID)
	histories, _, err := qes.queryRepo.GetQueryHistory(ctx, userUUID, limit, offset)
	if err != nil {
		return nil, err
	}

	// 转换为指针切片
	result := make([]*model.QueryHistory, len(histories))
	for i := range histories {
		result[i] = &histories[i]
	}
	return result, nil
}

// GetQueryResult 获取查询结果
func (qes *QueryExecutionService) GetQueryResult(ctx context.Context, queryID, userID string) (*model.MedicalQueryResponse, error) {
	// 获取查询历史
	queryUUID := uuid.MustParse(queryID)
	queryHistory, err := qes.queryRepo.GetQueryHistoryByID(ctx, queryUUID)
	if err != nil {
		return nil, fmt.Errorf("failed to get query history: %w", err)
	}

	// 检查权限
	userUUID := uuid.MustParse(userID)
	if queryHistory.UserID != userUUID {
		return nil, fmt.Errorf("access denied: query belongs to different user")
	}

	// 如果查询还在运行中
	if queryHistory.Status == "running" {
		studyName := ""
		if queryHistory.Name != nil {
			studyName = *queryHistory.Name
		}
		return &model.MedicalQueryResponse{
			QueryID:   queryHistory.ID,
			StudyName: studyName,
			Status:    "running",
		}, nil
	}

	// 如果查询失败
	if queryHistory.Status == "failed" {
		studyName := ""
		if queryHistory.Name != nil {
			studyName = *queryHistory.Name
		}
		return &model.MedicalQueryResponse{
			QueryID:   queryHistory.ID,
			StudyName: studyName,
			Status:    "error",
			Error: &model.QueryError{
				Code:        "EXECUTION_FAILED",
				Message:     "Query execution failed",
				Recoverable: true,
			},
		}, nil
	}

	// 查询成功，但需要重新获取结果（因为结果可能很大，不存储在历史记录中）
	// 这里可以从缓存或重新执行查询获取结果
	studyName := ""
	if queryHistory.Name != nil {
		studyName = *queryHistory.Name
	}
	total := 0
	if queryHistory.RecordCount != nil {
		total = *queryHistory.RecordCount
	}
	executionTime := 0
	if queryHistory.ExecutionTime != nil {
		executionTime = *queryHistory.ExecutionTime
	}

	return &model.MedicalQueryResponse{
		QueryID:       queryHistory.ID,
		StudyName:     studyName,
		Status:        "completed",
		Total:         total,
		ExecutionTime: executionTime,
		Metadata:      make(map[string]interface{}),
	}, nil
}

// CancelQuery 取消查询
func (qes *QueryExecutionService) CancelQuery(ctx context.Context, queryID, userID string) error {
	// 获取查询历史
	queryUUID := uuid.MustParse(queryID)
	queryHistory, err := qes.queryRepo.GetQueryHistoryByID(ctx, queryUUID)
	if err != nil {
		return fmt.Errorf("failed to get query history: %w", err)
	}

	// 检查权限
	userUUID := uuid.MustParse(userID)
	if queryHistory.UserID != userUUID {
		return fmt.Errorf("access denied: query belongs to different user")
	}

	// 只能取消运行中的查询
	if queryHistory.Status != "running" {
		return fmt.Errorf("cannot cancel query with status: %s", queryHistory.Status)
	}

	// 取消查询引擎中的查询
	if err := qes.queryEngine.CancelQuery(queryID); err != nil {
		return fmt.Errorf("failed to cancel query in engine: %w", err)
	}

	// 更新查询状态
	queryHistory.Status = "cancelled"

	if err := qes.queryRepo.SaveQueryHistory(ctx, queryHistory); err != nil {
		logger.Error("Failed to update query history after cancellation", err, map[string]interface{}{
			"query_id": queryID,
			"user_id":  userID,
		})
	}

	// 记录审计日志
	qes.auditService.LogQueryExecution(ctx, &QueryAuditLog{
		QueryID:   queryID,
		UserID:    userID,
		Action:    "cancel_query",
		Status:    "success",
		Timestamp: time.Now(),
	})

	logger.Info("Query cancelled", map[string]interface{}{
		"query_id": queryID,
		"user_id":  userID,
	})

	return nil
}

// GetQueryStats 获取查询统计
func (qes *QueryExecutionService) GetQueryStats(ctx context.Context, userID string) (*QueryStats, error) {
	// 获取缓存统计
	cacheStats := qes.queryEngine.GetCacheStats()

	// 这里应该实现真实的统计查询，目前返回模拟数据
	return &QueryStats{
		TotalQueries:      0,
		SuccessfulQueries: 0,
		FailedQueries:     0,
		CancelledQueries:  0,
		AvgExecutionTime:  0,
		TotalDataExported: 0,
		CacheHitRate:      cacheStats.HitRate,
		LastQueryTime:     nil,
	}, nil
}

// QueryStats 查询统计
type QueryStats struct {
	TotalQueries      int        `json:"total_queries"`
	SuccessfulQueries int        `json:"successful_queries"`
	FailedQueries     int        `json:"failed_queries"`
	CancelledQueries  int        `json:"cancelled_queries"`
	AvgExecutionTime  float64    `json:"avg_execution_time"`
	TotalDataExported int64      `json:"total_data_exported"`
	CacheHitRate      float64    `json:"cache_hit_rate"`
	LastQueryTime     *time.Time `json:"last_query_time"`
}

// ValidateQueryDSL 验证查询DSL
func (qes *QueryExecutionService) ValidateQueryDSL(ctx context.Context, dsl *query.MedicalQueryDSL) (*query.QueryValidationResult, error) {
	validator := query.NewQueryValidator()
	result := validator.ValidateQuery(dsl)
	return &result, nil
}

// GetDataSourceStatus 获取数据源状态
func (qes *QueryExecutionService) GetDataSourceStatus(datasetID string) (*database.DataSourceStatus, bool) {
	return qes.queryEngine.GetDataSourceStatus(datasetID)
}
