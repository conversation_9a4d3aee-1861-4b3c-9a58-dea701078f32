package api

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"

	"github.com/chungzy/medical-data-platform/internal/config"
	"github.com/chungzy/medical-data-platform/internal/model"
	"github.com/chungzy/medical-data-platform/internal/service"
	"github.com/chungzy/medical-data-platform/pkg/jwt"
	"github.com/chungzy/medical-data-platform/pkg/logger"
)

type AuthController struct {
	userService *service.UserService
	jwtManager  *jwt.JWTManager
	config      *config.Config
}

func NewAuthController(userService *service.UserService, jwtManager *jwt.JWTManager, config *config.Config) *AuthController {
	return &AuthController{
		userService: userService,
		jwtManager:  jwtManager,
		config:      config,
	}
}

// Register 用户注册
func (ac *AuthController) Register(c *gin.Context) {
	c.Set("business_operation", "user_register")

	logger.InfoWithGinContext(c, "User registration attempt")

	var req model.UserRegistrationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to parse registration request")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	logger.InfoWithGinContext(c, "Registration request parsed", map[string]interface{}{
		"username": req.Name,
		"email":    req.Email,
	})

	// 检查邮箱是否已存在
	existingUser, _ := ac.userService.GetByEmail(c.Request.Context(), req.Email)
	if existingUser != nil {
		logger.WarnWithGinContext(c, "Email already exists", map[string]interface{}{
			"email": req.Email,
		})
		c.JSON(http.StatusConflict, gin.H{"error": "Email already exists"})
		return
	}

	// 密码加密
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to hash password")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to hash password"})
		return
	}

	// 创建用户
	user := &model.User{
		ID:           uuid.New(),
		Name:         req.Name,
		Email:        req.Email,
		Phone:        req.Phone,
		PasswordHash: string(hashedPassword),
		Role:         req.Role,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	if err := ac.userService.Create(c.Request.Context(), user); err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to create user", map[string]interface{}{
			"username": req.Name,
			"email":    req.Email,
		})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create user", "details": err.Error()})
		return
	}

	logger.InfoWithGinContext(c, "User created successfully", map[string]interface{}{
		"user_id":  user.ID,
		"username": user.Name,
		"email":    user.Email,
		"role":     user.Role,
	})

	// 生成JWT token
	token, err := ac.jwtManager.GenerateToken(user.ID, user.Email, user.Role)
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to generate JWT token for new user", map[string]interface{}{
			"user_id":  user.ID,
			"username": user.Name,
		})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
		return
	}

	logger.InfoWithGinContext(c, "JWT token generated successfully", map[string]interface{}{
		"user_id":  user.ID,
		"username": user.Name,
	})

	// 返回响应
	response := model.AuthResponse{
		Token: token,
		User: model.UserResponse{
			ID:        user.ID,
			Name:      user.Name,
			Email:     user.Email,
			Phone:     user.Phone,
			Role:      user.Role,
			CreatedAt: user.CreatedAt,
		},
	}

	logger.InfoWithGinContext(c, "User registration successful", map[string]interface{}{
		"user_id":  user.ID,
		"username": user.Name,
		"email":    user.Email,
		"role":     user.Role,
	})

	c.JSON(http.StatusCreated, response)
}

// Login 用户登录
func (ac *AuthController) Login(c *gin.Context) {
	c.Set("business_operation", "user_login")

	logger.InfoWithGinContext(c, "User login attempt")

	var req model.UserLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to parse login request")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	logger.InfoWithGinContext(c, "Login request parsed", map[string]interface{}{
		"username": req.Email,
	})

	// 查找用户
	user, err := ac.userService.GetByEmail(c.Request.Context(), req.Email)
	if err != nil {
		logger.WarnWithGinContext(c, "Login failed - invalid credentials", map[string]interface{}{
			"username": req.Email,
			"error":    err.Error(),
		})
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid email or password"})
		return
	}

	// 验证密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(req.Password)); err != nil {
		logger.WarnWithGinContext(c, "Login failed - invalid credentials", map[string]interface{}{
			"username": req.Email,
			"error":    err.Error(),
		})
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid email or password"})
		return
	}

	logger.InfoWithGinContext(c, "User validated successfully", map[string]interface{}{
		"user_id":  user.ID,
		"username": user.Email,
		"role":     user.Role,
	})

	// 生成JWT token
	token, err := ac.jwtManager.GenerateToken(user.ID, user.Email, user.Role)
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to generate JWT token", map[string]interface{}{
			"user_id":  user.ID,
			"username": user.Email,
		})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
		return
	}

	logger.InfoWithGinContext(c, "JWT token generated successfully", map[string]interface{}{
		"user_id":  user.ID,
		"username": user.Email,
	})

	// 返回响应
	response := model.AuthResponse{
		Token: token,
		User: model.UserResponse{
			ID:        user.ID,
			Name:      user.Name,
			Email:     user.Email,
			Phone:     user.Phone,
			Role:      user.Role,
			CreatedAt: user.CreatedAt,
		},
	}

	logger.InfoWithGinContext(c, "User login successful", map[string]interface{}{
		"user_id":  user.ID,
		"username": user.Email,
		"role":     user.Role,
	})

	c.JSON(http.StatusOK, response)
}

// GetMe 获取当前用户信息
func (ac *AuthController) GetMe(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	user, err := ac.userService.GetByID(c.Request.Context(), userID.(uuid.UUID))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	response := model.UserResponse{
		ID:        user.ID,
		Name:      user.Name,
		Email:     user.Email,
		Phone:     user.Phone,
		Role:      user.Role,
		CreatedAt: user.CreatedAt,
	}

	c.JSON(http.StatusOK, response)
}
