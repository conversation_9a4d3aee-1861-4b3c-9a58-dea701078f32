package worker

import (
	"github.com/chungzy/medical-data-platform/internal/config"
	"github.com/chungzy/medical-data-platform/internal/worker/file_cleanup"
	"github.com/chungzy/medical-data-platform/pkg/logger"
)

// InitializeWorkers 初始化并注册所有worker
func InitializeWorkers(cfg *config.Config) *WorkerManager {
	logger.Info("Initializing worker manager and registering workers")

	// 创建worker管理器
	workerManager := NewWorkerManager(cfg)

	// 注册文件清理worker
	fileCleanupWorker := file_cleanup.NewFileCleanupWorker(cfg)
	workerManager.RegisterWorker(fileCleanupWorker)

	logger.Info("All workers registered successfully", map[string]interface{}{
		"registered_workers": workerManager.GetWorkerNames(),
	})

	return workerManager
}
