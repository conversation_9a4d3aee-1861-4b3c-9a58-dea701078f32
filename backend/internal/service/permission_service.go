package service

import (
	"context"
	"fmt"

	"github.com/chungzy/medical-data-platform/internal/model"
	"github.com/chungzy/medical-data-platform/internal/repository"
	"github.com/chungzy/medical-data-platform/pkg/logger"
	"github.com/google/uuid"
)

type PermissionService struct {
	userRepo       repository.UserRepository
	permissionRepo PermissionRepository
}

type PermissionRepository interface {
	GetUserPermissions(ctx context.Context, userID uuid.UUID) (*model.UserPermissions, error)
	GetUserRoles(ctx context.Context, userID uuid.UUID) ([]model.Role, error)
	CheckDatasetAccess(ctx context.Context, userID uuid.UUID, datasetID string) (bool, error)
	CreateRole(ctx context.Context, role *model.Role) error
	CreatePermission(ctx context.Context, permission *model.Permission) error
	AssignRoleToUser(ctx context.Context, userID, roleID uuid.UUID) error
	AssignPermissionToRole(ctx context.Context, roleID, permissionID uuid.UUID) error
	GetRoleByCode(ctx context.Context, code string) (*model.Role, error)
	GetPermissionByCode(ctx context.Context, code string) (*model.Permission, error)
	GetAllRoles(ctx context.Context) ([]model.Role, error)
	GetAllPermissions(ctx context.Context) ([]model.Permission, error)
}

func NewPermissionService(userRepo repository.UserRepository, permissionRepo PermissionRepository) *PermissionService {
	return &PermissionService{
		userRepo:       userRepo,
		permissionRepo: permissionRepo,
	}
}

// CheckPermission 检查用户是否有指定权限
func (ps *PermissionService) CheckPermission(ctx context.Context, userID uuid.UUID, permission string, resource ...string) (bool, error) {
	logger.Info("Checking permission", map[string]interface{}{
		"user_id":    userID,
		"permission": permission,
		"resource":   resource,
	})

	// 获取用户权限
	userPermissions, err := ps.permissionRepo.GetUserPermissions(ctx, userID)
	if err != nil {
		return false, fmt.Errorf("failed to get user permissions: %w", err)
	}

	// 检查直接权限
	for _, perm := range userPermissions.Permissions {
		if perm.Code == permission {
			return true, nil
		}
	}

	// 检查角色权限
	for _, role := range userPermissions.Roles {
		for _, perm := range role.Permissions {
			if perm.Code == permission {
				return true, nil
			}
		}
	}

	return false, nil
}

// CheckDatasetAccess 检查用户对数据集的访问权限
func (ps *PermissionService) CheckDatasetAccess(ctx context.Context, userID uuid.UUID, datasetID string, accessType string) (bool, error) {
	logger.Info("Checking dataset access", map[string]interface{}{
		"user_id":     userID,
		"dataset_id":  datasetID,
		"access_type": accessType,
	})

	// 检查数据集特定权限
	hasAccess, err := ps.permissionRepo.CheckDatasetAccess(ctx, userID, datasetID)
	if err != nil {
		return false, fmt.Errorf("failed to check dataset access: %w", err)
	}

	if hasAccess {
		return true, nil
	}

	// 检查通用数据集权限
	var requiredPermission string
	switch accessType {
	case "read":
		requiredPermission = model.PermissionDatasetRead
	case "write":
		requiredPermission = model.PermissionDatasetWrite
	case "admin":
		requiredPermission = model.PermissionDatasetAdmin
	default:
		requiredPermission = model.PermissionDatasetRead
	}

	return ps.CheckPermission(ctx, userID, requiredPermission, datasetID)
}

// GetUserPermissions 获取用户的所有权限
func (ps *PermissionService) GetUserPermissions(ctx context.Context, userID uuid.UUID) (*model.UserPermissions, error) {
	return ps.permissionRepo.GetUserPermissions(ctx, userID)
}

// HasRole 检查用户是否有指定角色
func (ps *PermissionService) HasRole(ctx context.Context, userID uuid.UUID, roleCode string) (bool, error) {
	roles, err := ps.permissionRepo.GetUserRoles(ctx, userID)
	if err != nil {
		return false, err
	}

	for _, role := range roles {
		if role.Code == roleCode {
			return true, nil
		}
	}

	return false, nil
}

// IsAdmin 检查用户是否为管理员
func (ps *PermissionService) IsAdmin(ctx context.Context, userID uuid.UUID) (bool, error) {
	return ps.HasRole(ctx, userID, model.RoleAdmin)
}

// CanAccessResource 检查用户是否可以访问指定资源
func (ps *PermissionService) CanAccessResource(ctx context.Context, userID uuid.UUID, resource, action string) (bool, error) {
	permission := fmt.Sprintf("%s:%s", resource, action)
	return ps.CheckPermission(ctx, userID, permission)
}

// GetAccessibleDatasets 获取用户可访问的数据集列表
func (ps *PermissionService) GetAccessibleDatasets(ctx context.Context, userID uuid.UUID) ([]string, error) {
	userPermissions, err := ps.permissionRepo.GetUserPermissions(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user permissions: %w", err)
	}

	// 如果用户有数据集读取权限，返回所有数据集
	hasDatasetRead, err := ps.CheckPermission(ctx, userID, model.PermissionDatasetRead)
	if err != nil {
		return nil, err
	}

	if hasDatasetRead {
		// 返回所有可用数据集（这里应该从配置或数据库获取）
		return []string{"mimic-iv", "eicu", "nhanes", "pic"}, nil
	}

	// 返回用户特定的数据集权限
	return userPermissions.Datasets, nil
}

// ValidateRoleHierarchy 验证角色层级关系
func (ps *PermissionService) ValidateRoleHierarchy(ctx context.Context, operatorUserID, targetUserID uuid.UUID) (bool, error) {
	operatorRoles, err := ps.permissionRepo.GetUserRoles(ctx, operatorUserID)
	if err != nil {
		return false, err
	}

	targetRoles, err := ps.permissionRepo.GetUserRoles(ctx, targetUserID)
	if err != nil {
		return false, err
	}

	// 获取操作者的最高角色级别
	maxOperatorLevel := 0
	for _, role := range operatorRoles {
		if level := model.GetRoleLevel(role.Code); level > maxOperatorLevel {
			maxOperatorLevel = level
		}
	}

	// 获取目标用户的最高角色级别
	maxTargetLevel := 0
	for _, role := range targetRoles {
		if level := model.GetRoleLevel(role.Code); level > maxTargetLevel {
			maxTargetLevel = level
		}
	}

	// 操作者的角色级别必须高于目标用户
	return maxOperatorLevel > maxTargetLevel, nil
}

// InitializeDefaultRoles 初始化默认角色和权限
func (ps *PermissionService) InitializeDefaultRoles(ctx context.Context) error {
	logger.Info("Initializing default roles and permissions")

	// 创建默认权限
	defaultPermissions := []model.Permission{
		{ID: uuid.New(), Name: "数据集读取", Code: model.PermissionDatasetRead, Resource: "dataset", Action: "read"},
		{ID: uuid.New(), Name: "数据集写入", Code: model.PermissionDatasetWrite, Resource: "dataset", Action: "write"},
		{ID: uuid.New(), Name: "数据集管理", Code: model.PermissionDatasetAdmin, Resource: "dataset", Action: "admin"},
		{ID: uuid.New(), Name: "执行查询", Code: model.PermissionQueryExecute, Resource: "query", Action: "execute"},
		{ID: uuid.New(), Name: "保存查询", Code: model.PermissionQuerySave, Resource: "query", Action: "save"},
		{ID: uuid.New(), Name: "删除查询", Code: model.PermissionQueryDelete, Resource: "query", Action: "delete"},
		{ID: uuid.New(), Name: "分享查询", Code: model.PermissionQueryShare, Resource: "query", Action: "share"},
		{ID: uuid.New(), Name: "创建导出", Code: model.PermissionExportCreate, Resource: "export", Action: "create"},
		{ID: uuid.New(), Name: "下载导出", Code: model.PermissionExportDownload, Resource: "export", Action: "download"},
		{ID: uuid.New(), Name: "用户读取", Code: model.PermissionUserRead, Resource: "user", Action: "read"},
		{ID: uuid.New(), Name: "用户写入", Code: model.PermissionUserWrite, Resource: "user", Action: "write"},
		{ID: uuid.New(), Name: "用户删除", Code: model.PermissionUserDelete, Resource: "user", Action: "delete"},
		{ID: uuid.New(), Name: "系统管理", Code: model.PermissionSystemAdmin, Resource: "system", Action: "admin"},
		{ID: uuid.New(), Name: "系统配置", Code: model.PermissionSystemConfig, Resource: "system", Action: "config"},
	}

	for _, perm := range defaultPermissions {
		if err := ps.permissionRepo.CreatePermission(ctx, &perm); err != nil {
			logger.Error("Failed to create permission", map[string]interface{}{
				"permission": perm.Code,
				"error":      err.Error(),
			})
		}
	}

	// 创建默认角色
	defaultRoles := []model.Role{
		{ID: uuid.New(), Name: "用户", Code: model.RoleAdmin, Level: 1, IsSystem: true},
		{ID: uuid.New(), Name: "系统管理员", Code: model.RoleAdmin, Level: 2, IsSystem: true},
	}

	for _, role := range defaultRoles {
		if err := ps.permissionRepo.CreateRole(ctx, &role); err != nil {
			logger.Error("Failed to create role", map[string]interface{}{
				"role":  role.Code,
				"error": err.Error(),
			})
		}
	}

	logger.Info("Default roles and permissions initialized successfully")
	return nil
}
