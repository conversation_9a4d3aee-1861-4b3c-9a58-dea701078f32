package query

import (
	"encoding/json"
	"time"
)

// MedicalQueryDSL 医学查询DSL结构
type MedicalQueryDSL struct {
	// 基础信息
	QueryID     string `json:"query_id"`
	StudyName   string `json:"study_name"`
	Description string `json:"description"`
	DatasetID   string `json:"dataset_id"`

	// 人群定义
	Cohort CohortDefinition `json:"cohort"`

	// 字段选择
	Fields []FieldSelection `json:"fields"`

	// 时间范围
	TimeRange *TimeRange `json:"time_range,omitempty"`

	// 过滤条件
	Filters []FilterCondition `json:"filters"`

	// 排序
	OrderBy []OrderByClause `json:"order_by"`

	// 限制
	Limit  *int `json:"limit,omitempty"`
	Offset *int `json:"offset,omitempty"`

	// 分组
	GroupBy []string `json:"group_by"`

	// 聚合
	Aggregations []Aggregation `json:"aggregations"`

	// 导出选项
	Export *ExportOptions `json:"export,omitempty"`
}

// CohortDefinition 人群定义
type CohortDefinition struct {
	// 主表
	PrimaryTable string `json:"primary_table"`

	// 包含条件
	InclusionCriteria []CohortCriteria `json:"inclusion_criteria"`

	// 排除条件
	ExclusionCriteria []CohortCriteria `json:"exclusion_criteria"`

	// 年龄范围
	AgeRange *AgeRange `json:"age_range,omitempty"`

	// 性别
	Gender *string `json:"gender,omitempty"`

	// 最小样本量
	MinSampleSize *int `json:"min_sample_size,omitempty"`
}

// CohortCriteria 队列标准
type CohortCriteria struct {
	ID          string      `json:"id"`
	Field       string      `json:"field"`
	Operator    string      `json:"operator"`
	Value       interface{} `json:"value"`
	Values      []interface{} `json:"values,omitempty"`
	Logic       string      `json:"logic"` // AND, OR
	Description string      `json:"description"`
	Table       string      `json:"table"`
}

// FieldSelection 字段选择
type FieldSelection struct {
	Field       string `json:"field"`
	Alias       string `json:"alias,omitempty"`
	Table       string `json:"table"`
	Function    string `json:"function,omitempty"` // COUNT, AVG, MAX, MIN, SUM
	Description string `json:"description"`
	Required    bool   `json:"required"`
}

// TimeRange 时间范围
type TimeRange struct {
	StartDate *time.Time `json:"start_date,omitempty"`
	EndDate   *time.Time `json:"end_date,omitempty"`
	Field     string     `json:"field"`
	Table     string     `json:"table"`
	Type      string     `json:"type"` // admission, discharge, lab, chart
}

// FilterCondition 过滤条件
type FilterCondition struct {
	Field    string      `json:"field"`
	Operator string      `json:"operator"`
	Value    interface{} `json:"value"`
	Values   []interface{} `json:"values,omitempty"`
	Logic    string      `json:"logic"` // AND, OR
	Table    string      `json:"table"`
}

// OrderByClause 排序子句
type OrderByClause struct {
	Field     string `json:"field"`
	Direction string `json:"direction"` // ASC, DESC
	Table     string `json:"table"`
}

// Aggregation 聚合
type Aggregation struct {
	Function string `json:"function"` // COUNT, AVG, MAX, MIN, SUM
	Field    string `json:"field"`
	Alias    string `json:"alias"`
	Table    string `json:"table"`
}

// AgeRange 年龄范围
type AgeRange struct {
	Min *int `json:"min,omitempty"`
	Max *int `json:"max,omitempty"`
}

// ExportOptions 导出选项
type ExportOptions struct {
	Format      string            `json:"format"`      // CSV, JSON, XLSX
	Filename    string            `json:"filename"`
	Compression bool              `json:"compression"`
	Headers     map[string]string `json:"headers"`
	MaxRows     *int              `json:"max_rows,omitempty"`
}

// QueryValidationError 查询验证错误
type QueryValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
	Code    string `json:"code"`
}

// QueryValidationResult 查询验证结果
type QueryValidationResult struct {
	Valid  bool                   `json:"valid"`
	Errors []QueryValidationError `json:"errors"`
}

// 操作符常量
const (
	// 比较操作符
	OpEqual              = "eq"
	OpNotEqual           = "ne"
	OpGreaterThan        = "gt"
	OpGreaterThanOrEqual = "gte"
	OpLessThan           = "lt"
	OpLessThanOrEqual    = "lte"
	OpLike               = "like"
	OpNotLike            = "not_like"
	OpIn                 = "in"
	OpNotIn              = "not_in"
	OpIsNull             = "is_null"
	OpIsNotNull          = "is_not_null"
	OpBetween            = "between"
	OpNotBetween         = "not_between"

	// 逻辑操作符
	LogicAnd = "AND"
	LogicOr  = "OR"

	// 聚合函数
	FuncCount   = "COUNT"
	FuncSum     = "SUM"
	FuncAvg     = "AVG"
	FuncMin     = "MIN"
	FuncMax     = "MAX"
	FuncStdDev  = "STDDEV"
	FuncVariance = "VARIANCE"

	// 排序方向
	OrderAsc  = "ASC"
	OrderDesc = "DESC"

	// 导出格式
	FormatCSV  = "CSV"
	FormatJSON = "JSON"
	FormatXLSX = "XLSX"

	// 时间类型
	TimeTypeAdmission = "admission"
	TimeTypeDischarge = "discharge"
	TimeTypeLab       = "lab"
	TimeTypeChart     = "chart"
)

// DSLBuilder DSL构建器
type DSLBuilder struct {
	dsl *MedicalQueryDSL
}

// NewDSLBuilder 创建DSL构建器
func NewDSLBuilder() *DSLBuilder {
	return &DSLBuilder{
		dsl: &MedicalQueryDSL{
			Fields:       []FieldSelection{},
			Filters:      []FilterCondition{},
			OrderBy:      []OrderByClause{},
			GroupBy:      []string{},
			Aggregations: []Aggregation{},
		},
	}
}

// SetBasicInfo 设置基础信息
func (b *DSLBuilder) SetBasicInfo(queryID, studyName, description, datasetID string) *DSLBuilder {
	b.dsl.QueryID = queryID
	b.dsl.StudyName = studyName
	b.dsl.Description = description
	b.dsl.DatasetID = datasetID
	return b
}

// SetCohort 设置人群定义
func (b *DSLBuilder) SetCohort(cohort CohortDefinition) *DSLBuilder {
	b.dsl.Cohort = cohort
	return b
}

// AddField 添加字段
func (b *DSLBuilder) AddField(field, table, alias, function, description string, required bool) *DSLBuilder {
	b.dsl.Fields = append(b.dsl.Fields, FieldSelection{
		Field:       field,
		Table:       table,
		Alias:       alias,
		Function:    function,
		Description: description,
		Required:    required,
	})
	return b
}

// AddFilter 添加过滤条件
func (b *DSLBuilder) AddFilter(field, table, operator, logic string, value interface{}) *DSLBuilder {
	b.dsl.Filters = append(b.dsl.Filters, FilterCondition{
		Field:    field,
		Table:    table,
		Operator: operator,
		Logic:    logic,
		Value:    value,
	})
	return b
}

// SetTimeRange 设置时间范围
func (b *DSLBuilder) SetTimeRange(field, table, timeType string, startDate, endDate *time.Time) *DSLBuilder {
	b.dsl.TimeRange = &TimeRange{
		Field:     field,
		Table:     table,
		Type:      timeType,
		StartDate: startDate,
		EndDate:   endDate,
	}
	return b
}

// AddOrderBy 添加排序
func (b *DSLBuilder) AddOrderBy(field, table, direction string) *DSLBuilder {
	b.dsl.OrderBy = append(b.dsl.OrderBy, OrderByClause{
		Field:     field,
		Table:     table,
		Direction: direction,
	})
	return b
}

// SetLimit 设置限制
func (b *DSLBuilder) SetLimit(limit, offset int) *DSLBuilder {
	b.dsl.Limit = &limit
	if offset > 0 {
		b.dsl.Offset = &offset
	}
	return b
}

// AddAggregation 添加聚合
func (b *DSLBuilder) AddAggregation(function, field, table, alias string) *DSLBuilder {
	b.dsl.Aggregations = append(b.dsl.Aggregations, Aggregation{
		Function: function,
		Field:    field,
		Table:    table,
		Alias:    alias,
	})
	return b
}

// SetExport 设置导出选项
func (b *DSLBuilder) SetExport(format, filename string, compression bool, maxRows *int) *DSLBuilder {
	b.dsl.Export = &ExportOptions{
		Format:      format,
		Filename:    filename,
		Compression: compression,
		MaxRows:     maxRows,
	}
	return b
}

// Build 构建DSL
func (b *DSLBuilder) Build() *MedicalQueryDSL {
	return b.dsl
}

// ToJSON 转换为JSON
func (dsl *MedicalQueryDSL) ToJSON() ([]byte, error) {
	return json.MarshalIndent(dsl, "", "  ")
}

// FromJSON 从JSON解析
func FromJSON(data []byte) (*MedicalQueryDSL, error) {
	var dsl MedicalQueryDSL
	err := json.Unmarshal(data, &dsl)
	return &dsl, err
}
