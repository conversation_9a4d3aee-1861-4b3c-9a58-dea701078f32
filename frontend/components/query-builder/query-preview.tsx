"use client"

import { useState, useMemo } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { 
  Play, 
  Eye, 
  Code, 
  BarChart3, 
  Clock, 
  Database,
  AlertTriangle,
  CheckCircle2,
  Copy,
  Download
} from "lucide-react"
import { toast } from "sonner"

interface QueryDSL {
  dataset_id: string
  study_name: string
  description: string
  cohort: any
  fields: any[]
  filters: any[]
  time_range?: any
  group_by?: string[]
  order_by?: any[]
  limit?: number
}

interface QueryPreviewProps {
  queryDSL: QueryDSL
  onExecute: () => void
  isExecuting: boolean
}

export function QueryPreview({ queryDSL, onExecute, isExecuting }: QueryPreviewProps) {
  const [activeTab, setActiveTab] = useState("dsl")

  // 计算查询复杂度
  const queryComplexity = useMemo(() => {
    let score = 0
    let factors = []

    // 字段数量影响
    const fieldCount = queryDSL.fields?.length || 0
    if (fieldCount > 20) {
      score += 3
      factors.push("大量字段")
    } else if (fieldCount > 10) {
      score += 2
      factors.push("较多字段")
    } else if (fieldCount > 5) {
      score += 1
      factors.push("中等字段数")
    }

    // 过滤条件影响
    const filterCount = queryDSL.filters?.length || 0
    if (filterCount > 10) {
      score += 3
      factors.push("复杂过滤")
    } else if (filterCount > 5) {
      score += 2
      factors.push("多个过滤条件")
    } else if (filterCount > 2) {
      score += 1
      factors.push("基本过滤")
    }

    // 聚合操作影响
    if (queryDSL.group_by && queryDSL.group_by.length > 0) {
      score += 2
      factors.push("分组聚合")
    }

    // 排序影响
    if (queryDSL.order_by && queryDSL.order_by.length > 0) {
      score += 1
      factors.push("排序操作")
    }

    // 时间范围影响
    if (queryDSL.time_range) {
      score += 1
      factors.push("时间过滤")
    }

    let level = "简单"
    let color = "green"
    if (score >= 8) {
      level = "复杂"
      color = "red"
    } else if (score >= 5) {
      level = "中等"
      color = "yellow"
    }

    return { score, level, color, factors }
  }, [queryDSL])

  // 预估执行时间
  const estimatedTime = useMemo(() => {
    const baseTime = 500 // 基础时间 500ms
    const fieldMultiplier = (queryDSL.fields?.length || 0) * 50
    const filterMultiplier = (queryDSL.filters?.length || 0) * 100
    const complexityMultiplier = queryComplexity.score * 200

    const total = baseTime + fieldMultiplier + filterMultiplier + complexityMultiplier
    
    if (total < 1000) return "< 1秒"
    if (total < 5000) return "1-5秒"
    if (total < 10000) return "5-10秒"
    if (total < 30000) return "10-30秒"
    return "> 30秒"
  }, [queryDSL, queryComplexity])

  // 生成SQL预览
  const generateSQL = () => {
    const fields = queryDSL.fields?.map(f => f.field?.name || f.name || f).join(", ") || "*"
    const tables = [...new Set(queryDSL.fields?.map(f => f.field?.table || f.table).filter(Boolean))]
    const mainTable = tables[0] || "patients"
    
    let sql = `SELECT ${fields}\nFROM ${mainTable}`
    
    // 添加JOIN
    if (tables.length > 1) {
      tables.slice(1).forEach(table => {
        sql += `\nLEFT JOIN ${table} ON ${mainTable}.subject_id = ${table}.subject_id`
      })
    }
    
    // 添加WHERE条件
    if (queryDSL.filters && queryDSL.filters.length > 0) {
      const conditions = queryDSL.filters.map((filter, index) => {
        const prefix = index > 0 ? ` ${filter.logic || 'AND'} ` : ""
        const value = filter.values ? filter.values.join(",") : filter.value
        return `${prefix}${filter.field} ${filter.operator} ${value}`
      }).join("")
      sql += `\nWHERE ${conditions}`
    }
    
    // 添加GROUP BY
    if (queryDSL.group_by && queryDSL.group_by.length > 0) {
      sql += `\nGROUP BY ${queryDSL.group_by.join(", ")}`
    }
    
    // 添加ORDER BY
    if (queryDSL.order_by && queryDSL.order_by.length > 0) {
      const orderClauses = queryDSL.order_by.map(order => 
        `${order.field} ${order.direction || 'ASC'}`
      ).join(", ")
      sql += `\nORDER BY ${orderClauses}`
    }
    
    // 添加LIMIT
    if (queryDSL.limit) {
      sql += `\nLIMIT ${queryDSL.limit}`
    }
    
    return sql
  }

  // 复制到剪贴板
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      toast.success("已复制到剪贴板")
    }).catch(() => {
      toast.error("复制失败")
    })
  }

  // 导出查询
  const exportQuery = (format: string) => {
    const data = format === "sql" ? generateSQL() : JSON.stringify(queryDSL, null, 2)
    const blob = new Blob([data], { type: "text/plain" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `query.${format}`
    a.click()
    URL.revokeObjectURL(url)
    toast.success(`查询已导出为 ${format.toUpperCase()} 文件`)
  }

  // 验证查询
  const validateQuery = () => {
    const issues = []
    
    if (!queryDSL.fields || queryDSL.fields.length === 0) {
      issues.push("未选择任何字段")
    }
    
    if (queryDSL.fields && queryDSL.fields.length > 50) {
      issues.push("字段数量过多，可能影响性能")
    }
    
    if (queryDSL.filters && queryDSL.filters.length > 20) {
      issues.push("过滤条件过多，可能影响性能")
    }
    
    if (!queryDSL.limit || queryDSL.limit > 10000) {
      issues.push("建议设置合理的结果限制")
    }
    
    return issues
  }

  const validationIssues = validateQuery()

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900">查询预览</h2>
        <p className="text-gray-600 mt-1">
          预览和验证您的查询，确认无误后执行
        </p>
      </div>

      {/* 查询统计 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Database className="h-4 w-4 text-blue-600" />
              <div>
                <div className="text-sm font-medium">字段数量</div>
                <div className="text-lg font-bold">{queryDSL.fields?.length || 0}</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Eye className="h-4 w-4 text-green-600" />
              <div>
                <div className="text-sm font-medium">过滤条件</div>
                <div className="text-lg font-bold">{queryDSL.filters?.length || 0}</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <BarChart3 className={`h-4 w-4 text-${queryComplexity.color}-600`} />
              <div>
                <div className="text-sm font-medium">复杂度</div>
                <div className="text-lg font-bold">{queryComplexity.level}</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-purple-600" />
              <div>
                <div className="text-sm font-medium">预估时间</div>
                <div className="text-lg font-bold">{estimatedTime}</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 验证结果 */}
      {validationIssues.length > 0 && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-yellow-800">
              <AlertTriangle className="h-5 w-5" />
              查询建议
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-1">
              {validationIssues.map((issue, index) => (
                <li key={index} className="text-sm text-yellow-700 flex items-center gap-2">
                  <div className="w-1 h-1 bg-yellow-600 rounded-full"></div>
                  {issue}
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}

      {/* 查询内容预览 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>查询内容</CardTitle>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(activeTab === "sql" ? generateSQL() : JSON.stringify(queryDSL, null, 2))}
              >
                <Copy className="h-4 w-4 mr-1" />
                复制
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => exportQuery(activeTab === "sql" ? "sql" : "json")}
              >
                <Download className="h-4 w-4 mr-1" />
                导出
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList>
              <TabsTrigger value="dsl">查询DSL</TabsTrigger>
              <TabsTrigger value="sql">SQL预览</TabsTrigger>
              <TabsTrigger value="summary">查询摘要</TabsTrigger>
            </TabsList>

            <TabsContent value="dsl" className="mt-4">
              <ScrollArea className="h-64">
                <pre className="text-sm bg-gray-100 p-4 rounded-lg overflow-auto">
                  {JSON.stringify(queryDSL, null, 2)}
                </pre>
              </ScrollArea>
            </TabsContent>

            <TabsContent value="sql" className="mt-4">
              <ScrollArea className="h-64">
                <pre className="text-sm bg-gray-100 p-4 rounded-lg overflow-auto font-mono">
                  {generateSQL()}
                </pre>
              </ScrollArea>
            </TabsContent>

            <TabsContent value="summary" className="mt-4">
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">研究信息</h4>
                  <div className="text-sm text-gray-600 space-y-1">
                    <div>研究名称: {queryDSL.study_name}</div>
                    <div>数据集: {queryDSL.dataset_id}</div>
                    <div>描述: {queryDSL.description}</div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-2">选择字段 ({queryDSL.fields?.length || 0})</h4>
                  <div className="flex flex-wrap gap-1">
                    {queryDSL.fields?.slice(0, 10).map((field, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {field.field?.name || field.name || field}
                      </Badge>
                    ))}
                    {(queryDSL.fields?.length || 0) > 10 && (
                      <Badge variant="outline" className="text-xs">
                        +{(queryDSL.fields?.length || 0) - 10} 更多
                      </Badge>
                    )}
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-2">过滤条件 ({queryDSL.filters?.length || 0})</h4>
                  <div className="space-y-1">
                    {queryDSL.filters?.slice(0, 5).map((filter, index) => (
                      <div key={index} className="text-sm text-gray-600">
                        {filter.field} {filter.operator} {filter.value || filter.values?.join(",")}
                      </div>
                    ))}
                    {(queryDSL.filters?.length || 0) > 5 && (
                      <div className="text-sm text-gray-500">
                        ... 还有 {(queryDSL.filters?.length || 0) - 5} 个条件
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-2">复杂度分析</h4>
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <Badge variant={queryComplexity.color === "green" ? "default" : queryComplexity.color === "yellow" ? "secondary" : "destructive"}>
                        {queryComplexity.level}
                      </Badge>
                      <span className="text-sm text-gray-600">评分: {queryComplexity.score}/10</span>
                    </div>
                    <div className="text-sm text-gray-600">
                      影响因素: {queryComplexity.factors.join(", ") || "无"}
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* 执行按钮 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {validationIssues.length === 0 ? (
            <CheckCircle2 className="h-5 w-5 text-green-600" />
          ) : (
            <AlertTriangle className="h-5 w-5 text-yellow-600" />
          )}
          <span className="text-sm text-gray-600">
            {validationIssues.length === 0 ? "查询验证通过" : `发现 ${validationIssues.length} 个建议`}
          </span>
        </div>

        <Button 
          onClick={onExecute}
          disabled={isExecuting || (queryDSL.fields?.length || 0) === 0}
          className="bg-blue-600 hover:bg-blue-700"
          size="lg"
        >
          {isExecuting ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              执行中...
            </>
          ) : (
            <>
              <Play className="h-4 w-4 mr-2" />
              执行查询
            </>
          )}
        </Button>
      </div>
    </div>
  )
}
