package repository

import (
	"context"
	"database/sql"

	"github.com/chungzy/medical-data-platform/internal/model"

	"github.com/google/uuid"
)

type exportRepository struct {
	db *sql.DB
}

func NewExportRepository(db *sql.DB) ExportRepository {
	return &exportRepository{db: db}
}

func (r *exportRepository) Create(ctx context.Context, record *model.ExportRecord) error {
	query := `
		INSERT INTO export_records (id, user_id, query_id, export_type, file_name, file_path, file_size, status, download_count, expires_at, created_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
	`

	_, err := r.db.ExecContext(ctx, query,
		record.ID, record.UserID, record.QueryID, record.ExportType,
		record.FileName, record.FilePath, record.FileSize, record.Status,
		record.DownloadCount, record.ExpiresAt, record.CreatedAt,
	)
	return err
}

func (r *exportRepository) GetByID(ctx context.Context, id uuid.UUID) (*model.ExportRecord, error) {
	query := `
		SELECT id, user_id, query_id, export_type, file_name, file_path, file_size, status, download_count, expires_at, created_at
		FROM export_records WHERE id = $1
	`

	record := &model.ExportRecord{}
	err := r.db.QueryRowContext(ctx, query, id).Scan(
		&record.ID, &record.UserID, &record.QueryID, &record.ExportType,
		&record.FileName, &record.FilePath, &record.FileSize, &record.Status,
		&record.DownloadCount, &record.ExpiresAt, &record.CreatedAt,
	)
	if err != nil {
		return nil, err
	}

	return record, nil
}

func (r *exportRepository) Update(ctx context.Context, record *model.ExportRecord) error {
	query := `
		UPDATE export_records 
		SET file_name = $2, file_path = $3, file_size = $4, status = $5, download_count = $6
		WHERE id = $1
	`

	_, err := r.db.ExecContext(ctx, query,
		record.ID, record.FileName, record.FilePath, record.FileSize,
		record.Status, record.DownloadCount,
	)
	return err
}

func (r *exportRepository) IncrementDownloadCount(ctx context.Context, id uuid.UUID) error {
	query := `UPDATE export_records SET download_count = download_count + 1 WHERE id = $1`
	_, err := r.db.ExecContext(ctx, query, id)
	return err
}

func (r *exportRepository) GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.ExportRecord, int, error) {
	query := `
		SELECT id, user_id, query_id, export_type, file_name, file_path, file_size, status, download_count, expires_at, created_at
		FROM export_records WHERE user_id = $1
		ORDER BY created_at DESC
		LIMIT $2 OFFSET $3
	`

	rows, err := r.db.QueryContext(ctx, query, userID, limit, offset)
	if err != nil {
		return nil, 0, err
	}
	defer rows.Close()

	var records []model.ExportRecord
	for rows.Next() {
		var record model.ExportRecord
		err := rows.Scan(
			&record.ID, &record.UserID, &record.QueryID, &record.ExportType,
			&record.FileName, &record.FilePath, &record.FileSize, &record.Status,
			&record.DownloadCount, &record.ExpiresAt, &record.CreatedAt,
		)
		if err != nil {
			return nil, 0, err
		}
		records = append(records, record)
	}

	// 获取总数
	countQuery := `SELECT COUNT(*) FROM export_records WHERE user_id = $1`
	var total int
	err = r.db.QueryRowContext(ctx, countQuery, userID).Scan(&total)
	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}

func (r *exportRepository) DeleteExpired(ctx context.Context) error {
	query := `DELETE FROM export_records WHERE expires_at < NOW()`
	_, err := r.db.ExecContext(ctx, query)
	return err
}
