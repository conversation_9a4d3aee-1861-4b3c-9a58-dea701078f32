# 数据库迁移文件

本目录包含医疗数据平台的数据库建表语句和示例数据。

## 文件说明

- `001_create_tables.sql` - 主要建表语句，包含所有核心表结构
- `002_sample_data.sql` - 示例数据插入语句，用于开发和测试

## 数据库表结构

### 核心表

1. **users** - 用户表
   - 存储用户基本信息、角色权限
   - 支持5种角色：student, doctor, researcher, teacher, analyst

2. **datasets** - 数据集表
   - 存储可用的医疗数据集信息
   - 包含MIMIC-III、MIMIC-IV、eICU等数据集

3. **dataset_tables** - 数据集表信息
   - 存储每个数据集包含的表信息

4. **data_fields** - 数据字段表
   - 存储数据字典信息，包含字段描述、类型、分类等

5. **query_history** - 查询历史表
   - 记录用户的查询历史和执行状态

6. **query_templates** - 查询模板表
   - 存储可重用的查询模板

### 扩展表

7. **export_records** - 数据导出记录表
8. **user_activity_logs** - 用户活动日志表
9. **system_configs** - 系统配置表
10. **dataset_permissions** - 数据集访问权限表

## 使用方法

### 1. 创建数据库

```bash
# 连接到PostgreSQL
psql -U postgres

# 创建数据库
CREATE DATABASE medical_data_platform;

# 切换到新数据库
\c medical_data_platform;
```

### 2. 执行建表语句

```bash
# 执行主要建表语句
psql -U postgres -d medical_data_platform -f migrations/001_create_tables.sql

# 插入示例数据（可选）
psql -U postgres -d medical_data_platform -f migrations/002_sample_data.sql
```

### 3. 验证表创建

```sql
-- 查看所有表
\dt

-- 查看表结构
\d users
\d datasets
\d data_fields
```

## 环境变量配置

确保在`.env`文件中配置正确的数据库连接信息：

```env
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=your_password
DB_NAME=medical_data_platform
DB_SSLMODE=disable
```

## 索引优化

建表语句已包含必要的索引，用于优化常见查询：

- 用户邮箱、角色索引
- 数据字段分类、名称索引
- 查询历史用户ID、时间索引
- 查询模板分类、使用次数索引

## 注意事项

1. **PostgreSQL版本要求**：建议使用PostgreSQL 12+
2. **UUID支持**：需要启用`gen_random_uuid()`函数
3. **JSONB支持**：用于存储复杂配置数据
4. **时区支持**：所有时间字段使用`TIMESTAMP WITH TIME ZONE`

## 示例数据说明

示例数据包含：
- 3个医疗数据集（MIMIC-III、MIMIC-IV、eICU）
- 常用数据表和字段定义
- 4个查询模板示例
- 系统配置参数

这些数据可以帮助您快速开始使用平台进行开发和测试。 