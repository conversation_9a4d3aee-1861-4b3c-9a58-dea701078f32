# 构建产物
bin/
medical-platform
*.exe

# 日志文件
logs/
*.log
*.log.*

# 临时文件
tmp/
temp/
*.tmp

# 导出文件
exports/
*.csv
*.xlsx
*.json

# 开发工具配置
.vscode/
.idea/
*.swp
*.swo
*~

# Git相关
.git/
.gitignore
.gitattributes

# 测试相关
coverage.out
*.test
test_*

# 文档（除了必要的配置文档）
doc/
docs/
*.md
!README.md

# 环境配置文件
.env
.env.*
env.example

# 依赖缓存
vendor/

# 操作系统文件
.DS_Store
Thumbs.db

# Docker相关
Dockerfile*
docker-compose*.yml
.dockerignore

# CI/CD配置
.github/
.gitlab-ci.yml
.travis.yml

# 任务文件
.tasks/

# 备份文件
*.bak
*.backup

# 编辑器临时文件
.#*
#*# 