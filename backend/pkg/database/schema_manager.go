package database

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/chungzy/medical-data-platform/pkg/logger"
)

// TableInfo 表信息结构
type TableInfo struct {
	SchemaName   string `json:"schema_name"`
	TableName    string `json:"table_name"`
	TableComment string `json:"table_comment"`
	RecordCount  int64  `json:"record_count"`
}

// ColumnInfo 列信息结构
type ColumnInfo struct {
	SchemaName      string `json:"schema_name"`
	TableName       string `json:"table_name"`
	ColumnName      string `json:"column_name"`
	DataType        string `json:"data_type"`
	IsNullable      bool   `json:"is_nullable"`
	ColumnDefault   string `json:"column_default"`
	ColumnComment   string `json:"column_comment"`
	OrdinalPosition int    `json:"ordinal_position"`
}

// SchemaInfo schema信息结构
type SchemaInfo struct {
	SchemaName  string      `json:"schema_name"`
	Tables      []TableInfo `json:"tables"`
	TableCount  int         `json:"table_count"`
	LastUpdated time.Time   `json:"last_updated"`
}

// SchemaManager schema管理器
type SchemaManager struct {
	dsm   *DataSourceManager
	cache map[string]*SchemaInfo
	mutex sync.RWMutex
}

// NewSchemaManager 创建schema管理器
func NewSchemaManager(dsm *DataSourceManager) *SchemaManager {
	return &SchemaManager{
		dsm:   dsm,
		cache: make(map[string]*SchemaInfo),
	}
}

// GetSchemaInfo 获取schema信息
func (sm *SchemaManager) GetSchemaInfo(ctx context.Context, dataSourceID string, schemaName string) (*SchemaInfo, error) {
	cacheKey := fmt.Sprintf("%s:%s", dataSourceID, schemaName)

	// 检查缓存
	sm.mutex.RLock()
	if cached, exists := sm.cache[cacheKey]; exists {
		// 缓存有效期1小时
		if time.Since(cached.LastUpdated) < time.Hour {
			sm.mutex.RUnlock()
			return cached, nil
		}
	}
	sm.mutex.RUnlock()

	// 从数据库获取
	schemaInfo, err := sm.fetchSchemaInfo(ctx, dataSourceID, schemaName)
	if err != nil {
		return nil, err
	}

	// 更新缓存
	sm.mutex.Lock()
	sm.cache[cacheKey] = schemaInfo
	sm.mutex.Unlock()

	return schemaInfo, nil
}

// fetchSchemaInfo 从数据库获取schema信息
func (sm *SchemaManager) fetchSchemaInfo(ctx context.Context, dataSourceID string, schemaName string) (*SchemaInfo, error) {
	db, err := sm.dsm.GetConnection(dataSourceID)
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %w", err)
	}

	// 查询表信息
	tables, err := sm.getTablesInSchema(ctx, db, schemaName)
	if err != nil {
		return nil, fmt.Errorf("failed to get tables in schema: %w", err)
	}

	schemaInfo := &SchemaInfo{
		SchemaName:  schemaName,
		Tables:      tables,
		TableCount:  len(tables),
		LastUpdated: time.Now(),
	}

	logger.Info("Fetched schema info", map[string]interface{}{
		"datasource_id": dataSourceID,
		"schema_name":   schemaName,
		"table_count":   len(tables),
	})

	return schemaInfo, nil
}

// getTablesInSchema 获取schema中的所有表
func (sm *SchemaManager) getTablesInSchema(ctx context.Context, db *sql.DB, schemaName string) ([]TableInfo, error) {
	query := `
		SELECT 
			t.table_schema,
			t.table_name,
			COALESCE(obj_description(c.oid), '') as table_comment,
			COALESCE(s.n_tup_ins, 0) as estimated_rows
		FROM information_schema.tables t
		LEFT JOIN pg_class c ON c.relname = t.table_name
		LEFT JOIN pg_stat_user_tables s ON s.relname = t.table_name AND s.schemaname = t.table_schema
		WHERE t.table_schema = $1
		AND t.table_type = 'BASE TABLE'
		ORDER BY t.table_name
	`

	rows, err := db.QueryContext(ctx, query, schemaName)
	if err != nil {
		return nil, fmt.Errorf("failed to query tables: %w", err)
	}
	defer rows.Close()

	var tables []TableInfo
	for rows.Next() {
		var table TableInfo
		var comment sql.NullString
		var recordCount sql.NullInt64

		err := rows.Scan(&table.SchemaName, &table.TableName, &comment, &recordCount)
		if err != nil {
			logger.Error("Failed to scan table row", err, map[string]interface{}{
				"schema_name": schemaName,
			})
			continue
		}

		if comment.Valid {
			table.TableComment = comment.String
		}
		if recordCount.Valid {
			table.RecordCount = recordCount.Int64
		}

		tables = append(tables, table)
	}

	return tables, rows.Err()
}

// GetTableColumns 获取表的列信息
func (sm *SchemaManager) GetTableColumns(ctx context.Context, dataSourceID string, schemaName string, tableName string) ([]ColumnInfo, error) {
	db, err := sm.dsm.GetConnection(dataSourceID)
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %w", err)
	}

	query := `
		SELECT 
			c.table_schema,
			c.table_name,
			c.column_name,
			c.data_type,
			c.is_nullable = 'YES' as is_nullable,
			COALESCE(c.column_default, '') as column_default,
			COALESCE(col_description(pgc.oid, c.ordinal_position), '') as column_comment,
			c.ordinal_position
		FROM information_schema.columns c
		LEFT JOIN pg_class pgc ON pgc.relname = c.table_name
		LEFT JOIN pg_namespace pgn ON pgn.oid = pgc.relnamespace AND pgn.nspname = c.table_schema
		WHERE c.table_schema = $1 AND c.table_name = $2
		ORDER BY c.ordinal_position
	`

	rows, err := db.QueryContext(ctx, query, schemaName, tableName)
	if err != nil {
		return nil, fmt.Errorf("failed to query columns: %w", err)
	}
	defer rows.Close()

	var columns []ColumnInfo
	for rows.Next() {
		var col ColumnInfo
		var columnDefault sql.NullString
		var columnComment sql.NullString

		err := rows.Scan(
			&col.SchemaName,
			&col.TableName,
			&col.ColumnName,
			&col.DataType,
			&col.IsNullable,
			&columnDefault,
			&columnComment,
			&col.OrdinalPosition,
		)
		if err != nil {
			logger.Error("Failed to scan column row", err, map[string]interface{}{
				"schema_name": schemaName,
				"table_name":  tableName,
			})
			continue
		}

		if columnDefault.Valid {
			col.ColumnDefault = columnDefault.String
		}
		if columnComment.Valid {
			col.ColumnComment = columnComment.String
		}

		columns = append(columns, col)
	}

	return columns, rows.Err()
}

// GetMIMICSchemas 获取MIMIC-IV相关的schemas
func (sm *SchemaManager) GetMIMICSchemas(ctx context.Context, dataSourceID string) ([]string, error) {
	db, err := sm.dsm.GetConnection(dataSourceID)
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %w", err)
	}

	query := `
		SELECT schema_name 
		FROM information_schema.schemata 
		WHERE schema_name LIKE 'mimiciv%' 
		ORDER BY schema_name
	`

	rows, err := db.QueryContext(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query schemas: %w", err)
	}
	defer rows.Close()

	var schemas []string
	for rows.Next() {
		var schema string
		if err := rows.Scan(&schema); err != nil {
			continue
		}
		schemas = append(schemas, schema)
	}

	return schemas, rows.Err()
}

// ValidateConnection 验证数据库连接和MIMIC数据
func (sm *SchemaManager) ValidateConnection(ctx context.Context, dataSourceID string) error {
	// 检查连接
	_, err := sm.dsm.GetConnection(dataSourceID)
	if err != nil {
		return fmt.Errorf("connection failed: %w", err)
	}

	// 检查MIMIC schemas是否存在
	schemas, err := sm.GetMIMICSchemas(ctx, dataSourceID)
	if err != nil {
		return fmt.Errorf("failed to check MIMIC schemas: %w", err)
	}

	if len(schemas) == 0 {
		return fmt.Errorf("no MIMIC schemas found in database")
	}

	logger.Info("Database validation successful", map[string]interface{}{
		"datasource_id": dataSourceID,
		"mimic_schemas": strings.Join(schemas, ", "),
	})

	return nil
}

// ClearCache 清除缓存
func (sm *SchemaManager) ClearCache() {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()
	sm.cache = make(map[string]*SchemaInfo)
}
