package router

import (
	"github.com/gin-gonic/gin"

	"github.com/chungzy/medical-data-platform/internal/api"
	"github.com/chungzy/medical-data-platform/internal/middleware"
	"github.com/chungzy/medical-data-platform/internal/service"
	"github.com/chungzy/medical-data-platform/pkg/jwt"
)

type Router struct {
	engine                 *gin.Engine
	authController         *api.AuthController
	queryController        *api.QueryController
	medicalQueryController *api.MedicalQueryController
	datasetController      *api.DatasetController
	dictionaryController   *api.DictionaryController
	templateController     *api.TemplateController
	exportController       *api.ExportController
	historyController      *api.HistoryController
	analyticsController    *api.AnalyticsController
	previewController      *api.PreviewController
	enhancedController     *api.EnhancedController
	systemController       *api.SystemController
	jwtManager             *jwt.JWTManager
	permissionService      *service.SimplePermissionService
}

func NewRouter(
	authController *api.AuthController,
	queryController *api.QueryController,
	medicalQueryController *api.MedicalQueryController,
	datasetController *api.DatasetController,
	dictionaryController *api.DictionaryController,
	templateController *api.TemplateController,
	exportController *api.ExportController,
	historyController *api.HistoryController,
	analyticsController *api.AnalyticsController,
	previewController *api.PreviewController,
	enhancedController *api.EnhancedController,
	systemController *api.SystemController,
	jwtManager *jwt.JWTManager,
	permissionService *service.SimplePermissionService,
) *Router {
	return &Router{
		engine:                 gin.New(),
		authController:         authController,
		queryController:        queryController,
		medicalQueryController: medicalQueryController,
		datasetController:      datasetController,
		dictionaryController:   dictionaryController,
		templateController:     templateController,
		exportController:       exportController,
		historyController:      historyController,
		analyticsController:    analyticsController,
		previewController:      previewController,
		enhancedController:     enhancedController,
		systemController:       systemController,
		jwtManager:             jwtManager,
		permissionService:      permissionService,
	}
}

func (r *Router) Setup() *gin.Engine {
	// 添加中间件
	r.engine.Use(gin.Logger())
	r.engine.Use(gin.Recovery())
	r.engine.Use(middleware.CORSMiddleware())

	// 健康检查
	r.engine.GET("/health", r.systemController.HealthCheck)

	// 数据源状态
	r.engine.GET("/api/datasources/status", r.systemController.GetDatasourceStatus)

	// API路由组 - 同时支持v1版本和无版本路径
	r.setupAPIRoutes()
	r.setupV1APIRoutes()

	return r.engine
}

func (r *Router) setupAPIRoutes() {
	api := r.engine.Group("/api")
	r.setupRoutes(api)
}

func (r *Router) setupV1APIRoutes() {
	apiV1 := r.engine.Group("/api/v1")
	r.setupRoutes(apiV1)
}

func (r *Router) setupRoutes(apiGroup *gin.RouterGroup) {
	// 认证路由（无需JWT验证）
	auth := apiGroup.Group("/auth")
	{
		auth.POST("/register", r.authController.Register)
		auth.POST("/login", r.authController.Login)
	}

	// 需要JWT验证的路由

	protected := apiGroup.Group("/")
	protected.Use(middleware.AuthMiddleware(r.jwtManager))
	{
		// 用户信息
		protected.GET("/auth/me", r.authController.GetMe)

		// 数据集管理
		datasets := protected.Group("/datasets")
		{
			datasets.GET("", r.datasetController.GetDatasets)
			datasets.GET("/:id", r.datasetController.GetDatasetByID)
		}

		// 数据字典
		dictionary := protected.Group("/dictionary")
		{
			dictionary.GET("/search", r.dictionaryController.SearchFields)
			dictionary.GET("/categories", r.dictionaryController.GetCategories)
			dictionary.GET("/fields/:id", r.dictionaryController.GetFieldByID)
		}

		// 查询相关
		queries := protected.Group("/queries")
		{
			queries.POST("/execute", r.queryController.ExecuteQuery)
			queries.GET("/:queryId/status", r.queryController.GetQueryStatus)
			queries.GET("/:queryId/results", r.queryController.GetQueryResults)
		}

		// 医学查询相关 - 需要用户权限
		medicalQueries := protected.Group("/medical-query")
		medicalQueries.Use(middleware.RequireUser(r.permissionService))
		{
			medicalQueries.GET("/categories", r.medicalQueryController.GetMedicalCategories)
			medicalQueries.POST("/validate-cohort", r.medicalQueryController.ValidateCohort)
			medicalQueries.POST("/estimate", r.medicalQueryController.EstimateResults)
			medicalQueries.POST("/execute", r.medicalQueryController.ExecuteMedicalQuery)
			medicalQueries.GET("/templates", r.medicalQueryController.GetQueryTemplates)
			medicalQueries.POST("/templates", r.medicalQueryController.SaveQueryTemplate)
			medicalQueries.GET("/history", r.medicalQueryController.GetMedicalQueryHistory)
			medicalQueries.POST("/:queryId/export", r.medicalQueryController.ExportQueryResults)
		}

		// 查询模板
		templates := protected.Group("/templates")
		{
			templates.GET("", r.templateController.GetTemplates)
			templates.POST("", r.templateController.CreateTemplate)
			templates.GET("/:id", r.templateController.GetTemplateByID)
			templates.PUT("/:id", r.templateController.UpdateTemplate)
			templates.DELETE("/:id", r.templateController.DeleteTemplate)
			templates.POST("/:id/use", r.templateController.UseTemplate)
		}

		// 数据导出
		export := protected.Group("/export")
		{
			export.POST("", r.exportController.ExportData)
			export.GET("/:id/status", r.exportController.GetExportStatus)
			export.GET("/:id/download", r.exportController.DownloadExport)
		}

		// 用户历史
		history := protected.Group("/history")
		{
			history.GET("/queries", r.historyController.GetQueryHistory)
			history.POST("/queries/:id/save-as-template", r.historyController.SaveAsTemplate)
			history.DELETE("/queries/:id", r.historyController.DeleteHistoryItem)
		}

		// 统计分析
		analytics := protected.Group("/analytics")
		{
			analytics.POST("/basic-stats", r.analyticsController.BasicStats)
			analytics.POST("/visualization", r.analyticsController.GenerateVisualization)
		}

		// 数据预览
		preview := protected.Group("/preview")
		{
			preview.POST("/table", r.previewController.PreviewTable)
			preview.POST("/query", r.previewController.PreviewQuery)
			preview.GET("/dataset/:dataset/overview", r.previewController.GetDatasetOverview)
			preview.POST("/sample", r.previewController.GetSampleData)
		}

		// 增强功能
		enhanced := protected.Group("/enhanced")
		{
			enhanced.POST("/export", r.enhancedController.CreateEnhancedExport)
			enhanced.GET("/export/:id/status", r.enhancedController.GetExportStatus)
			enhanced.POST("/query/validate", r.enhancedController.ValidateQuery)
			enhanced.POST("/query/plan", r.enhancedController.GetQueryPlan)
		}

		// 管理员功能 - 需要管理员权限
		// TODO: 添加用户管理API后启用
		// admin := protected.Group("/admin")
		// admin.Use(middleware.RequireAdmin(r.permissionService))
		// {
		//     // 用户管理路由
		// }
	}
}
