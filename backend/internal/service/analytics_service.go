package service

import (
	"context"
	"fmt"
	"math"

	"github.com/google/uuid"

	"github.com/chungzy/medical-data-platform/internal/model"
	"github.com/chungzy/medical-data-platform/internal/repository"
)

type AnalyticsService struct {
	queryRepo     repository.QueryRepository
	analyticsRepo repository.AnalyticsRepository
	queryService  *QueryService
}

func NewAnalyticsService(queryRepo repository.QueryRepository, analyticsRepo repository.AnalyticsRepository, queryService *QueryService) *AnalyticsService {
	return &AnalyticsService{
		queryRepo:     queryRepo,
		analyticsRepo: analyticsRepo,
		queryService:  queryService,
	}
}

// GetBasicStats 获取基础统计信息
func (s *AnalyticsService) GetBasicStats(ctx context.Context, userID uuid.UUID, req *model.BasicStatsRequest) (*model.BasicStatsResponse, error) {
	// 获取查询历史
	queryHistory, err := s.queryService.GetQueryHistoryByID(ctx, req.QueryID)
	if err != nil {
		return nil, fmt.Errorf("query not found: %w", err)
	}

	// 检查用户权限
	if queryHistory.UserID != userID {
		return nil, fmt.Errorf("permission denied")
	}

	// 这里简化处理，实际应该重新执行查询并计算统计信息
	// 模拟统计数据
	stats := make(map[string]model.FieldStats)

	for _, field := range req.Fields {
		// 模拟统计计算
		fieldStats := model.FieldStats{
			Count:   1000,
			Missing: 10,
		}

		// 根据字段类型模拟不同的统计信息
		switch field {
		case "age", "weight", "height", "temperature":
			mean := 65.5
			median := 64.0
			std := 12.3
			min := 18.0
			max := 95.0

			fieldStats.Mean = &mean
			fieldStats.Median = &median
			fieldStats.Std = &std
			fieldStats.Min = &min
			fieldStats.Max = &max
		}

		stats[field] = fieldStats
	}

	return &model.BasicStatsResponse{
		Stats: stats,
	}, nil
}

// GenerateVisualization 生成可视化数据
func (s *AnalyticsService) GenerateVisualization(ctx context.Context, userID uuid.UUID, req *model.VisualizationRequest) (*model.VisualizationResponse, error) {
	// 获取查询历史
	queryHistory, err := s.queryService.GetQueryHistoryByID(ctx, req.QueryID)
	if err != nil {
		return nil, fmt.Errorf("query not found: %w", err)
	}

	// 检查用户权限
	if queryHistory.UserID != userID {
		return nil, fmt.Errorf("permission denied")
	}

	// 这里简化处理，实际应该重新执行查询并生成可视化数据
	// 模拟可视化数据
	var chartData []map[string]interface{}

	switch req.ChartType {
	case "bar":
		chartData = s.generateBarChartData(req.XField, req.YField)
	case "line":
		chartData = s.generateLineChartData(req.XField, req.YField)
	case "pie":
		chartData = s.generatePieChartData(req.XField)
	case "scatter":
		chartData = s.generateScatterChartData(req.XField, req.YField)
	default:
		return nil, fmt.Errorf("unsupported chart type: %s", req.ChartType)
	}

	config := model.ChartConfig{
		Type:  req.ChartType,
		XAxis: req.XField,
	}

	if req.YField != nil {
		config.YAxis = *req.YField
	}

	return &model.VisualizationResponse{
		ChartData: chartData,
		Config:    config,
	}, nil
}

// generateBarChartData 生成柱状图数据
func (s *AnalyticsService) generateBarChartData(xField string, yField *string) []map[string]interface{} {
	data := []map[string]interface{}{
		{xField: "Category A", "value": 120},
		{xField: "Category B", "value": 80},
		{xField: "Category C", "value": 150},
		{xField: "Category D", "value": 90},
	}

	if yField != nil {
		for i := range data {
			data[i][*yField] = data[i]["value"]
			delete(data[i], "value")
		}
	}

	return data
}

// generateLineChartData 生成折线图数据
func (s *AnalyticsService) generateLineChartData(xField string, yField *string) []map[string]interface{} {
	data := []map[string]interface{}{}

	for i := 0; i < 10; i++ {
		point := map[string]interface{}{
			xField:  fmt.Sprintf("Point %d", i+1),
			"value": 50 + 30*math.Sin(float64(i)*0.5),
		}

		if yField != nil {
			point[*yField] = point["value"]
			delete(point, "value")
		}

		data = append(data, point)
	}

	return data
}

// generatePieChartData 生成饼图数据
func (s *AnalyticsService) generatePieChartData(xField string) []map[string]interface{} {
	return []map[string]interface{}{
		{xField: "Segment A", "value": 35, "percentage": 35.0},
		{xField: "Segment B", "value": 25, "percentage": 25.0},
		{xField: "Segment C", "value": 20, "percentage": 20.0},
		{xField: "Segment D", "value": 20, "percentage": 20.0},
	}
}

// generateScatterChartData 生成散点图数据
func (s *AnalyticsService) generateScatterChartData(xField string, yField *string) []map[string]interface{} {
	data := []map[string]interface{}{}

	for i := 0; i < 20; i++ {
		x := float64(i) + math.Sin(float64(i)*0.3)*5
		y := x*1.2 + math.Cos(float64(i)*0.4)*10 + 20

		point := map[string]interface{}{
			xField: x,
			"y":    y,
		}

		if yField != nil {
			point[*yField] = y
			delete(point, "y")
		}

		data = append(data, point)
	}

	return data
}
