package adapter

import (
	"context"
	"database/sql"

	"github.com/chungzy/medical-data-platform/internal/model"
)

// DatasetAdapter 数据集适配器接口
type DatasetAdapter interface {
	// 基本信息
	GetName() string
	GetDescription() string
	GetVersion() string
	GetSchemas() []string

	// 元数据操作
	GetTables(ctx context.Context) ([]model.DatasetTable, error)
	GetFields(ctx context.Context, table string) ([]model.DataField, error)
	GetFieldCategories(ctx context.Context) ([]model.FieldCategory, error)

	// 查询操作
	BuildQuery(dsl *model.QueryDSL) (string, []interface{}, error)
	ExecuteQuery(ctx context.Context, sql string, args []interface{}) ([]map[string]interface{}, error)
	ExecuteQueryWithCount(ctx context.Context, sql string, args []interface{}) ([]map[string]interface{}, int, error)
	ValidateQuery(dsl *model.QueryDSL) error

	// 字段映射
	MapField(field string) string
	MapTable(table string) string
	GetJoinClause(table string) string

	// 查询优化
	OptimizeQuery(sql string) string
	EstimateRowCount(dsl *model.QueryDSL) int
	GetQueryComplexity(dsl *model.QueryDSL) string

	// 数据预览
	PreviewData(ctx context.Context, table string, limit int) ([]map[string]interface{}, error)
	GetSampleData(ctx context.Context, table string, fields []string, limit int) ([]map[string]interface{}, error)

	// 健康检查
	HealthCheck(ctx context.Context) error
}

// AdapterRegistry 适配器注册表
type AdapterRegistry struct {
	adapters map[string]DatasetAdapter
}

// NewAdapterRegistry 创建适配器注册表
func NewAdapterRegistry() *AdapterRegistry {
	return &AdapterRegistry{
		adapters: make(map[string]DatasetAdapter),
	}
}

// Register 注册适配器
func (r *AdapterRegistry) Register(name string, adapter DatasetAdapter) {
	r.adapters[name] = adapter
}

// Get 获取适配器
func (r *AdapterRegistry) Get(name string) (DatasetAdapter, bool) {
	adapter, exists := r.adapters[name]
	return adapter, exists
}

// List 列出所有适配器
func (r *AdapterRegistry) List() []string {
	var names []string
	for name := range r.adapters {
		names = append(names, name)
	}
	return names
}

// AdapterManager 适配器管理器
type AdapterManager struct {
	registry *AdapterRegistry
	db       *sql.DB
}

// NewAdapterManager 创建适配器管理器
func NewAdapterManager(db *sql.DB) *AdapterManager {
	return &AdapterManager{
		registry: NewAdapterRegistry(),
		db:       db,
	}
}

// GetAdapter 获取适配器
func (m *AdapterManager) GetAdapter(dataset string) (DatasetAdapter, error) {
	adapter, exists := m.registry.Get(dataset)
	if !exists {
		return nil, ErrAdapterNotFound{Dataset: dataset}
	}
	return adapter, nil
}

// RegisterAdapter 注册适配器
func (m *AdapterManager) RegisterAdapter(name string, adapter DatasetAdapter) {
	m.registry.Register(name, adapter)
}

// ListAdapters 列出所有适配器
func (m *AdapterManager) ListAdapters() []string {
	return m.registry.List()
}

// AdapterConfig 适配器配置
type AdapterConfig struct {
	Name        string            `json:"name"`
	Type        string            `json:"type"`
	Description string            `json:"description"`
	Schemas     []string          `json:"schemas"`
	TableMaps   map[string]string `json:"table_maps"`
	FieldMaps   map[string]string `json:"field_maps"`
	JoinRules   map[string]string `json:"join_rules"`
}

// QueryContext 查询上下文
type QueryContext struct {
	Dataset       string
	UserID        string
	QueryID       string
	Timeout       int
	MaxRows       int
	EnableCache   bool
	CacheTimeout  int
}

// QueryResult 查询结果
type QueryResult struct {
	Data          []map[string]interface{} `json:"data"`
	Total         int                      `json:"total"`
	ExecutionTime int                      `json:"execution_time"`
	CacheHit      bool                     `json:"cache_hit"`
	Warnings      []string                 `json:"warnings,omitempty"`
}

// MetadataInfo 元数据信息
type MetadataInfo struct {
	Tables     []model.DatasetTable    `json:"tables"`
	Fields     []model.DataField       `json:"fields"`
	Categories []model.FieldCategory   `json:"categories"`
	Statistics map[string]interface{}  `json:"statistics"`
}

// ValidationResult 验证结果
type ValidationResult struct {
	Valid    bool     `json:"valid"`
	Errors   []string `json:"errors,omitempty"`
	Warnings []string `json:"warnings,omitempty"`
}

// 错误定义
type ErrAdapterNotFound struct {
	Dataset string
}

func (e ErrAdapterNotFound) Error() string {
	return "adapter not found for dataset: " + e.Dataset
}

type ErrInvalidQuery struct {
	Message string
	Details []string
}

func (e ErrInvalidQuery) Error() string {
	return "invalid query: " + e.Message
}

type ErrQueryTimeout struct {
	Timeout int
}

func (e ErrQueryTimeout) Error() string {
	return "query timeout exceeded"
}

type ErrConnectionFailed struct {
	Dataset string
	Reason  string
}

func (e ErrConnectionFailed) Error() string {
	return "connection failed for dataset " + e.Dataset + ": " + e.Reason
}
