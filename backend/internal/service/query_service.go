package service

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/google/uuid"

	"github.com/chungzy/medical-data-platform/internal/model"
	"github.com/chungzy/medical-data-platform/internal/repository"
)

type QueryService struct {
	queryRepo repository.QueryRepository
	db        *sql.DB
}

func NewQueryService(queryRepo repository.QueryRepository, db *sql.DB) *QueryService {
	return &QueryService{
		queryRepo: queryRepo,
		db:        db,
	}
}

// ExecuteQuery 执行查询
func (qs *QueryService) ExecuteQuery(ctx context.Context, query string, args []interface{}) ([]map[string]interface{}, error) {
	rows, err := qs.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	// 获取列信息
	columns, err := rows.Columns()
	if err != nil {
		return nil, err
	}

	// 读取数据
	var results []map[string]interface{}
	for rows.Next() {
		values := make([]interface{}, len(columns))
		valuePtrs := make([]interface{}, len(columns))
		for i := range values {
			valuePtrs[i] = &values[i]
		}

		if err := rows.Scan(valuePtrs...); err != nil {
			return nil, err
		}

		row := make(map[string]interface{})
		for i, col := range columns {
			val := values[i]
			if b, ok := val.([]byte); ok {
				row[col] = string(b)
			} else {
				row[col] = val
			}
		}
		results = append(results, row)
	}

	return results, rows.Err()
}

// ExecuteQueryWithCount 执行查询并返回数据和总数
func (qs *QueryService) ExecuteQueryWithCount(ctx context.Context, query string, args []interface{}) ([]map[string]interface{}, int, error) {
	// 执行查询获取数据
	rows, err := qs.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, 0, err
	}
	defer rows.Close()

	// 获取列信息
	columns, err := rows.Columns()
	if err != nil {
		return nil, 0, err
	}

	// 读取数据
	var results []map[string]interface{}
	for rows.Next() {
		values := make([]interface{}, len(columns))
		valuePtrs := make([]interface{}, len(columns))
		for i := range values {
			valuePtrs[i] = &values[i]
		}

		if err := rows.Scan(valuePtrs...); err != nil {
			return nil, 0, err
		}

		row := make(map[string]interface{})
		for i, col := range columns {
			val := values[i]
			if b, ok := val.([]byte); ok {
				row[col] = string(b)
			} else {
				row[col] = val
			}
		}
		results = append(results, row)
	}

	if err := rows.Err(); err != nil {
		return nil, 0, err
	}

	// 返回数据和总数（这里简化处理，实际应该执行COUNT查询）
	total := len(results)
	return results, total, nil
}

// SaveQueryHistory 保存查询历史
func (qs *QueryService) SaveQueryHistory(ctx context.Context, history *model.QueryHistory) error {
	return qs.queryRepo.SaveQueryHistory(ctx, history)
}

// GetQueryHistoryByID 根据ID获取查询历史
func (qs *QueryService) GetQueryHistoryByID(ctx context.Context, queryID uuid.UUID) (*model.QueryHistory, error) {
	return qs.queryRepo.GetQueryHistoryByID(ctx, queryID)
}

// GetUserQueryHistory 获取用户查询历史
func (qs *QueryService) GetUserQueryHistory(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.QueryHistory, int, error) {
	return qs.queryRepo.GetQueryHistory(ctx, userID, limit, offset)
}

// ExecuteEnhancedQuery 执行增强查询
func (qs *QueryService) ExecuteEnhancedQuery(userID string, req interface{}) (interface{}, error) {
	// 这里需要根据实际的 EnhancedQueryRequest 结构来实现
	// 暂时返回一个示例响应
	response := map[string]interface{}{
		"queryId":       fmt.Sprintf("query_%d", time.Now().UnixNano()),
		"data":          []map[string]interface{}{},
		"total":         0,
		"cohortSize":    0,
		"executionTime": 0,
		"status":        "completed",
		"metadata":      map[string]interface{}{},
	}
	return response, nil
}

// GetTableRelationships 获取表关系
func (qs *QueryService) GetTableRelationships(dataset string) (interface{}, error) {
	// 这里需要根据实际需求实现表关系查询逻辑
	// 暂时返回一个示例响应
	relationships := []map[string]interface{}{
		{
			"fromTable": "patients",
			"toTable":   "admissions",
			"joinKey":   "subject_id",
			"joinType":  "one_to_many",
		},
		{
			"fromTable": "admissions",
			"toTable":   "chartevents",
			"joinKey":   "hadm_id",
			"joinType":  "one_to_many",
		},
	}
	return relationships, nil
}

// GetFieldRecommendations 获取字段推荐
func (qs *QueryService) GetFieldRecommendations(dataset, cohort string) (interface{}, error) {
	// 这里需要根据实际需求实现字段推荐逻辑
	// 暂时返回一个示例响应
	recommendations := []map[string]interface{}{
		{
			"field":       "age",
			"table":       "patients",
			"type":        "numeric",
			"description": "Patient age",
			"relevance":   0.9,
		},
		{
			"field":       "gender",
			"table":       "patients",
			"type":        "categorical",
			"description": "Patient gender",
			"relevance":   0.8,
		},
	}
	return recommendations, nil
}
