"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { FileText, Search, Play, Copy, Star, Clock, Users, Heart, Activity, Stethoscope } from "lucide-react"
import { DashboardLayout } from "@/components/dashboard-layout"

interface QueryTemplate {
  id: string
  name: string
  description: string
  category: string
  dataset: string
  fields: string[]
  conditions: string[]
  author: string
  usage: number
  rating: number
  lastUsed?: string
  isPublic: boolean
}

export default function TemplatesPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [selectedDataset, setSelectedDataset] = useState("all")

  const categories = [
    { value: "all", label: "全部", icon: FileText },
    { value: "icu", label: "重症监护", icon: Activity },
    { value: "cardiology", label: "心血管", icon: Heart },
    { value: "general", label: "通用分析", icon: Stethoscope },
    { value: "research", label: "科研模板", icon: Search },
  ]

  const datasets = [
    { value: "all", label: "全部数据集" },
    { value: "mimic-iv", label: "MIMIC-IV" },
    { value: "eicu", label: "EICU" },
    { value: "nhanes", label: "NHANES" },
    { value: "pic", label: "PIC" },
  ]

  const templates: QueryTemplate[] = [
    {
      id: "1",
      name: "ICU患者首次入院指标提取",
      description: "基于真实MIMIC-IV数据提取ICU患者首次入院时的基本信息和生命体征",
      category: "icu",
      dataset: "mimic-iv",
      fields: ["subject_id", "gender", "anchor_age", "admittime", "dischtime", "admission_type"],
      conditions: ["首次入院", "ICU停留>24小时", "使用真实MIMIC-IV表结构"],
      author: "系统模板",
      usage: 1250,
      rating: 4.8,
      lastUsed: "2024-01-26",
      isPublic: true,
    },
    {
      id: "2",
      name: "机械通气患者死亡率分析",
      description: "分析接受机械通气治疗患者的死亡率及相关风险因素",
      category: "icu",
      dataset: "eicu",
      fields: ["患者ID", "年龄", "APACHE评分", "通气时长", "死亡状态"],
      conditions: ["机械通气>48小时", "年龄>18岁"],
      author: "张医生",
      usage: 890,
      rating: 4.6,
      lastUsed: "2024-01-14",
      isPublic: true,
    },
    {
      id: "3",
      name: "心血管疾病风险因素评估",
      description: "评估心血管疾病患者的主要风险因素和预后指标",
      category: "cardiology",
      dataset: "nhanes",
      fields: ["年龄", "性别", "BMI", "血压", "胆固醇", "血糖", "吸烟史"],
      conditions: ["心血管疾病诊断", "年龄20-80岁"],
      author: "李教授",
      usage: 650,
      rating: 4.7,
      isPublic: true,
    },
    {
      id: "4",
      name: "感染患者抗生素使用模式",
      description: "分析感染患者的抗生素使用模式和治疗效果",
      category: "general",
      dataset: "mimic-iv",
      fields: ["患者ID", "感染类型", "抗生素种类", "用药时长", "治疗结果"],
      conditions: ["感染诊断", "抗生素治疗>3天"],
      author: "王医生",
      usage: 420,
      rating: 4.4,
      isPublic: true,
    },
    {
      id: "5",
      name: "儿科重症患者营养评估",
      description: "评估儿科重症患者的营养状况和生长发育指标",
      category: "icu",
      dataset: "pic",
      fields: ["年龄", "体重", "身高", "营养评分", "喂养方式", "住院时长"],
      conditions: ["年龄<18岁", "ICU住院>7天"],
      author: "陈医生",
      usage: 280,
      rating: 4.5,
      lastUsed: "2024-01-12",
      isPublic: true,
    },
    {
      id: "6",
      name: "急性肾损伤预测模型",
      description: "构建急性肾损伤的预测模型，包含相关生化指标",
      category: "research",
      dataset: "mimic-iv",
      fields: ["肌酐", "尿素氮", "尿量", "血压", "年龄", "合并症"],
      conditions: ["入院48小时内", "基线肌酐正常"],
      author: "研究团队",
      usage: 180,
      rating: 4.9,
      isPublic: false,
    },
  ]

  const filteredTemplates = templates.filter((template) => {
    const matchesSearch =
      searchTerm === "" ||
      template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.description.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesCategory = selectedCategory === "all" || template.category === selectedCategory
    const matchesDataset = selectedDataset === "all" || template.dataset === selectedDataset

    return matchesSearch && matchesCategory && matchesDataset
  })

  const useTemplate = (templateId: string) => {
    console.log("使用模板:", templateId)
    // TODO: 跳转到查询构建器并加载模板
  }

  const copyTemplate = (templateId: string) => {
    console.log("复制模板:", templateId)
    // TODO: 实现模板复制功能
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">查询模板</h1>
            <p className="text-gray-600 mt-1">使用预设模板快速构建常用查询</p>
          </div>
          <Button>
            <FileText className="h-4 w-4 mr-2" />
            创建模板
          </Button>
        </div>

        {/* Search and Filter */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Search className="h-5 w-5" />
              <span>搜索和筛选</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">搜索模板</label>
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="搜索模板名称或描述..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">分类</label>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">数据集</label>
                <Select value={selectedDataset} onValueChange={setSelectedDataset}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {datasets.map((dataset) => (
                      <SelectItem key={dataset.value} value={dataset.value}>
                        {dataset.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Template Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTemplates.map((template) => {
            const CategoryIcon = categories.find((c) => c.value === template.category)?.icon || FileText
            return (
              <Card key={template.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-2">
                      <CategoryIcon className="h-5 w-5 text-blue-600" />
                      <Badge variant="secondary" className="text-xs">
                        {datasets.find((d) => d.value === template.dataset)?.label}
                      </Badge>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Star className="h-4 w-4 text-yellow-500 fill-current" />
                      <span className="text-sm font-medium">{template.rating}</span>
                    </div>
                  </div>
                  <CardTitle className="text-lg">{template.name}</CardTitle>
                  <CardDescription className="text-sm">{template.description}</CardDescription>
                </CardHeader>

                <CardContent className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium mb-2">包含字段:</h4>
                    <div className="flex flex-wrap gap-1">
                      {template.fields.slice(0, 4).map((field, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {field}
                        </Badge>
                      ))}
                      {template.fields.length > 4 && (
                        <Badge variant="outline" className="text-xs">
                          +{template.fields.length - 4}
                        </Badge>
                      )}
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium mb-2">查询条件:</h4>
                    <div className="space-y-1">
                      {template.conditions.slice(0, 2).map((condition, index) => (
                        <div key={index} className="text-xs text-gray-600 bg-gray-50 px-2 py-1 rounded">
                          {condition}
                        </div>
                      ))}
                      {template.conditions.length > 2 && (
                        <div className="text-xs text-gray-500">+{template.conditions.length - 2} 个条件</div>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <div className="flex items-center space-x-2">
                      <Users className="h-3 w-3" />
                      <span>{template.usage} 次使用</span>
                    </div>
                    {template.lastUsed && (
                      <div className="flex items-center space-x-1">
                        <Clock className="h-3 w-3" />
                        <span>{template.lastUsed}</span>
                      </div>
                    )}
                  </div>

                  <div className="flex space-x-2">
                    <Button size="sm" className="flex-1" onClick={() => useTemplate(template.id)}>
                      <Play className="h-3 w-3 mr-1" />
                      使用
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => copyTemplate(template.id)}>
                      <Copy className="h-3 w-3" />
                    </Button>
                  </div>

                  <div className="text-xs text-gray-500">
                    作者: {template.author}
                    {!template.isPublic && (
                      <Badge variant="secondary" className="ml-2 text-xs">
                        私有
                      </Badge>
                    )}
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Popular Categories */}
        <Card>
          <CardHeader>
            <CardTitle>热门分类</CardTitle>
            <CardDescription>按分类浏览模板</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {categories.slice(1).map((category) => {
                const Icon = category.icon
                const count = templates.filter((t) => t.category === category.value).length
                return (
                  <Button
                    key={category.value}
                    variant="outline"
                    className="h-20 flex flex-col items-center space-y-2"
                    onClick={() => setSelectedCategory(category.value)}
                  >
                    <Icon className="h-6 w-6" />
                    <div className="text-center">
                      <div className="text-sm font-medium">{category.label}</div>
                      <div className="text-xs text-gray-500">{count} 个模板</div>
                    </div>
                  </Button>
                )
              })}
            </div>
          </CardContent>
        </Card>

        {filteredTemplates.length === 0 && (
          <Card>
            <CardContent className="text-center py-12">
              <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">未找到匹配的模板</h3>
              <p className="text-gray-500 mb-4">请尝试调整搜索条件或筛选器</p>
              <Button onClick={() => console.log("创建新模板")}>创建新模板</Button>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  )
}
