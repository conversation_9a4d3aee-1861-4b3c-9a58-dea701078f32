package middleware

import (
	"net/http"

	"github.com/chungzy/medical-data-platform/internal/service"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// RequirePermission 权限检查中间件
func RequirePermission(permissionService *service.PermissionService, permission string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "user not authenticated"})
			c.Abort()
			return
		}

		uid, ok := userID.(uuid.UUID)
		if !ok {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "invalid user ID"})
			c.Abort()
			return
		}

		hasPermission, err := permissionService.CheckPermission(c.Request.Context(), uid, permission)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "permission check failed"})
			c.Abort()
			return
		}

		if !hasPermission {
			c.JSON(http.StatusForbidden, gin.H{"error": "insufficient permissions"})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireRole 角色检查中间件
func RequireRole(permissionService *service.PermissionService, roleCode string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "user not authenticated"})
			c.Abort()
			return
		}

		uid, ok := userID.(uuid.UUID)
		if !ok {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "invalid user ID"})
			c.Abort()
			return
		}

		hasRole, err := permissionService.HasRole(c.Request.Context(), uid, roleCode)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "role check failed"})
			c.Abort()
			return
		}

		if !hasRole {
			c.JSON(http.StatusForbidden, gin.H{"error": "insufficient role"})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireDatasetAccess 数据集访问权限中间件
func RequireDatasetAccess(permissionService *service.PermissionService, accessType string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "user not authenticated"})
			c.Abort()
			return
		}

		uid, ok := userID.(uuid.UUID)
		if !ok {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "invalid user ID"})
			c.Abort()
			return
		}

		// 从路径参数或请求体中获取数据集ID
		datasetID := c.Param("dataset_id")
		if datasetID == "" {
			// 尝试从请求体中获取
			var requestBody map[string]interface{}
			if err := c.ShouldBindJSON(&requestBody); err == nil {
				if dsID, exists := requestBody["dataset_id"]; exists {
					if dsIDStr, ok := dsID.(string); ok {
						datasetID = dsIDStr
					}
				}
			}
		}

		if datasetID == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "dataset ID required"})
			c.Abort()
			return
		}

		hasAccess, err := permissionService.CheckDatasetAccess(c.Request.Context(), uid, datasetID, accessType)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "dataset access check failed"})
			c.Abort()
			return
		}

		if !hasAccess {
			c.JSON(http.StatusForbidden, gin.H{"error": "insufficient dataset access"})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireAdmin 管理员权限中间件
/*
func RequireAdmin(permissionService *service.PermissionService) gin.HandlerFunc {
	return RequireRole(permissionService, "admin")
}*/

// RequireAnyRole 任意角色检查中间件
func RequireAnyRole(permissionService *service.PermissionService, roleCodes ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "user not authenticated"})
			c.Abort()
			return
		}

		uid, ok := userID.(uuid.UUID)
		if !ok {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "invalid user ID"})
			c.Abort()
			return
		}

		hasAnyRole := false
		for _, roleCode := range roleCodes {
			hasRole, err := permissionService.HasRole(c.Request.Context(), uid, roleCode)
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "role check failed"})
				c.Abort()
				return
			}
			if hasRole {
				hasAnyRole = true
				break
			}
		}

		if !hasAnyRole {
			c.JSON(http.StatusForbidden, gin.H{"error": "insufficient role"})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireResourceAccess 资源访问权限中间件
func RequireResourceAccess(permissionService *service.PermissionService, resource, action string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "user not authenticated"})
			c.Abort()
			return
		}

		uid, ok := userID.(uuid.UUID)
		if !ok {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "invalid user ID"})
			c.Abort()
			return
		}

		canAccess, err := permissionService.CanAccessResource(c.Request.Context(), uid, resource, action)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "resource access check failed"})
			c.Abort()
			return
		}

		if !canAccess {
			c.JSON(http.StatusForbidden, gin.H{"error": "insufficient resource access"})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RateLimitByUser 基于用户的限流中间件
func RateLimitByUser(permissionService *service.PermissionService) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := c.Get("user_id")
		if !exists {
			c.Next()
			return
		}

		uid, ok := userID.(uuid.UUID)
		if !ok {
			c.Next()
			return
		}

		// 检查用户角色，不同角色有不同的限流策略
		isAdmin, err := permissionService.IsAdmin(c.Request.Context(), uid)
		if err != nil {
			c.Next()
			return
		}

		if isAdmin {
			// 管理员不限流
			c.Next()
			return
		}

		// 这里可以实现具体的限流逻辑
		// 例如：普通用户每分钟最多100个请求
		// 高级用户每分钟最多500个请求

		c.Next()
	}
}
