"use client"

import { useAuth } from "@/lib/hooks/use-auth"
import { usePermissions } from "@/lib/hooks/use-permissions"
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { CheckCircle, XCircle, Settings, User, Database } from "lucide-react"
import { toast } from "sonner"

export default function DevTestPage() {
  const { user, logout } = useAuth()
  const { permissions, loading } = usePermissions()

  // 检查开发模式
  const isDevMode = process.env.NEXT_PUBLIC_DEV_MODE === 'true'
  const bypassAuth = process.env.NEXT_PUBLIC_BYPASS_AUTH === 'true'

  const testApiCall = async () => {
    try {
      // 这里可以测试API调用
      toast.success("API调用测试成功")
    } catch (error) {
      toast.error("API调用失败")
    }
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">开发模式测试</h1>
          <p className="text-gray-600">验证开发模式功能和权限设置</p>
        </div>

        {/* 开发模式状态 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Settings className="h-5 w-5" />
              <span>开发模式状态</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">开发模式</span>
                <div className="flex items-center space-x-2">
                  {isDevMode ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-500" />
                  )}
                  <Badge variant={isDevMode ? "default" : "secondary"}>
                    {isDevMode ? "启用" : "禁用"}
                  </Badge>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">权限绕过</span>
                <div className="flex items-center space-x-2">
                  {bypassAuth ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-500" />
                  )}
                  <Badge variant={bypassAuth ? "default" : "secondary"}>
                    {bypassAuth ? "启用" : "禁用"}
                  </Badge>
                </div>
              </div>
            </div>

            {isDevMode && bypassAuth && (
              <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                <p className="text-sm text-green-800 font-medium">✅ 开发模式正常工作</p>
                <p className="text-xs text-green-600">所有权限检查已绕过，可以自由开发测试</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* 用户信息 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <User className="h-5 w-5" />
              <span>当前用户信息</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {user ? (
              <div className="space-y-3">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-gray-500">用户名</p>
                    <p className="text-gray-900">{user.name}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">邮箱</p>
                    <p className="text-gray-900">{user.email}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">角色</p>
                    <Badge>{user.role}</Badge>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">用户ID</p>
                    <p className="text-gray-900 font-mono text-sm">{user.id}</p>
                  </div>
                </div>
              </div>
            ) : (
              <p className="text-gray-500">未登录</p>
            )}
          </CardContent>
        </Card>

        {/* 权限信息 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Database className="h-5 w-5" />
              <span>权限信息</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <p className="text-gray-500">加载中...</p>
            ) : permissions ? (
              <div className="space-y-4">
                <div>
                  <p className="text-sm font-medium text-gray-500 mb-2">权限级别</p>
                  <Badge variant="outline">
                    级别 {permissions.permission} - {
                      permissions.permission === 1 ? "管理员" : 
                      permissions.permission === 2 ? "普通用户" : "无权限"
                    }
                  </Badge>
                </div>

                <div>
                  <p className="text-sm font-medium text-gray-500 mb-2">可用权限</p>
                  <div className="flex flex-wrap gap-2">
                    {permissions.permissions.slice(0, 8).map((permission, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {permission}
                      </Badge>
                    ))}
                    {permissions.permissions.length > 8 && (
                      <Badge variant="outline" className="text-xs">
                        +{permissions.permissions.length - 8} 更多
                      </Badge>
                    )}
                  </div>
                </div>

                <div>
                  <p className="text-sm font-medium text-gray-500 mb-2">可访问数据集</p>
                  <div className="flex flex-wrap gap-2">
                    {permissions.datasets.map((dataset, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {dataset}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            ) : (
              <p className="text-gray-500">无权限信息</p>
            )}
          </CardContent>
        </Card>

        {/* 测试按钮 */}
        <Card>
          <CardHeader>
            <CardTitle>功能测试</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-4">
              <Button onClick={testApiCall}>
                测试API调用
              </Button>
              <Button variant="outline" onClick={() => window.location.href = "/query-builder"}>
                访问查询构建器
              </Button>
              <Button variant="outline" onClick={() => window.location.href = "/admin/users"}>
                访问用户管理
              </Button>
              <Button variant="destructive" onClick={logout}>
                退出登录
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 环境变量显示 */}
        <Card>
          <CardHeader>
            <CardTitle>环境变量</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 font-mono text-sm">
              <div>
                <span className="text-gray-500">NEXT_PUBLIC_DEV_MODE:</span>{" "}
                <span className="font-semibold">{process.env.NEXT_PUBLIC_DEV_MODE || "undefined"}</span>
              </div>
              <div>
                <span className="text-gray-500">NEXT_PUBLIC_BYPASS_AUTH:</span>{" "}
                <span className="font-semibold">{process.env.NEXT_PUBLIC_BYPASS_AUTH || "undefined"}</span>
              </div>
              <div>
                <span className="text-gray-500">NEXT_PUBLIC_API_URL:</span>{" "}
                <span className="font-semibold">{process.env.NEXT_PUBLIC_API_URL || "undefined"}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
