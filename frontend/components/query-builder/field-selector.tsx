"use client"

import { useState, useEffect, use<PERSON>em<PERSON> } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { 
  Search, 
  Filter, 
  Users, 
  Clock, 
  Activity, 
  Stethoscope, 
  Pill, 
  Scissors, 
  Clipboard,
  ChevronDown,
  ChevronRight,
  Star,
  Info
} from "lucide-react"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { apiClient } from "@/lib/api"
import { toast } from "sonner"

// 字段接口定义
interface MedicalField {
  id: string
  name: string
  nameEn: string
  type: string
  description: string
  table: string
  category: string
  unit?: string
  examples?: string[]
  isRequired?: boolean
  popularity?: number
}

interface MedicalFieldCategory {
  id: string
  name: string
  description: string
  icon: string
  fields: MedicalField[]
}

interface Dataset {
  id: string
  name: string
  description: string
  version: string
  tableCount: number
  features: string[]
  status: "healthy" | "warning" | "error"
}

interface SelectedField {
  field: MedicalField
  alias?: string
  aggregation?: string
}

interface FieldSelectorProps {
  dataset: Dataset | null
  selectedFields: SelectedField[]
  onFieldsChange: (fields: SelectedField[]) => void
  maxFields?: number
}

export function FieldSelector({
  dataset,
  selectedFields,
  onFieldsChange,
  maxFields = 50
}: FieldSelectorProps) {
  const [categories, setCategories] = useState<MedicalFieldCategory[]>([])
  const [loading, setLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set())
  const [showOnlySelected, setShowOnlySelected] = useState(false)
  const [sortBy, setSortBy] = useState<"name" | "popularity" | "required">("name")

  // 加载字段分类
  useEffect(() => {
    if (!dataset) return

    const loadCategories = async () => {
      setLoading(true)
      try {
        // 使用默认分类
        const defaultCategories = getDefaultCategories()
        setCategories(defaultCategories)
        setExpandedCategories(new Set(defaultCategories.map(cat => cat.id)))
      } catch (error) {
        console.error("Failed to load categories:", error)
      } finally {
        setLoading(false)
      }
    }

    loadCategories()
  }, [dataset])

  // 获取默认分类
  const getDefaultCategories = (): MedicalFieldCategory[] => [
    {
      id: "demographics",
      name: "人口统计学",
      description: "患者基本人口学信息和标识符",
      icon: "users",
      fields: [
        {
          id: "subject_id",
          name: "患者ID",
          nameEn: "subject_id",
          type: "integer",
          description: "患者唯一标识符",
          table: "patients",
          category: "demographics",
          isRequired: true
        },
        {
          id: "gender",
          name: "性别",
          nameEn: "gender",
          type: "string",
          description: "患者性别",
          table: "patients",
          category: "demographics",
          examples: ["M", "F"]
        },
        {
          id: "anchor_age",
          name: "年龄",
          nameEn: "anchor_age",
          type: "integer",
          description: "患者年龄",
          table: "patients",
          category: "demographics",
          unit: "岁"
        }
      ]
    },
    {
      id: "temporal",
      name: "时间信息",
      description: "入院、出院、检查等时间相关信息",
      icon: "clock",
      fields: [
        {
          id: "admittime",
          name: "入院时间",
          nameEn: "admittime",
          type: "timestamp",
          description: "患者入院时间",
          table: "admissions",
          category: "temporal"
        },
        {
          id: "dischtime",
          name: "出院时间",
          nameEn: "dischtime",
          type: "timestamp",
          description: "患者出院时间",
          table: "admissions",
          category: "temporal"
        }
      ]
    },
    {
      id: "clinical_measurements",
      name: "临床测量",
      description: "生命体征、实验室检查等数值测量",
      icon: "activity",
      fields: [
        {
          id: "heart_rate",
          name: "心率",
          nameEn: "heart_rate",
          type: "numeric",
          description: "患者心率",
          table: "chartevents",
          category: "clinical_measurements",
          unit: "bpm"
        },
        {
          id: "blood_pressure",
          name: "血压",
          nameEn: "blood_pressure",
          type: "numeric",
          description: "患者血压",
          table: "chartevents",
          category: "clinical_measurements",
          unit: "mmHg"
        }
      ]
    }
  ]

  // 获取分类图标
  const getCategoryIcon = (iconName: string) => {
    const iconMap: Record<string, React.ReactNode> = {
      users: <Users className="h-4 w-4" />,
      clock: <Clock className="h-4 w-4" />,
      activity: <Activity className="h-4 w-4" />,
      stethoscope: <Stethoscope className="h-4 w-4" />,
      pill: <Pill className="h-4 w-4" />,
      scissors: <Scissors className="h-4 w-4" />,
      clipboard: <Clipboard className="h-4 w-4" />,
    }
    return iconMap[iconName] || <Activity className="h-4 w-4" />
  }

  // 过滤和排序字段
  const filteredCategories = useMemo(() => {
    return categories.map(category => {
      let fields = category.fields

      // 搜索过滤
      if (searchTerm) {
        fields = fields.filter(field => 
          field.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          field.nameEn.toLowerCase().includes(searchTerm.toLowerCase()) ||
          field.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
          field.table.toLowerCase().includes(searchTerm.toLowerCase())
        )
      }

      // 只显示已选择的字段
      if (showOnlySelected) {
        fields = fields.filter(field =>
          selectedFields.some(selected => selected.field.id === field.id)
        )
      }

      // 排序
      fields = [...fields].sort((a, b) => {
        switch (sortBy) {
          case "popularity":
            return (b.popularity || 0) - (a.popularity || 0)
          case "required":
            if (a.isRequired && !b.isRequired) return -1
            if (!a.isRequired && b.isRequired) return 1
            return a.name.localeCompare(b.name)
          default:
            return a.name.localeCompare(b.name)
        }
      })

      return { ...category, fields }
    }).filter(category => category.fields.length > 0)
  }, [categories, searchTerm, showOnlySelected, sortBy, selectedFields])

  // 切换分类展开状态
  const toggleCategory = (categoryId: string) => {
    const newExpanded = new Set(expandedCategories)
    if (newExpanded.has(categoryId)) {
      newExpanded.delete(categoryId)
    } else {
      newExpanded.add(categoryId)
    }
    setExpandedCategories(newExpanded)
  }

  // 检查字段是否被选中
  const isFieldSelected = (field: MedicalField) => {
    return selectedFields.some(selected => selected.field.id === field.id)
  }

  // 切换字段选择状态
  const toggleField = (field: MedicalField) => {
    if (isFieldSelected(field)) {
      // 移除字段
      const updatedFields = selectedFields.filter(selected => selected.field.id !== field.id)
      onFieldsChange(updatedFields)
    } else {
      // 添加字段
      if (selectedFields.length >= maxFields) {
        toast.error(`最多只能选择 ${maxFields} 个字段`)
        return
      }
      const newSelectedField: SelectedField = {
        field,
        alias: undefined,
        aggregation: undefined
      }
      onFieldsChange([...selectedFields, newSelectedField])
    }
  }

  // 获取分类统计信息
  const getCategoryStats = (category: MedicalFieldCategory) => {
    const totalFields = category.fields.length
    const selectedCount = category.fields.filter(field => isFieldSelected(field)).length
    const requiredCount = category.fields.filter(field => field.isRequired).length
    
    return { totalFields, selectedCount, requiredCount }
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            字段选择器
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="animate-pulse">
                <div className="h-12 bg-slate-100 rounded-lg"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Filter className="h-5 w-5" />
          字段选择器
        </CardTitle>
        <CardDescription>
          选择要包含在查询中的医学字段
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 搜索和过滤控件 */}
        <div className="space-y-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索字段..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <div className="flex items-center gap-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="show-selected"
                checked={showOnlySelected}
                onCheckedChange={(checked) => setShowOnlySelected(checked === true)}
              />
              <label htmlFor="show-selected" className="text-sm">
                只显示已选择
              </label>
            </div>
            
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">排序:</span>
              <Button
                variant={sortBy === "name" ? "default" : "outline"}
                size="sm"
                onClick={() => setSortBy("name")}
              >
                名称
              </Button>
              <Button
                variant={sortBy === "popularity" ? "default" : "outline"}
                size="sm"
                onClick={() => setSortBy("popularity")}
              >
                热度
              </Button>
              <Button
                variant={sortBy === "required" ? "default" : "outline"}
                size="sm"
                onClick={() => setSortBy("required")}
              >
                必需
              </Button>
            </div>
          </div>
        </div>

        <Separator />

        {/* 字段分类列表 */}
        <ScrollArea className="h-[600px]">
          <div className="space-y-3">
            {filteredCategories.map((category) => {
              const stats = getCategoryStats(category)
              const isExpanded = expandedCategories.has(category.id)
              
              return (
                <Collapsible
                  key={category.id}
                  open={isExpanded}
                  onOpenChange={() => toggleCategory(category.id)}
                >
                  <CollapsibleTrigger asChild>
                    <Button
                      variant="ghost"
                      className="w-full justify-between p-3 h-auto"
                    >
                      <div className="flex items-center gap-3">
                        {getCategoryIcon(category.icon)}
                        <div className="text-left">
                          <div className="font-medium">{category.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {category.description}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary">
                          {stats.selectedCount}/{stats.totalFields}
                        </Badge>
                        {stats.requiredCount > 0 && (
                          <Badge variant="destructive" className="text-xs">
                            {stats.requiredCount} 必需
                          </Badge>
                        )}
                        {isExpanded ? (
                          <ChevronDown className="h-4 w-4" />
                        ) : (
                          <ChevronRight className="h-4 w-4" />
                        )}
                      </div>
                    </Button>
                  </CollapsibleTrigger>
                  
                  <CollapsibleContent className="space-y-2 mt-2">
                    {category.fields.map((field) => (
                      <div
                        key={field.id}
                        className="flex items-center space-x-3 p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors"
                      >
                        <Checkbox
                          checked={isFieldSelected(field)}
                          onCheckedChange={() => toggleField(field)}
                        />
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{field.name}</span>
                            <span className="text-sm text-muted-foreground">
                              ({field.nameEn})
                            </span>
                            {field.isRequired && (
                              <Star className="h-3 w-3 text-red-500" />
                            )}
                          </div>
                          
                          <div className="text-sm text-muted-foreground mt-1">
                            {field.description}
                          </div>
                          
                          <div className="flex items-center gap-2 mt-2">
                            <Badge variant="outline" className="text-xs">
                              {field.type}
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                              {field.table}
                            </Badge>
                            {field.unit && (
                              <Badge variant="outline" className="text-xs">
                                {field.unit}
                              </Badge>
                            )}
                          </div>
                          
                          {field.examples && field.examples.length > 0 && (
                            <div className="text-xs text-muted-foreground mt-1">
                              示例: {field.examples.slice(0, 3).join(", ")}
                            </div>
                          )}
                        </div>
                        
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger>
                              <Info className="h-4 w-4 text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <div className="max-w-xs">
                                <p className="font-medium">{field.name}</p>
                                <p className="text-sm">{field.description}</p>
                                <p className="text-xs mt-1">表: {field.table}</p>
                                <p className="text-xs">类型: {field.type}</p>
                                {field.examples && (
                                  <p className="text-xs mt-1">
                                    示例: {field.examples.join(", ")}
                                  </p>
                                )}
                              </div>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                    ))}
                  </CollapsibleContent>
                </Collapsible>
              )
            })}
          </div>
        </ScrollArea>

        {/* 选择统计 */}
        <Separator />
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <span>已选择 {selectedFields.length} 个字段</span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              // 清除所有选择
              onFieldsChange([])
            }}
          >
            清除选择
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
