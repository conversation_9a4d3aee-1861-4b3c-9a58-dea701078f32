package api

import (
	"net/http"
	"strconv"
	"time"

	"github.com/chungzy/medical-data-platform/internal/model"
	"github.com/chungzy/medical-data-platform/internal/service"
	"github.com/chungzy/medical-data-platform/pkg/logger"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

type MedicalQueryController struct {
	medicalQueryService *service.MedicalQueryService
}

func NewMedicalQueryController(medicalQueryService *service.MedicalQueryService) *MedicalQueryController {
	return &MedicalQueryController{
		medicalQueryService: medicalQueryService,
	}
}

// GetMedicalCategories 获取医学字段分类
func (mqc *MedicalQueryController) GetMedicalCategories(c *gin.Context) {
	c.Set("business_operation", "get_medical_categories")

	logger.InfoWithGinContext(c, "Getting medical field categories")

	categories, err := mqc.medicalQueryService.GetMedicalCategories(c.Request.Context())
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to get medical categories")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get medical categories"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"categories": categories,
	})
}

// ValidateCohort 验证人群筛选条件
func (mqc *MedicalQueryController) ValidateCohort(c *gin.Context) {
	c.Set("business_operation", "validate_cohort")

	logger.InfoWithGinContext(c, "Validating cohort criteria")

	var req model.CohortValidationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to parse cohort validation request")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format", "details": err.Error()})
		return
	}

	validation, err := mqc.medicalQueryService.ValidateCohort(c.Request.Context(), &req)
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to validate cohort")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to validate cohort"})
		return
	}

	c.JSON(http.StatusOK, validation)
}

// EstimateResults 估算查询结果数量
func (mqc *MedicalQueryController) EstimateResults(c *gin.Context) {
	c.Set("business_operation", "estimate_results")

	logger.InfoWithGinContext(c, "Estimating query results")

	var req model.MedicalQueryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to parse estimation request")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format", "details": err.Error()})
		return
	}

	estimation, err := mqc.medicalQueryService.EstimateResults(c.Request.Context(), &req)
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to estimate results")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to estimate results"})
		return
	}

	c.JSON(http.StatusOK, estimation)
}

// ExecuteMedicalQuery 执行医学查询
func (mqc *MedicalQueryController) ExecuteMedicalQuery(c *gin.Context) {
	c.Set("business_operation", "execute_medical_query")

	logger.InfoWithGinContext(c, "Starting medical query execution")

	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		logger.WarnWithGinContext(c, "User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req model.MedicalQueryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to parse medical query request")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format", "details": err.Error()})
		return
	}

	logger.InfoWithGinContext(c, "Medical query request parsed successfully", map[string]interface{}{
		"study_name":      req.StudyName,
		"cohort_criteria": len(req.CohortCriteria),
		"data_dimensions": len(req.DataDimensions),
	})

	// 执行查询
	startTime := time.Now()
	result, err := mqc.medicalQueryService.ExecuteMedicalQuery(c.Request.Context(), userID.(uuid.UUID), &req)
	executionTime := int(time.Since(startTime).Milliseconds())

	if err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to execute medical query", map[string]interface{}{
			"execution_time_ms": executionTime,
		})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to execute query", "details": err.Error()})
		return
	}

	logger.InfoWithGinContext(c, "Medical query executed successfully", map[string]interface{}{
		"query_id":          result.QueryID,
		"total_records":     result.Total,
		"execution_time_ms": executionTime,
	})

	c.JSON(http.StatusOK, result)
}

// GetQueryTemplates 获取查询模板
func (mqc *MedicalQueryController) GetQueryTemplates(c *gin.Context) {
	c.Set("business_operation", "get_query_templates")

	logger.InfoWithGinContext(c, "Getting medical query templates")

	// 获取分页参数
	pageStr := c.DefaultQuery("page", "1")
	limitStr := c.DefaultQuery("limit", "20")
	category := c.Query("category")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 100 {
		limit = 20
	}

	templates, total, err := mqc.medicalQueryService.GetQueryTemplates(c.Request.Context(), page, limit, category)
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to get query templates")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get query templates"})
		return
	}

	totalPages := (total + limit - 1) / limit

	response := model.QueryTemplatesResponse{
		Templates: templates,
		Pagination: model.PaginationInfo{
			Page:       page,
			Limit:      limit,
			Total:      total,
			TotalPages: totalPages,
		},
	}

	c.JSON(http.StatusOK, response)
}

// SaveQueryTemplate 保存查询模板
func (mqc *MedicalQueryController) SaveQueryTemplate(c *gin.Context) {
	c.Set("business_operation", "save_query_template")

	logger.InfoWithGinContext(c, "Saving query template")

	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		logger.WarnWithGinContext(c, "User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req model.SaveTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to parse save template request")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format", "details": err.Error()})
		return
	}

	template, err := mqc.medicalQueryService.SaveQueryTemplate(c.Request.Context(), userID.(uuid.UUID), &req)
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to save query template")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save template"})
		return
	}

	logger.InfoWithGinContext(c, "Query template saved successfully", map[string]interface{}{
		"template_id": template.ID,
		"name":        template.Name,
	})

	c.JSON(http.StatusCreated, template)
}

// GetMedicalQueryHistory 获取医学查询历史
func (mqc *MedicalQueryController) GetMedicalQueryHistory(c *gin.Context) {
	c.Set("business_operation", "get_medical_query_history")

	logger.InfoWithGinContext(c, "Getting medical query history")

	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		logger.WarnWithGinContext(c, "User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// 获取分页参数
	pageStr := c.DefaultQuery("page", "1")
	limitStr := c.DefaultQuery("limit", "20")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 100 {
		limit = 20
	}

	history, total, err := mqc.medicalQueryService.GetMedicalQueryHistory(c.Request.Context(), userID.(uuid.UUID), page, limit)
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to get medical query history")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get query history"})
		return
	}

	totalPages := (total + limit - 1) / limit

	response := model.MedicalQueryHistoryResponse{
		Queries: history,
		Pagination: model.PaginationInfo{
			Page:       page,
			Limit:      limit,
			Total:      total,
			TotalPages: totalPages,
		},
	}

	c.JSON(http.StatusOK, response)
}

// ExportQueryResults 导出查询结果
func (mqc *MedicalQueryController) ExportQueryResults(c *gin.Context) {
	c.Set("business_operation", "export_query_results")

	logger.InfoWithGinContext(c, "Exporting query results")

	queryIDStr := c.Param("queryId")
	queryID, err := uuid.Parse(queryIDStr)
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Invalid query ID format", map[string]interface{}{
			"query_id_str": queryIDStr,
		})
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query ID"})
		return
	}

	format := c.DefaultQuery("format", "csv")
	if format != "csv" && format != "excel" && format != "json" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid format. Supported: csv, excel, json"})
		return
	}

	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		logger.WarnWithGinContext(c, "User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	exportResult, err := mqc.medicalQueryService.ExportQueryResults(c.Request.Context(), userID.(uuid.UUID), queryID, format)
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to export query results", map[string]interface{}{
			"query_id": queryID,
			"format":   format,
		})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to export results"})
		return
	}

	logger.InfoWithGinContext(c, "Query results exported successfully", map[string]interface{}{
		"query_id":    queryID,
		"format":      format,
		"export_path": exportResult.FilePath,
	})

	c.JSON(http.StatusOK, exportResult)
}
