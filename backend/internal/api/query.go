package api

import (
	"encoding/json"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"

	"github.com/chungzy/medical-data-platform/internal/dsl"
	"github.com/chungzy/medical-data-platform/internal/model"
	"github.com/chungzy/medical-data-platform/internal/service"
	"github.com/chungzy/medical-data-platform/pkg/logger"
)

type QueryController struct {
	queryService *service.QueryService
}

func NewQueryController(queryService *service.QueryService) *QueryController {
	return &QueryController{
		queryService: queryService,
	}
}

// ExecuteQuery 执行查询
func (qc *QueryController) ExecuteQuery(c *gin.Context) {
	// 设置业务操作标识
	c.Set("business_operation", "execute_query")

	logger.InfoWithGinContext(c, "Starting query execution")

	// 获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		logger.WarnWithGinContext(c, "User ID not found in context")
		c.J<PERSON>(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// 解析请求
	var req model.QueryExecuteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to parse query request")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format", "details": err.Error()})
		return
	}

	logger.InfoWithGinContext(c, "Query request parsed successfully", map[string]interface{}{
		"dataset":          req.Dataset,
		"fields_count":     len(req.Fields),
		"conditions_count": len(req.Conditions),
	})

	// 构建DSL查询对象
	queryDSL := &model.QueryDSL{
		Dataset: req.Dataset,
		Select:  req.Fields,
	}

	// 处理查询条件
	if len(req.Conditions) > 0 {
		queryDSL.Where = &model.ConditionGroup{
			Logic:      "AND",
			Conditions: req.Conditions,
		}
	}

	// 处理时间范围
	if req.TimeRange != nil {
		timeConditions := []model.Condition{
			{
				Field:    "created_at",
				Operator: ">=",
				Value:    req.TimeRange.Start,
			},
			{
				Field:    "created_at",
				Operator: "<=",
				Value:    req.TimeRange.End,
			},
		}

		if queryDSL.Where == nil {
			queryDSL.Where = &model.ConditionGroup{
				Logic:      "AND",
				Conditions: timeConditions,
			}
		} else {
			queryDSL.Where.Conditions = append(queryDSL.Where.Conditions, timeConditions...)
		}
	}

	// 设置分页
	if req.Limit != nil {
		queryDSL.Limit = req.Limit
	}
	if req.Offset != nil {
		queryDSL.Offset = req.Offset
	}

	// 使用DSL构建器生成SQL
	builder := dsl.NewSQLBuilder(req.Dataset)
	sql, args, err := builder.BuildQuery(queryDSL)
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to build SQL query", map[string]interface{}{
			"dataset": req.Dataset,
		})
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to build query", "details": err.Error()})
		return
	}

	logger.InfoWithGinContext(c, "SQL query built successfully", map[string]interface{}{
		"dataset": req.Dataset,
	})

	// 执行查询
	startTime := time.Now()
	data, total, err := qc.queryService.ExecuteQueryWithCount(c.Request.Context(), sql, args)
	executionTime := int(time.Since(startTime).Milliseconds())

	if err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to execute query", map[string]interface{}{
			"dataset":           req.Dataset,
			"execution_time_ms": executionTime,
		})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to execute query", "details": err.Error()})
		return
	}

	logger.InfoWithGinContext(c, "Query executed successfully", map[string]interface{}{
		"dataset":           req.Dataset,
		"total_records":     total,
		"execution_time_ms": executionTime,
	})

	// 生成查询ID
	queryID := uuid.New()

	// 保存查询历史
	queryConfig, _ := json.Marshal(queryDSL)
	history := &model.QueryHistory{
		ID:            queryID,
		UserID:        userID.(uuid.UUID),
		DatasetID:     req.Dataset,
		QueryConfig:   queryConfig,
		Status:        "completed",
		RecordCount:   &total,
		ExecutionTime: &executionTime,
		CreatedAt:     time.Now(),
	}

	if err := qc.queryService.SaveQueryHistory(c.Request.Context(), history); err != nil {
		// 记录错误但不影响响应
		logger.ErrorWithGinContext(c, err, "Failed to save query history", map[string]interface{}{
			"query_id": queryID,
			"user_id":  userID,
		})
	} else {
		logger.InfoWithGinContext(c, "Query history saved successfully", map[string]interface{}{
			"query_id": queryID,
		})
	}

	// 返回响应
	response := model.QueryExecuteResponse{
		QueryID:       queryID,
		Data:          data,
		Total:         total,
		ExecutionTime: executionTime,
		Status:        "completed",
	}

	logger.InfoWithGinContext(c, "Query execution completed successfully", map[string]interface{}{
		"query_id":          queryID,
		"total_records":     total,
		"execution_time_ms": executionTime,
	})

	c.JSON(http.StatusOK, response)
}

// GetQueryStatus 获取查询状态
func (qc *QueryController) GetQueryStatus(c *gin.Context) {
	c.Set("business_operation", "get_query_status")

	logger.InfoWithGinContext(c, "Getting query status")

	queryIDStr := c.Param("queryId")
	queryID, err := uuid.Parse(queryIDStr)
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Invalid query ID format", map[string]interface{}{
			"query_id_str": queryIDStr,
		})
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query ID"})
		return
	}

	history, err := qc.queryService.GetQueryHistoryByID(c.Request.Context(), queryID)
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Query not found", map[string]interface{}{
			"query_id": queryID,
		})
		c.JSON(http.StatusNotFound, gin.H{"error": "Query not found"})
		return
	}

	response := model.QueryStatusResponse{
		QueryID: queryID,
		Status:  history.Status,
	}

	logger.InfoWithGinContext(c, "Query status retrieved successfully", map[string]interface{}{
		"query_id": queryID,
		"status":   history.Status,
	})

	c.JSON(http.StatusOK, response)
}

// GetQueryResults 获取查询结果
func (qc *QueryController) GetQueryResults(c *gin.Context) {
	c.Set("business_operation", "get_query_results")

	logger.InfoWithGinContext(c, "Getting query results")

	queryIDStr := c.Param("queryId")
	queryID, err := uuid.Parse(queryIDStr)
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Invalid query ID format", map[string]interface{}{
			"query_id_str": queryIDStr,
		})
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query ID"})
		return
	}

	// 获取分页参数
	pageStr := c.DefaultQuery("page", "1")
	limitStr := c.DefaultQuery("limit", "100")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 1000 {
		limit = 100
	}

	offset := (page - 1) * limit

	logger.InfoWithGinContext(c, "Pagination parameters parsed", map[string]interface{}{
		"query_id": queryID,
		"page":     page,
		"limit":    limit,
		"offset":   offset,
	})

	// 获取查询历史
	history, err := qc.queryService.GetQueryHistoryByID(c.Request.Context(), queryID)
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Query not found", map[string]interface{}{
			"query_id": queryID,
		})
		c.JSON(http.StatusNotFound, gin.H{"error": "Query not found"})
		return
	}

	// 重新执行查询以获取分页结果
	var queryDSL model.QueryDSL
	if err := json.Unmarshal(history.QueryConfig, &queryDSL); err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to parse query config", map[string]interface{}{
			"query_id": queryID,
		})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to parse query config"})
		return
	}

	// 设置分页
	queryDSL.Limit = &limit
	queryDSL.Offset = &offset

	// 构建SQL
	builder := dsl.NewSQLBuilder(history.DatasetID)
	sql, args, err := builder.BuildQuery(&queryDSL)
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to build query", map[string]interface{}{
			"query_id": queryID,
			"dataset":  history.DatasetID,
		})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to build query"})
		return
	}

	// 执行查询
	startTime := time.Now()
	data, err := qc.queryService.ExecuteQuery(c.Request.Context(), sql, args)
	executionTime := int(time.Since(startTime).Milliseconds())

	if err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to execute query", map[string]interface{}{
			"query_id":          queryID,
			"execution_time_ms": executionTime,
		})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to execute query"})
		return
	}

	// 计算总页数
	total := 0
	if history.RecordCount != nil {
		total = *history.RecordCount
	}
	totalPages := (total + limit - 1) / limit

	// 返回响应
	response := model.QueryResultsResponse{
		Data: data,
		Pagination: model.PaginationInfo{
			Page:       page,
			Limit:      limit,
			Total:      total,
			TotalPages: totalPages,
		},
	}

	logger.InfoWithGinContext(c, "Query results retrieved successfully", map[string]interface{}{
		"query_id":          queryID,
		"page":              page,
		"limit":             limit,
		"total_records":     total,
		"execution_time_ms": executionTime,
	})

	c.JSON(http.StatusOK, response)
}
