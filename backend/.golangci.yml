run:
  timeout: 5m
  issues-exit-code: 1
  tests: true
  skip-dirs:
    - vendor
    - bin
    - logs
    - exports
    - tmp
  skip-files:
    - ".*\\.pb\\.go$"
    - ".*_test\\.go$"

output:
  format: colored-line-number
  print-issued-lines: true
  print-linter-name: true

linters-settings:
  govet:
    check-shadowing: true
    settings:
      printf:
        funcs:
          - (github.com/sirupsen/logrus.FieldLogger).Infof
          - (github.com/sirupsen/logrus.FieldLogger).Warnf
          - (github.com/sirupsen/logrus.FieldLogger).Errorf
          - (github.com/sirupsen/logrus.FieldLogger).Fatalf

  golint:
    min-confidence: 0

  gocyclo:
    min-complexity: 15

  maligned:
    suggest-new: true

  dupl:
    threshold: 100

  goconst:
    min-len: 2
    min-occurrences: 2

  depguard:
    list-type: blacklist
    packages:
      - github.com/sirupsen/logrus
    packages-with-error-message:
      - github.com/sirupsen/logrus: "logging is allowed only by pkg/logger"

  misspell:
    locale: US

  lll:
    line-length: 140

  goimports:
    local-prefixes: medical-data-platform-server

  gocritic:
    enabled-tags:
      - diagnostic
      - experimental
      - opinionated
      - performance
      - style
    disabled-checks:
      - dupImport
      - ifElseChain
      - octalLiteral
      - whyNoLint
      - wrapperFunc

  funlen:
    lines: 100
    statements: 50

  gocognit:
    min-complexity: 20

  nestif:
    min-complexity: 4

  gomnd:
    settings:
      mnd:
        checks: argument,case,condition,operation,return,assign
        ignored-numbers: 0,1,2,3
        ignored-functions: strings.SplitN

  godox:
    keywords:
      - NOTE
      - OPTIMIZE
      - HACK

  dogsled:
    max-blank-identifiers: 2

  whitespace:
    multi-if: false
    multi-func: false

linters:
  disable-all: true
  enable:
    - bodyclose
    - deadcode
    - depguard
    - dogsled
    - dupl
    - errcheck
    - funlen
    - gochecknoinits
    - goconst
    - gocritic
    - gocyclo
    - gofmt
    - goimports
    - golint
    - gomnd
    - goprintffuncname
    - gosec
    - gosimple
    - govet
    - ineffassign
    - interfacer
    - lll
    - misspell
    - nakedret
    - noctx
    - nolintlint
    - rowserrcheck
    - scopelint
    - staticcheck
    - structcheck
    - stylecheck
    - typecheck
    - unconvert
    - unparam
    - unused
    - varcheck
    - whitespace

issues:
  exclude-rules:
    - path: _test\.go
      linters:
        - gomnd
        - funlen
        - gocyclo

    - path: cmd/
      linters:
        - gochecknoinits

    - path: internal/dsl/
      linters:
        - gocyclo
        - funlen

    - linters:
        - lll
      source: "^//go:generate "

  exclude:
    - 'declaration of "(err|ctx)" shadows declaration at'
    - 'shadow: declaration of "err" shadows declaration'
    - 'G104: Errors unhandled'
    - 'G204: Subprocess launched with variable'
    - 'G304: Potential file inclusion via variable'

  exclude-use-default: false
  max-issues-per-linter: 50
  max-same-issues: 3

severity:
  default-severity: error
  case-sensitive: false 