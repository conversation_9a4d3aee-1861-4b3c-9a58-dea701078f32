"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Database,
  Search,
  BarChart3,
  FileText,
  History,
  Plus,
  TrendingUp,
  Users,
  Activity,
  Clock,
  ArrowUpRight,
  Sparkles,
  Zap,
  Brain,
  AlertCircle,
} from "lucide-react"
import Link from "next/link"
import { DashboardLayout } from "@/components/dashboard-layout"
import { useDatasets } from "@/lib/hooks/use-datasets"
import { apiClient } from "@/lib/api"

export default function DashboardPage() {
  const { datasets, loading: datasetsLoading, error: datasetsError } = useDatasets()
  const [recentQueries, setRecentQueries] = useState([])
  const [stats, setStats] = useState({
    totalQueries: 247,
    activeDatasets: 3,
    dataExported: "2.4GB",
    savedTemplates: 18,
  })

  useEffect(() => {
    fetchRecentQueries()
  }, [])

  const fetchRecentQueries = async () => {
    try {
      const response = await apiClient.getQueryHistory(1, 5)
      if (response.data) {
        setRecentQueries(response.data.queries || [])
      } else {
        // 使用模拟数据
        /*
        setRecentQueries([
          {
            id: 1,
            name: "ICU患者首次入院指标",
            dataset: "MIMIC-IV",
            date: "2小时前",
            status: "completed",
            records: "1,250",
            trend: "+12%",
          },
          {
            id: 2,
            name: "机械通气患者死亡率分析",
            dataset: "eICU-CRD",
            date: "1天前",
            status: "completed",
            records: "890",
            trend: "-5%",
          },
          {
            id: 3,
            name: "心血管疾病风险因素",
            dataset: "NHANES",
            date: "2天前",
            status: "running",
            records: "2,150",
            trend: "+8%",
          },
        ])
          */
      }
    } catch (error) {
      console.error("Failed to fetch recent queries:", error)
    }
  }

  const activeDatasets = datasets.filter((d) => d.status === "active")

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Welcome Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
          <div className="space-y-2">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
                <span className="text-sm text-slate-600">在线</span>
              </div>
            </div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 bg-clip-text text-transparent">
              欢迎回来，张医生
            </h1>
            <p className="text-lg text-slate-600">今天是个开始新研究的好日子 ✨</p>
          </div>

          <div className="flex items-center gap-3">
            <Button variant="outline" size="lg" className="gap-2 border-slate-200 hover:bg-slate-50">
              <Brain className="h-4 w-4" />
              AI 助手
            </Button>
            <Link href="/query-builder">
              <Button size="lg" className="gap-2 bg-slate-900 hover:bg-slate-800 text-white shadow-lg">
                <Plus className="h-4 w-4" />
                新建查询
              </Button>
            </Link>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="border-0 shadow-lg bg-gradient-to-br from-slate-50 to-slate-100/50">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <p className="text-sm font-medium text-slate-700">总查询数</p>
                  <p className="text-3xl font-bold text-slate-900">{stats.totalQueries}</p>
                  <div className="flex items-center gap-1 text-xs text-emerald-600">
                    <TrendingUp className="h-3 w-3" />
                    <span>+12% 本周</span>
                  </div>
                </div>
                <div className="p-3 bg-slate-900 rounded-2xl">
                  <Search className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg bg-gradient-to-br from-emerald-50 to-emerald-100/50">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <p className="text-sm font-medium text-emerald-700">活跃数据集</p>
                  <p className="text-3xl font-bold text-emerald-900">{activeDatasets.length}</p>
                  <div className="flex items-center gap-1 text-xs text-emerald-600">
                    <Activity className="h-3 w-3" />
                    <span>共{datasets.length}个数据集</span>
                  </div>
                </div>
                <div className="p-3 bg-emerald-600 rounded-2xl">
                  <Database className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg bg-gradient-to-br from-amber-50 to-amber-100/50">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <p className="text-sm font-medium text-amber-700">数据导出</p>
                  <p className="text-3xl font-bold text-amber-900">{stats.dataExported}</p>
                  <div className="flex items-center gap-1 text-xs text-amber-600">
                    <ArrowUpRight className="h-3 w-3" />
                    <span>本月导出</span>
                  </div>
                </div>
                <div className="p-3 bg-amber-600 rounded-2xl">
                  <FileText className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg bg-gradient-to-br from-rose-50 to-rose-100/50">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <p className="text-sm font-medium text-rose-700">保存模板</p>
                  <p className="text-3xl font-bold text-rose-900">{stats.savedTemplates}</p>
                  <div className="flex items-center gap-1 text-xs text-rose-600">
                    <Zap className="h-3 w-3" />
                    <span>+3 本周新增</span>
                  </div>
                </div>
                <div className="p-3 bg-rose-600 rounded-2xl">
                  <BarChart3 className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Recent Activity */}
          <div className="lg:col-span-2 space-y-6">
            <Card className="border-0 shadow-lg">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <CardTitle className="text-xl font-semibold">最近活动</CardTitle>
                    <CardDescription>您最近执行的数据查询</CardDescription>
                  </div>
                  <Button variant="ghost" size="sm" className="gap-2">
                    <History className="h-4 w-4" />
                    查看全部
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {recentQueries.map((query: any) => (
                  <div
                    key={query.id}
                    className="group p-4 rounded-xl border border-slate-100 hover:border-slate-200 hover:shadow-md transition-all duration-200"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1 space-y-2">
                        <div className="flex items-center gap-3">
                          <h4 className="font-semibold text-slate-900 group-hover:text-slate-700 transition-colors">
                            {query.name}
                          </h4>
                          <Badge variant="secondary" className="text-xs font-medium">
                            {query.dataset}
                          </Badge>
                        </div>

                        <div className="flex items-center gap-6 text-sm text-slate-600">
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            <span>{query.date}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Users className="h-3 w-3" />
                            <span>{query.records} 记录</span>
                          </div>
                          <div
                            className={`flex items-center gap-1 font-medium ${
                              query.trend.startsWith("+") ? "text-emerald-600" : "text-rose-500"
                            }`}
                          >
                            <TrendingUp className="h-3 w-3" />
                            <span>{query.trend}</span>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <Badge
                          variant={query.status === "completed" ? "default" : "secondary"}
                          className={
                            query.status === "completed" ? "bg-emerald-100 text-emerald-700 hover:bg-emerald-100" : ""
                          }
                        >
                          {query.status === "completed" ? "已完成" : "运行中"}
                        </Badge>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <ArrowUpRight className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="text-xl font-semibold">快速操作</CardTitle>
                <CardDescription>常用功能快速入口</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <Link href="/query-builder">
                    <Button
                      variant="outline"
                      className="h-24 flex flex-col items-center gap-3 hover:bg-slate-50 hover:border-slate-300 transition-all duration-200"
                    >
                      <div className="p-2 bg-slate-100 rounded-lg">
                        <Search className="h-5 w-5 text-slate-700" />
                      </div>
                      <span className="text-sm font-medium">查询构建器</span>
                    </Button>
                  </Link>

                  <Link href="/templates">
                    <Button
                      variant="outline"
                      className="h-24 flex flex-col items-center gap-3 hover:bg-emerald-50 hover:border-emerald-200 transition-all duration-200"
                    >
                      <div className="p-2 bg-emerald-100 rounded-lg">
                        <FileText className="h-5 w-5 text-emerald-700" />
                      </div>
                      <span className="text-sm font-medium">查询模板</span>
                    </Button>
                  </Link>

                  <Link href="/dictionary">
                    <Button
                      variant="outline"
                      className="h-24 flex flex-col items-center gap-3 hover:bg-amber-50 hover:border-amber-200 transition-all duration-200"
                    >
                      <div className="p-2 bg-amber-100 rounded-lg">
                        <Database className="h-5 w-5 text-amber-700" />
                      </div>
                      <span className="text-sm font-medium">数据字典</span>
                    </Button>
                  </Link>

                  <Link href="/visualization">
                    <Button
                      variant="outline"
                      className="h-24 flex flex-col items-center gap-3 hover:bg-rose-50 hover:border-rose-200 transition-all duration-200"
                    >
                      <div className="p-2 bg-rose-100 rounded-lg">
                        <BarChart3 className="h-5 w-5 text-rose-700" />
                      </div>
                      <span className="text-sm font-medium">数据可视化</span>
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Datasets Overview */}
          <div className="space-y-6">
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <CardTitle className="text-xl font-semibold">数据集概览</CardTitle>
                    <CardDescription>当前可访问的医学数据库</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {datasetsError && (
                  <div className="p-3 bg-amber-50 border border-amber-200 rounded-lg">
                    <div className="flex items-center gap-2 text-amber-800">
                      <AlertCircle className="h-4 w-4" />
                      <span className="text-sm font-medium">数据源提示</span>
                    </div>
                    <p className="text-xs text-amber-700 mt-1">{datasetsError}</p>
                  </div>
                )}
                {datasetsLoading ? (
                  <div className="space-y-3">
                    {[1, 2, 3].map((i) => (
                      <div key={i} className="animate-pulse">
                        <div className="h-20 bg-slate-100 rounded-xl"></div>
                      </div>
                    ))}
                  </div>
                ) : (
                  datasets.slice(0, 4).map((dataset, index) => {
                    const colors = [
                      "from-slate-600 to-slate-700",
                      "from-emerald-600 to-emerald-700",
                      "from-amber-600 to-amber-700",
                      "from-rose-600 to-rose-700",
                    ]
                    return (
                      <div
                        key={dataset.id}
                        className="group p-4 rounded-xl border border-slate-100 hover:border-slate-200 hover:shadow-md transition-all duration-200"
                      >
                        <div className="flex items-start gap-4">
                          <div className={`p-3 rounded-xl bg-gradient-to-r ${colors[index % colors.length]} shadow-lg`}>
                            <Database className="h-5 w-5 text-white" />
                          </div>

                          <div className="flex-1 space-y-3">
                            <div className="space-y-1">
                              <div className="flex items-center gap-2">
                                <h4 className="font-semibold text-slate-900">{dataset.name}</h4>
                                <Badge
                                  variant={dataset.status === "active" ? "default" : "secondary"}
                                  className={
                                    dataset.status === "active"
                                      ? "bg-emerald-100 text-emerald-700 hover:bg-emerald-100"
                                      : ""
                                  }
                                >
                                  {dataset.status === "active" ? "可用" : "维护中"}
                                </Badge>
                              </div>
                              <p className="text-sm text-slate-600">{dataset.description}</p>
                            </div>

                            <div className="space-y-2">
                              <div className="flex justify-between text-xs text-slate-600">
                                <span>使用率</span>
                                <span>{Math.floor(Math.random() * 40 + 60)}%</span>
                              </div>
                              <div className="w-full bg-slate-200 rounded-full h-1.5">
                                <div
                                  className="bg-gradient-to-r from-slate-600 to-slate-700 h-1.5 rounded-full transition-all duration-300"
                                  style={{ width: `${Math.floor(Math.random() * 40 + 60)}%` }}
                                ></div>
                              </div>
                            </div>

                            <div className="flex items-center justify-between text-xs text-slate-600">
                              <span>版本: {dataset.version || 'N/A'}</span>
                              <span>{dataset.patientCount?.toLocaleString() || '0'} 患者</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    )
                  })
                )}
              </CardContent>
            </Card>

            {/* AI Insights */}
            <Card className="border-0 shadow-lg bg-gradient-to-br from-slate-50 to-slate-100">
              <CardHeader>
                <div className="flex items-center gap-2">
                  <div className="p-2 bg-gradient-to-r from-slate-700 to-slate-800 rounded-lg">
                    <Sparkles className="h-4 w-4 text-white" />
                  </div>
                  <CardTitle className="text-lg font-semibold bg-gradient-to-r from-slate-700 to-slate-800 bg-clip-text text-transparent">
                    AI 洞察
                  </CardTitle>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="p-3 bg-white/80 rounded-lg border border-slate-200">
                    <p className="text-sm font-medium text-slate-800">💡 建议</p>
                    <p className="text-xs text-slate-600 mt-1">
                      基于您的查询历史，建议探索 MIMIC-IV 中的心血管疾病相关数据
                    </p>
                  </div>

                  <div className="p-3 bg-white/80 rounded-lg border border-slate-200">
                    <p className="text-sm font-medium text-slate-800">📊 趋势</p>
                    <p className="text-xs text-slate-600 mt-1">ICU 患者死亡率分析是本月最热门的查询类型</p>
                  </div>

                  <div className="p-3 bg-white/80 rounded-lg border border-slate-200">
                    <p className="text-sm font-medium text-slate-800">🎯 优化</p>
                    <p className="text-xs text-slate-600 mt-1">您的查询效率比平均水平高 23%，继续保持！</p>
                  </div>
                </div>

                <Button className="w-full bg-gradient-to-r from-slate-700 to-slate-800 hover:from-slate-800 hover:to-slate-900">
                  获取更多洞察
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
