package file_cleanup

import (
	"os"
	"path/filepath"
	"sync"
	"time"

	"github.com/chungzy/medical-data-platform/internal/config"
	"github.com/chungzy/medical-data-platform/pkg/logger"
)

// FileCleanupWorker 文件清理worker
type FileCleanupWorker struct {
	config    *config.Config
	ticker    *time.Ticker
	stopChan  chan struct{}
	isRunning bool
	mu        sync.RWMutex
	stats     FileCleanupStats
}

// FileCleanupStats 文件清理统计
type FileCleanupStats struct {
	LastRunTime       time.Time `json:"last_run_time"`
	TotalRuns         int64     `json:"total_runs"`
	TotalFilesDeleted int64     `json:"total_files_deleted"`
	TotalBytesFreed   int64     `json:"total_bytes_freed"`
	LastError         string    `json:"last_error,omitempty"`
}

// NewFileCleanupWorker 创建文件清理worker
func NewFileCleanupWorker(cfg *config.Config) *FileCleanupWorker {
	return &FileCleanupWorker{
		config:   cfg,
		stop<PERSON>han: make(chan struct{}),
		stats:    FileCleanupStats{},
	}
}

// GetName 获取worker名称
func (w *FileCleanupWorker) GetName() string {
	return "file_cleanup_worker"
}

// GetStatus 获取worker状态
func (w *FileCleanupWorker) GetStatus() map[string]interface{} {
	w.mu.RLock()
	defer w.mu.RUnlock()

	return map[string]interface{}{
		"name":                w.GetName(),
		"is_running":          w.isRunning,
		"cleanup_interval":    w.config.App.FileCleanup.Interval,
		"export_retention":    w.config.App.ExportFileRetentionDays,
		"temp_retention":      1, // 临时文件固定1天
		"last_run_time":       w.stats.LastRunTime,
		"total_runs":          w.stats.TotalRuns,
		"total_files_deleted": w.stats.TotalFilesDeleted,
		"total_bytes_freed":   w.stats.TotalBytesFreed,
		"last_error":          w.stats.LastError,
	}
}

// Start 启动文件清理worker
func (w *FileCleanupWorker) Start() error {
	w.mu.Lock()
	defer w.mu.Unlock()

	if w.isRunning {
		return nil
	}

	// 验证配置
	if !w.config.App.FileCleanup.Enable {
		logger.Warn("File cleanup disabled in configuration")
		return nil
	}

	logger.Info("Starting file cleanup worker", map[string]interface{}{
		"cleanup_interval": w.config.App.FileCleanup.Interval,
		"export_retention": w.config.App.ExportFileRetentionDays,
		"temp_retention":   1,
	})

	// 解析清理间隔
	interval, err := time.ParseDuration(w.config.App.FileCleanup.Interval)
	if err != nil {
		logger.Error("Invalid cleanup interval", map[string]interface{}{
			"interval": w.config.App.FileCleanup.Interval,
			"error":    err.Error(),
		})
		return err
	}

	// 创建定时器
	w.ticker = time.NewTicker(interval)
	w.isRunning = true

	// 启动清理goroutine
	go w.cleanupLoop()

	// 立即执行一次清理（如果配置启用）
	if w.config.App.FileCleanup.CleanupOnStartup {
		go w.performCleanup()
	}

	logger.Info("File cleanup worker started successfully")
	return nil
}

// Stop 停止文件清理worker
func (w *FileCleanupWorker) Stop() {
	w.mu.Lock()
	defer w.mu.Unlock()

	if !w.isRunning {
		return
	}

	logger.Info("Stopping file cleanup worker")

	// 停止定时器
	if w.ticker != nil {
		w.ticker.Stop()
	}

	// 发送停止信号
	close(w.stopChan)
	w.isRunning = false

	logger.Info("File cleanup worker stopped")
}

// cleanupLoop 清理循环
func (w *FileCleanupWorker) cleanupLoop() {
	for {
		select {
		case <-w.ticker.C:
			w.performCleanup()
		case <-w.stopChan:
			return
		}
	}
}

// performCleanup 执行清理操作
func (w *FileCleanupWorker) performCleanup() {
	logger.Info("Starting file cleanup operation")

	w.mu.Lock()
	w.stats.LastRunTime = time.Now()
	w.stats.TotalRuns++
	w.stats.LastError = ""
	w.mu.Unlock()

	var totalDeleted int64
	var totalFreed int64

	// 清理导出文件
	if deleted, freed, err := w.cleanupDirectory(
		w.config.App.FileCleanup.ExportDir,
		time.Duration(w.config.App.ExportFileRetentionDays)*24*time.Hour,
		"export files",
	); err != nil {
		w.mu.Lock()
		w.stats.LastError = err.Error()
		w.mu.Unlock()
		logger.Error("Failed to cleanup export files", map[string]interface{}{
			"error": err.Error(),
		})
	} else {
		totalDeleted += deleted
		totalFreed += freed
	}

	// 清理临时文件
	if deleted, freed, err := w.cleanupDirectory(
		w.config.App.FileCleanup.TempDir,
		24*time.Hour, // 临时文件保留1天
		"temp files",
	); err != nil {
		w.mu.Lock()
		if w.stats.LastError == "" {
			w.stats.LastError = err.Error()
		}
		w.mu.Unlock()
		logger.Error("Failed to cleanup temp files", map[string]interface{}{
			"error": err.Error(),
		})
	} else {
		totalDeleted += deleted
		totalFreed += freed
	}

	// 更新统计
	w.mu.Lock()
	w.stats.TotalFilesDeleted += totalDeleted
	w.stats.TotalBytesFreed += totalFreed
	w.mu.Unlock()

	logger.Info("File cleanup operation completed", map[string]interface{}{
		"files_deleted": totalDeleted,
		"bytes_freed":   totalFreed,
	})
}

// cleanupDirectory 清理指定目录
func (w *FileCleanupWorker) cleanupDirectory(dir string, maxAge time.Duration, fileType string) (int64, int64, error) {
	if dir == "" {
		logger.Warn("Directory path is empty, skipping cleanup", map[string]interface{}{
			"file_type": fileType,
		})
		return 0, 0, nil
	}

	logger.Info("Cleaning up directory", map[string]interface{}{
		"directory": dir,
		"max_age":   maxAge,
		"file_type": fileType,
	})

	// 检查目录是否存在
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		logger.Info("Directory does not exist, skipping cleanup", map[string]interface{}{
			"directory": dir,
			"file_type": fileType,
		})
		return 0, 0, nil
	}

	var filesDeleted int64
	var bytesFreed int64
	cutoffTime := time.Now().Add(-maxAge)

	err := filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			logger.Warn("Error accessing file during cleanup", map[string]interface{}{
				"path":  path,
				"error": err.Error(),
			})
			return nil // 继续处理其他文件
		}

		// 跳过目录
		if info.IsDir() {
			return nil
		}

		// 检查文件是否过期
		if info.ModTime().Before(cutoffTime) {
			logger.Info("Deleting expired file", map[string]interface{}{
				"path":      path,
				"file_type": fileType,
				"mod_time":  info.ModTime(),
				"size":      info.Size(),
			})

			if err := os.Remove(path); err != nil {
				logger.Error("Failed to delete file", map[string]interface{}{
					"path":  path,
					"error": err.Error(),
				})
				return nil // 继续处理其他文件
			}

			filesDeleted++
			bytesFreed += info.Size()
		}

		return nil
	})

	if err != nil {
		return filesDeleted, bytesFreed, err
	}

	logger.Info("Directory cleanup completed", map[string]interface{}{
		"directory":     dir,
		"file_type":     fileType,
		"files_deleted": filesDeleted,
		"bytes_freed":   bytesFreed,
	})

	return filesDeleted, bytesFreed, nil
}
