package repository

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/chungzy/medical-data-platform/internal/model"
	"github.com/google/uuid"
	"github.com/jmoiron/sqlx"
)

type PermissionRepositoryImpl struct {
	db *sqlx.DB
}

func NewPermissionRepository(db *sqlx.DB) *PermissionRepositoryImpl {
	return &PermissionRepositoryImpl{db: db}
}

// GetUserPermissions 获取用户的所有权限信息
func (r *PermissionRepositoryImpl) GetUserPermissions(ctx context.Context, userID uuid.UUID) (*model.UserPermissions, error) {
	userPermissions := &model.UserPermissions{
		UserID:      userID,
		Roles:       []model.Role{},
		Permissions: []model.Permission{},
		Datasets:    []string{},
	}

	// 获取用户角色
	roles, err := r.GetUserRoles(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user roles: %w", err)
	}
	userPermissions.Roles = roles

	// 获取权限
	query := `
		SELECT DISTINCT p.id, p.name, p.code, p.description, p.resource, p.action, p.created_at, p.updated_at
		FROM user_roles ur
		JOIN role_permissions rp ON ur.role_id = rp.role_id
		JOIN permissions p ON rp.permission_id = p.id
		WHERE ur.user_id = $1
		ORDER BY p.resource, p.action
	`

	var permissions []model.Permission
	err = r.db.SelectContext(ctx, &permissions, query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user permissions: %w", err)
	}
	userPermissions.Permissions = permissions

	// 获取数据集权限
	datasetQuery := `
		SELECT DISTINCT dataset_id 
		FROM dataset_permissions 
		WHERE user_id = $1
	`

	var datasets []string
	err = r.db.SelectContext(ctx, &datasets, datasetQuery, userID)
	if err != nil && err != sql.ErrNoRows {
		return nil, fmt.Errorf("failed to get user datasets: %w", err)
	}
	userPermissions.Datasets = datasets

	return userPermissions, nil
}

// GetUserRoles 获取用户角色
func (r *PermissionRepositoryImpl) GetUserRoles(ctx context.Context, userID uuid.UUID) ([]model.Role, error) {
	query := `
		SELECT r.id, r.name, r.code, r.description, r.level, r.is_system, r.created_at, r.updated_at
		FROM user_roles ur
		JOIN roles r ON ur.role_id = r.id
		WHERE ur.user_id = $1
		ORDER BY r.level DESC
	`

	var roles []model.Role
	err := r.db.SelectContext(ctx, &roles, query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user roles: %w", err)
	}

	// 为每个角色获取权限
	for i := range roles {
		permissions, err := r.getRolePermissions(ctx, roles[i].ID)
		if err != nil {
			return nil, fmt.Errorf("failed to get role permissions: %w", err)
		}
		roles[i].Permissions = permissions
	}

	return roles, nil
}

// getRolePermissions 获取角色的权限
func (r *PermissionRepositoryImpl) getRolePermissions(ctx context.Context, roleID uuid.UUID) ([]model.Permission, error) {
	query := `
		SELECT p.id, p.name, p.code, p.description, p.resource, p.action, p.created_at, p.updated_at
		FROM role_permissions rp
		JOIN permissions p ON rp.permission_id = p.id
		WHERE rp.role_id = $1
		ORDER BY p.resource, p.action
	`

	var permissions []model.Permission
	err := r.db.SelectContext(ctx, &permissions, query, roleID)
	if err != nil {
		return nil, fmt.Errorf("failed to get role permissions: %w", err)
	}

	return permissions, nil
}

// CheckDatasetAccess 检查用户对数据集的访问权限
func (r *PermissionRepositoryImpl) CheckDatasetAccess(ctx context.Context, userID uuid.UUID, datasetID string) (bool, error) {
	query := `
		SELECT EXISTS(
			SELECT 1 FROM dataset_permissions 
			WHERE user_id = $1 AND dataset_id = $2
		)
	`

	var hasAccess bool
	err := r.db.GetContext(ctx, &hasAccess, query, userID, datasetID)
	if err != nil {
		return false, fmt.Errorf("failed to check dataset access: %w", err)
	}

	return hasAccess, nil
}

// CreateRole 创建角色
func (r *PermissionRepositoryImpl) CreateRole(ctx context.Context, role *model.Role) error {
	if role.ID == uuid.Nil {
		role.ID = uuid.New()
	}

	query := `
		INSERT INTO roles (id, name, code, description, level, is_system)
		VALUES (:id, :name, :code, :description, :level, :is_system)
		ON CONFLICT (code) DO NOTHING
	`

	_, err := r.db.NamedExecContext(ctx, query, role)
	if err != nil {
		return fmt.Errorf("failed to create role: %w", err)
	}

	return nil
}

// CreatePermission 创建权限
func (r *PermissionRepositoryImpl) CreatePermission(ctx context.Context, permission *model.Permission) error {
	if permission.ID == uuid.Nil {
		permission.ID = uuid.New()
	}

	query := `
		INSERT INTO permissions (id, name, code, description, resource, action)
		VALUES (:id, :name, :code, :description, :resource, :action)
		ON CONFLICT (code) DO NOTHING
	`

	_, err := r.db.NamedExecContext(ctx, query, permission)
	if err != nil {
		return fmt.Errorf("failed to create permission: %w", err)
	}

	return nil
}

// AssignRoleToUser 为用户分配角色
func (r *PermissionRepositoryImpl) AssignRoleToUser(ctx context.Context, userID, roleID uuid.UUID) error {
	query := `
		INSERT INTO user_roles (user_id, role_id)
		VALUES ($1, $2)
		ON CONFLICT (user_id, role_id) DO NOTHING
	`

	_, err := r.db.ExecContext(ctx, query, userID, roleID)
	if err != nil {
		return fmt.Errorf("failed to assign role to user: %w", err)
	}

	return nil
}

// AssignPermissionToRole 为角色分配权限
func (r *PermissionRepositoryImpl) AssignPermissionToRole(ctx context.Context, roleID, permissionID uuid.UUID) error {
	query := `
		INSERT INTO role_permissions (role_id, permission_id)
		VALUES ($1, $2)
		ON CONFLICT (role_id, permission_id) DO NOTHING
	`

	_, err := r.db.ExecContext(ctx, query, roleID, permissionID)
	if err != nil {
		return fmt.Errorf("failed to assign permission to role: %w", err)
	}

	return nil
}

// RemoveRoleFromUser 移除用户角色
func (r *PermissionRepositoryImpl) RemoveRoleFromUser(ctx context.Context, userID, roleID uuid.UUID) error {
	query := `DELETE FROM user_roles WHERE user_id = $1 AND role_id = $2`

	_, err := r.db.ExecContext(ctx, query, userID, roleID)
	if err != nil {
		return fmt.Errorf("failed to remove role from user: %w", err)
	}

	return nil
}

// GrantDatasetAccess 授予用户数据集访问权限
func (r *PermissionRepositoryImpl) GrantDatasetAccess(ctx context.Context, userID uuid.UUID, datasetID, access string) error {
	query := `
		INSERT INTO dataset_permissions (user_id, dataset_id, access)
		VALUES ($1, $2, $3)
		ON CONFLICT (user_id, dataset_id) 
		DO UPDATE SET access = EXCLUDED.access, updated_at = CURRENT_TIMESTAMP
	`

	_, err := r.db.ExecContext(ctx, query, userID, datasetID, access)
	if err != nil {
		return fmt.Errorf("failed to grant dataset access: %w", err)
	}

	return nil
}

// RevokeDatasetAccess 撤销用户数据集访问权限
func (r *PermissionRepositoryImpl) RevokeDatasetAccess(ctx context.Context, userID uuid.UUID, datasetID string) error {
	query := `DELETE FROM dataset_permissions WHERE user_id = $1 AND dataset_id = $2`

	_, err := r.db.ExecContext(ctx, query, userID, datasetID)
	if err != nil {
		return fmt.Errorf("failed to revoke dataset access: %w", err)
	}

	return nil
}

// GetAllRoles 获取所有角色
func (r *PermissionRepositoryImpl) GetAllRoles(ctx context.Context) ([]model.Role, error) {
	query := `
		SELECT id, name, code, description, level, is_system, created_at, updated_at
		FROM roles
		ORDER BY level ASC
	`

	var roles []model.Role
	err := r.db.SelectContext(ctx, &roles, query)
	if err != nil {
		return nil, fmt.Errorf("failed to get all roles: %w", err)
	}

	return roles, nil
}

// GetAllPermissions 获取所有权限
func (r *PermissionRepositoryImpl) GetAllPermissions(ctx context.Context) ([]model.Permission, error) {
	query := `
		SELECT id, name, code, description, resource, action, created_at, updated_at
		FROM permissions
		ORDER BY resource, action
	`

	var permissions []model.Permission
	err := r.db.SelectContext(ctx, &permissions, query)
	if err != nil {
		return nil, fmt.Errorf("failed to get all permissions: %w", err)
	}

	return permissions, nil
}

// GetRoleByCode 根据代码获取角色
func (r *PermissionRepositoryImpl) GetRoleByCode(ctx context.Context, code string) (*model.Role, error) {
	query := `
		SELECT id, name, code, description, level, is_system, created_at, updated_at
		FROM roles
		WHERE code = $1
	`

	var role model.Role
	err := r.db.GetContext(ctx, &role, query, code)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get role by code: %w", err)
	}

	return &role, nil
}

// GetPermissionByCode 根据代码获取权限
func (r *PermissionRepositoryImpl) GetPermissionByCode(ctx context.Context, code string) (*model.Permission, error) {
	query := `
		SELECT id, name, code, description, resource, action, created_at, updated_at
		FROM permissions
		WHERE code = $1
	`

	var permission model.Permission
	err := r.db.GetContext(ctx, &permission, query, code)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get permission by code: %w", err)
	}

	return &permission, nil
}
