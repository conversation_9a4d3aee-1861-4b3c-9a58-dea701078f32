package service

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"

	"github.com/chungzy/medical-data-platform/internal/model"
	"github.com/chungzy/medical-data-platform/internal/repository"
)

type HistoryService struct {
	queryRepo    repository.QueryRepository
	templateRepo repository.TemplateRepository
}

func NewHistoryService(queryRepo repository.QueryRepository, templateRepo repository.TemplateRepository) *HistoryService {
	return &HistoryService{
		queryRepo:    queryRepo,
		templateRepo: templateRepo,
	}
}

// GetUserQueryHistory 获取用户查询历史
func (s *HistoryService) GetUserQueryHistory(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.QueryHistory, int, error) {
	return s.queryRepo.GetQueryHistory(ctx, userID, limit, offset)
}

// SaveAsTemplate 将查询历史保存为模板
func (s *HistoryService) SaveAsTemplate(ctx context.Context, queryID uuid.UUID, userID uuid.UUID, req *model.SaveAsTemplateRequest) (*model.QueryTemplate, error) {
	// 获取查询历史
	queryHistory, err := s.queryRepo.GetQueryHistoryByID(ctx, queryID)
	if err != nil {
		return nil, err
	}

	// 检查权限
	if queryHistory.UserID != userID {
		return nil, fmt.Errorf("permission denied")
	}

	// 创建模板
	template := &model.QueryTemplate{
		ID:             uuid.New(),
		UserID:         userID,
		Name:           req.Name,
		Description:    req.Description,
		DatasetID:      queryHistory.DatasetID,
		TemplateConfig: queryHistory.QueryConfig,
		IsPublic:       req.IsPublic,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	// 保存模板
	if err := s.templateRepo.Create(ctx, template); err != nil {
		return nil, err
	}

	return template, nil
}

// DeleteHistoryItem 删除历史记录
func (s *HistoryService) DeleteHistoryItem(ctx context.Context, queryID uuid.UUID, userID uuid.UUID) error {
	// 获取查询历史
	queryHistory, err := s.queryRepo.GetQueryHistoryByID(ctx, queryID)
	if err != nil {
		return err
	}

	// 检查权限
	if queryHistory.UserID != userID {
		return fmt.Errorf("permission denied")
	}

	// 删除历史记录
	return s.queryRepo.DeleteQueryHistory(ctx, queryID)
}
