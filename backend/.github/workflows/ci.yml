# Medical Data Platform Server CI/CD Pipeline
# 医疗数据平台服务器持续集成/持续部署流水线

name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
    tags: [ 'v*' ]
  pull_request:
    branches: [ main, develop ]
  release:
    types: [published]

env:
  GO_VERSION: '1.21'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # 代码检查
  lint:
    name: Lint
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}

    - name: Cache Go modules
      uses: actions/cache@v3
      with:
        path: |
          ~/.cache/go-build
          ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-

    - name: Download dependencies
      run: go mod download

   # - name: Run golangci-lint
   #   uses: golangci/golangci-lint-action@v3
   #   with:
   #     version: latest
   #     args: --timeout=5m

   # - name: Run go vet
   #   run: go vet ./...

   # - name: Run go fmt check
   #   run: |
   #    if [ "$(gofmt -s -l . | wc -l)" -gt 0 ]; then
   #     echo "Code is not formatted properly:"
   #     gofmt -s -l .
   #     exit 1
   #   fi

  # 单元测试
  test:
    name: Test
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}

    - name: Cache Go modules
      uses: actions/cache@v3
      with:
        path: |
          ~/.cache/go-build
          ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-

    - name: Download dependencies
      run: go mod download

    - name: Run tests
      env:
        DATABASE_URL: postgres://postgres:postgres@localhost:5432/test_db?sslmode=disable
        JWT_SECRET: test-secret-key
        LOG_LEVEL: error
      run: |
        go test -v -race -coverprofile=coverage.out ./...
        go tool cover -func=coverage.out

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.out
        flags: unittests
        name: codecov-umbrella

  # 构建测试
  build:
    name: Build
    runs-on: ubuntu-latest
    needs: [lint, test]
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}

    - name: Cache Go modules
      uses: actions/cache@v3
      with:
        path: |
          ~/.cache/go-build
          ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-

    - name: Download dependencies
      run: go mod download

    - name: Build application
      run: |
        VERSION=$(git describe --tags --always --dirty 2>/dev/null || echo "dev")
        BUILD_TIME=$(date -u '+%Y-%m-%d_%H:%M:%S')
        GIT_COMMIT=$(git rev-parse --short HEAD)
        
        go build -ldflags "-X main.Version=$VERSION -X main.BuildTime=$BUILD_TIME -X main.GitCommit=$GIT_COMMIT" \
          -o bin/server cmd/server/main.go

    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: server-binary
        path: bin/server

  # 安全扫描
  security:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: [lint]
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}

    - name: Run Gosec Security Scanner
      uses: securecodewarrior/github-action-gosec@master
      with:
        args: '-fmt sarif -out gosec.sarif ./...'

    - name: Upload SARIF file
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: gosec.sarif

  # Docker构建和推送
  docker:
    name: Docker Build & Push
    runs-on: ubuntu-latest
    needs: [build]
    if: github.event_name == 'push' || github.event_name == 'release'
    permissions:
      contents: read
      packages: write

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=sha,prefix={{branch}}-

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        platforms: linux/amd64,linux/arm64
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # 创建部署包（不包含源代码）
  create-deployment-package:
    name: Create Deployment Package
    runs-on: ubuntu-latest
    needs: [docker]
    if: github.event_name == 'release' || github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        sparse-checkout: |
          docker-compose.prod.yml
          nginx/
          scripts/
          configs/
          migrations/
          .env.example
        sparse-checkout-cone-mode: false

    - name: Extract version
      id: version
      run: |
        if [[ "${{ github.event_name }}" == "release" ]]; then
          VERSION=${{ github.event.release.tag_name }}
        else
          VERSION=$(git rev-parse --short HEAD)
        fi
        echo "version=$VERSION" >> $GITHUB_OUTPUT

    - name: Create deployment package
      run: |
        # 创建部署目录
        mkdir -p deployment-package
        
        # 复制必要的部署文件（不包含源代码）
        cp docker-compose.prod.yml deployment-package/
        cp -r nginx deployment-package/
        cp -r scripts deployment-package/
        cp -r configs deployment-package/
        cp -r migrations deployment-package/
        cp .env.example deployment-package/.env.template
        
        # 创建部署说明文档
        cat > deployment-package/README.md << 'EOF'
        # Medical Data Platform Server - 部署包
        
        ## 版本信息
        - 版本: ${{ steps.version.outputs.version }}
        - 构建时间: $(date -u '+%Y-%m-%d %H:%M:%S UTC')
        - Git提交: ${{ github.sha }}
        
        ## 部署说明
        
        ### 1. 环境准备
        ```bash
        # 安装Docker和Docker Compose
        curl -fsSL https://get.docker.com -o get-docker.sh
        sh get-docker.sh
        
        # 创建部署目录
        sudo mkdir -p /opt/medical-platform
        sudo chown $USER:$USER /opt/medical-platform
        ```
        
        ### 2. 配置环境变量
        ```bash
        # 复制环境变量模板
        cp .env.template .env
        
        # 编辑环境变量（必须设置）
        vi .env
        ```
        
        必须配置的环境变量：
        - `DB_PASSWORD`: 数据库密码
        - `JWT_SECRET`: JWT密钥
        - `REDIS_PASSWORD`: Redis密码（可选）
        
        ### 3. 启动服务
        ```bash
        # 启动生产环境
        docker-compose -f docker-compose.prod.yml up -d
        
        # 查看服务状态
        docker-compose -f docker-compose.prod.yml ps
        
        # 查看日志
        docker-compose -f docker-compose.prod.yml logs -f
        ```
        
        ### 4. 管理脚本
        ```bash
        # 停止服务
        ./scripts/stop.sh -p
        
        # 重启服务
        ./scripts/restart.sh -p
        
        # 数据库备份
        ./scripts/backup.sh
        ```
        
        ### 5. 健康检查
        ```bash
        curl http://localhost/health
        ```
        
        ## 目录结构
        ```
        deployment-package/
        ├── docker-compose.prod.yml    # 生产环境Docker配置
        ├── nginx/                     # Nginx配置文件
        ├── scripts/                   # 管理脚本
        ├── configs/                   # 应用配置模板
        ├── migrations/                # 数据库迁移文件
        ├── .env.template             # 环境变量模板
        └── README.md                 # 部署说明
        ```
        
        ## 注意事项
        1. 请确保服务器有足够的资源（至少2GB内存）
        2. 生产环境建议使用SSL证书，请将证书放在nginx/ssl/目录下
        3. 定期备份数据库和配置文件
        4. 监控服务状态和日志
        EOF
        
        # 创建环境变量模板
        cat > deployment-package/.env << 'EOF'
        # 医疗数据平台生产环境配置
        
        # Docker镜像配置
        DOCKER_REGISTRY=ghcr.io
        DOCKER_IMAGE=medical-data-platform/server
        VERSION=${{ steps.version.outputs.version }}
        
        # 数据库配置
        DB_USER=medical_user
        DB_PASSWORD=CHANGE_ME_STRONG_PASSWORD
        DB_NAME=medical_data_platform
        
        # JWT配置
        JWT_SECRET=CHANGE_ME_STRONG_JWT_SECRET
        
        # Redis配置（可选）
        REDIS_PASSWORD=CHANGE_ME_REDIS_PASSWORD
        
        # 备份配置
        BACKUP_SCHEDULE=0 2 * * *
        BACKUP_RETENTION_DAYS=30
        
        # 数据目录
        DATA_DIR=/opt/medical-platform/data
        LOG_DIR=/opt/medical-platform/logs
        BACKUP_DIR=/opt/medical-platform/backups
        CONFIG_DIR=/opt/medical-platform/configs
        EXPORT_DIR=/opt/medical-platform/exports
        EOF
        
        # 创建快速部署脚本
        cat > deployment-package/deploy.sh << 'EOF'
        #!/bin/bash
        set -e
        
        echo "医疗数据平台快速部署脚本"
        echo "=========================="
        
        # 检查Docker
        if ! command -v docker >/dev/null 2>&1; then
            echo "错误: Docker未安装"
            exit 1
        fi
        
        if ! command -v docker-compose >/dev/null 2>&1; then
            echo "错误: Docker Compose未安装"
            exit 1
        fi
        
        # 检查环境变量文件
        if [[ ! -f .env ]]; then
            echo "警告: .env文件不存在，使用模板创建"
            cp .env.template .env
            echo "请编辑.env文件设置必要的环境变量，然后重新运行此脚本"
            exit 1
        fi
        
        # 创建数据目录
        source .env
        sudo mkdir -p "$DATA_DIR" "$LOG_DIR" "$BACKUP_DIR" "$CONFIG_DIR" "$EXPORT_DIR"
        sudo chown -R $USER:$USER "$DATA_DIR" "$LOG_DIR" "$BACKUP_DIR" "$CONFIG_DIR" "$EXPORT_DIR"
        
        # 启动服务
        echo "启动医疗数据平台服务..."
        docker-compose -f docker-compose.prod.yml up -d
        
        echo "部署完成！"
        echo "健康检查: curl http://localhost/health"
        echo "查看日志: docker-compose -f docker-compose.prod.yml logs -f"
        EOF
        
        chmod +x deployment-package/deploy.sh
        chmod +x deployment-package/scripts/*.sh

    - name: Create deployment archive
      run: |
        tar -czf medical-platform-deployment-${{ steps.version.outputs.version }}.tar.gz deployment-package/

    - name: Upload deployment package
      uses: actions/upload-artifact@v3
      with:
        name: deployment-package-${{ steps.version.outputs.version }}
        path: medical-platform-deployment-${{ steps.version.outputs.version }}.tar.gz

    - name: Release deployment package
      if: github.event_name == 'release'
      uses: softprops/action-gh-release@v1
      with:
        files: medical-platform-deployment-${{ steps.version.outputs.version }}.tar.gz
        name: 医疗数据平台 ${{ steps.version.outputs.version }}
        body: |
          ## 部署包说明
          
          此部署包包含生产环境部署所需的所有文件，**不包含源代码**。
          
          ### 包含内容
          - Docker Compose生产环境配置
          - Nginx反向代理配置
          - 数据库备份和管理脚本
          - 配置文件模板
          - 数据库迁移文件
          - 快速部署脚本
          
          ### 快速部署
          ```bash
          # 下载并解压部署包
          wget https://github.com/${{ github.repository }}/releases/download/${{ steps.version.outputs.version }}/medical-platform-deployment-${{ steps.version.outputs.version }}.tar.gz
          tar -xzf medical-platform-deployment-${{ steps.version.outputs.version }}.tar.gz
          cd deployment-package
          
          # 配置环境变量
          cp .env.template .env
          vi .env  # 设置必要的环境变量
          
          # 快速部署
          ./deploy.sh
          ```
          
          详细部署说明请参考包内的README.md文件。

  # 部署到开发环境
  deploy-dev:
    name: Deploy to Development
    runs-on: ubuntu-latest
    needs: [docker]
    if: github.ref == 'refs/heads/develop'
    environment: development
    
    steps:
    - name: Deploy to development
      run: |
        echo "Deploying to development environment..."
        # 这里添加实际的部署逻辑
        # 例如：kubectl apply -f k8s/dev/ 或者调用部署API

  # 部署到生产环境
  deploy-prod:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [create-deployment-package]
    if: github.event_name == 'release'
    environment: production
    
    steps:
    - name: Deploy to production
      run: |
        echo "Deploying to production environment..."
        # 这里添加实际的部署逻辑
        # 例如：使用部署包进行自动化部署

  # 性能测试
  performance:
    name: Performance Test
    runs-on: ubuntu-latest
    needs: [build]
    if: github.event_name == 'pull_request'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Download build artifacts
      uses: actions/download-artifact@v3
      with:
        name: server-binary
        path: bin/

    - name: Make binary executable
      run: chmod +x bin/server

    - name: Start application
      run: |
        # 设置测试环境变量
        export DATABASE_URL="sqlite://test.db"
        export JWT_SECRET="test-secret"
        export LOG_LEVEL="error"
        export GIN_MODE="release"
        
        # 启动应用（后台运行）
        ./bin/server &
        APP_PID=$!
        
        # 等待应用启动
        sleep 5
        
        # 健康检查
        curl -f http://localhost:8080/health || exit 1
        
        # 简单的性能测试
        echo "Running basic performance tests..."
        
        # 停止应用
        kill $APP_PID

  # 通知
  notify:
    name: Notify
    runs-on: ubuntu-latest
    needs: [lint, test, build, security]
    if: always()
    
    steps:
    - name: Notify on success
      if: ${{ needs.lint.result == 'success' && needs.test.result == 'success' && needs.build.result == 'success' }}
      run: |
        echo "✅ All checks passed successfully!"
        
    - name: Notify on failure
      if: ${{ needs.lint.result == 'failure' || needs.test.result == 'failure' || needs.build.result == 'failure' || needs.security.result == 'failure' }}
      run: |
        echo "❌ Some checks failed. Please review the logs."
        exit 1 