package service

import (
	"context"
	"fmt"

	"github.com/chungzy/medical-data-platform/internal/model"
	"github.com/chungzy/medical-data-platform/internal/repository"
	"github.com/chungzy/medical-data-platform/pkg/logger"
	"github.com/google/uuid"
)

type SimplePermissionService struct {
	userRepo repository.UserRepository
}

func NewSimplePermissionService(userRepo repository.UserRepository) *SimplePermissionService {
	return &SimplePermissionService{
		userRepo: userRepo,
	}
}

// CheckPermission 检查用户权限
func (s *SimplePermissionService) CheckPermission(ctx context.Context, userID uuid.UUID, action string) (bool, error) {
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return false, fmt.Errorf("failed to get user: %w", err)
	}

	if user == nil {
		return false, fmt.Errorf("user not found")
	}

	logger.Info("Checking permission", map[string]interface{}{
		"user_id":    userID,
		"permission": user.Permission,
		"action":     action,
	})

	switch action {
	case "admin":
		// 只有管理员可以执行管理操作
		return user.Permission == model.PermissionAdmin, nil
	case "query", "read", "export":
		// 管理员和普通用户都可以执行基础操作
		return user.Permission == model.PermissionAdmin || user.Permission == model.PermissionUser, nil
	case "user_management":
		// 只有管理员可以管理用户
		return user.Permission == model.PermissionAdmin, nil
	default:
		// 默认需要至少是普通用户权限
		return user.Permission >= model.PermissionUser, nil
	}
}

// IsAdmin 检查是否为管理员
func (s *SimplePermissionService) IsAdmin(ctx context.Context, userID uuid.UUID) (bool, error) {
	return s.CheckPermission(ctx, userID, "admin")
}

// HasAccess 检查是否有基础访问权限
func (s *SimplePermissionService) HasAccess(ctx context.Context, userID uuid.UUID) (bool, error) {
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return false, err
	}

	if user == nil {
		return false, fmt.Errorf("user not found")
	}

	return user.Permission >= model.PermissionUser, nil
}

// GetUserPermissionLevel 获取用户权限级别
func (s *SimplePermissionService) GetUserPermissionLevel(ctx context.Context, userID uuid.UUID) (int, error) {
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return 0, err
	}

	if user == nil {
		return 0, fmt.Errorf("user not found")
	}

	return user.Permission, nil
}

// SetUserPermission 设置用户权限
func (s *SimplePermissionService) SetUserPermission(ctx context.Context, userID uuid.UUID, permission int) error {
	if permission < model.PermissionNone || permission > model.PermissionUser {
		return fmt.Errorf("invalid permission level: %d", permission)
	}

	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return err
	}

	if user == nil {
		return fmt.Errorf("user not found")
	}

	user.Permission = permission
	
	// 同时更新role字段以保持一致性
	switch permission {
	case model.PermissionAdmin:
		user.Role = "admin"
	case model.PermissionUser:
		user.Role = "user"
	default:
		user.Role = "user" // 默认为普通用户
	}

	return s.userRepo.Update(ctx, user)
}

// GetPermissionName 获取权限名称
func GetPermissionName(permission int) string {
	switch permission {
	case model.PermissionNone:
		return "无权限"
	case model.PermissionAdmin:
		return "管理员"
	case model.PermissionUser:
		return "普通用户"
	default:
		return "未知权限"
	}
}

// CanAccessDataset 检查数据集访问权限
func (s *SimplePermissionService) CanAccessDataset(ctx context.Context, userID uuid.UUID, datasetID string) (bool, error) {
	// 简化版本：有基础权限的用户都可以访问数据集
	return s.HasAccess(ctx, userID)
}

// CanExecuteQuery 检查查询执行权限
func (s *SimplePermissionService) CanExecuteQuery(ctx context.Context, userID uuid.UUID) (bool, error) {
	return s.CheckPermission(ctx, userID, "query")
}

// CanManageUsers 检查用户管理权限
func (s *SimplePermissionService) CanManageUsers(ctx context.Context, userID uuid.UUID) (bool, error) {
	return s.CheckPermission(ctx, userID, "user_management")
}

// CanExportData 检查数据导出权限
func (s *SimplePermissionService) CanExportData(ctx context.Context, userID uuid.UUID) (bool, error) {
	return s.CheckPermission(ctx, userID, "export")
}
