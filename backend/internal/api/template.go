package api

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"

	"github.com/chungzy/medical-data-platform/internal/model"
	"github.com/chungzy/medical-data-platform/internal/service"
	"github.com/chungzy/medical-data-platform/pkg/logger"
)

type TemplateController struct {
	templateService *service.TemplateService
}

func NewTemplateController(templateService *service.TemplateService) *TemplateController {
	return &TemplateController{
		templateService: templateService,
	}
}

// GetTemplates 获取查询模板列表
func (tc *TemplateController) GetTemplates(c *gin.Context) {
	c.Set("business_operation", "get_templates")

	logger.InfoWithGinContext(c, "Getting templates list")

	userID, exists := c.Get("user_id")
	if !exists {
		logger.WarnWithGinContext(c, "User ID not found in context")
		c.<PERSON>(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// 获取查询参数
	category := c.Query("category")
	isPublic := c.Query("public") == "true"
	search := c.Query("search")

	logger.InfoWithGinContext(c, "Template query parameters parsed", map[string]interface{}{
		"user_id":   userID,
		"category":  category,
		"is_public": isPublic,
		"search":    search,
	})

	templates, err := tc.templateService.GetTemplates(c.Request.Context(), userID.(uuid.UUID), category, isPublic, search)
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to get templates", map[string]interface{}{
			"user_id":   userID,
			"category":  category,
			"is_public": isPublic,
			"search":    search,
		})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get templates", "details": err.Error()})
		return
	}

	logger.InfoWithGinContext(c, "Templates retrieved successfully", map[string]interface{}{
		"user_id":         userID,
		"templates_count": len(templates),
	})

	c.JSON(http.StatusOK, gin.H{
		"templates": templates,
		"total":     len(templates),
	})
}

// CreateTemplate 创建查询模板
func (tc *TemplateController) CreateTemplate(c *gin.Context) {
	c.Set("business_operation", "create_template")

	logger.InfoWithGinContext(c, "Creating new template")

	userID, exists := c.Get("user_id")
	if !exists {
		logger.WarnWithGinContext(c, "User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req model.CreateTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to parse create template request")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format", "details": err.Error()})
		return
	}

	logger.InfoWithGinContext(c, "Create template request parsed", map[string]interface{}{
		"user_id":    userID,
		"name":       req.Name,
		"category":   req.Category,
		"dataset_id": req.DatasetID,
		"is_public":  req.IsPublic,
	})

	template := &model.QueryTemplate{
		ID:             uuid.New(),
		UserID:         userID.(uuid.UUID),
		Name:           req.Name,
		Description:    req.Description,
		Category:       req.Category,
		DatasetID:      req.DatasetID,
		TemplateConfig: req.TemplateConfig,
		IsPublic:       req.IsPublic,
		Author:         req.Author,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	if err := tc.templateService.Create(c.Request.Context(), template); err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to create template", map[string]interface{}{
			"user_id":     userID,
			"template_id": template.ID,
			"name":        template.Name,
		})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create template", "details": err.Error()})
		return
	}

	logger.InfoWithGinContext(c, "Template created successfully", map[string]interface{}{
		"user_id":     userID,
		"template_id": template.ID,
		"name":        template.Name,
	})

	c.JSON(http.StatusCreated, template)
}

// GetTemplateByID 获取模板详情
func (tc *TemplateController) GetTemplateByID(c *gin.Context) {
	c.Set("business_operation", "get_template_by_id")

	logger.InfoWithGinContext(c, "Getting template by ID")

	templateID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Invalid template ID format", map[string]interface{}{
			"template_id_param": c.Param("id"),
		})
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid template ID"})
		return
	}

	logger.InfoWithGinContext(c, "Template ID parsed", map[string]interface{}{
		"template_id": templateID,
	})

	template, err := tc.templateService.GetByID(c.Request.Context(), templateID)
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Template not found", map[string]interface{}{
			"template_id": templateID,
		})
		c.JSON(http.StatusNotFound, gin.H{"error": "Template not found"})
		return
	}

	logger.InfoWithGinContext(c, "Template retrieved successfully", map[string]interface{}{
		"template_id": templateID,
		"name":        template.Name,
		"user_id":     template.UserID,
	})

	c.JSON(http.StatusOK, template)
}

// UpdateTemplate 更新模板
func (tc *TemplateController) UpdateTemplate(c *gin.Context) {
	c.Set("business_operation", "update_template")

	logger.InfoWithGinContext(c, "Updating template")

	userID, exists := c.Get("user_id")
	if !exists {
		logger.WarnWithGinContext(c, "User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	templateID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Invalid template ID format", map[string]interface{}{
			"template_id_param": c.Param("id"),
		})
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid template ID"})
		return
	}

	var req model.UpdateTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to parse update template request")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format", "details": err.Error()})
		return
	}

	logger.InfoWithGinContext(c, "Update template request parsed", map[string]interface{}{
		"user_id":     userID,
		"template_id": templateID,
		"name":        req.Name,
		"category":    req.Category,
	})

	// 检查模板是否存在且用户有权限修改
	template, err := tc.templateService.GetByID(c.Request.Context(), templateID)
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Template not found for update", map[string]interface{}{
			"template_id": templateID,
			"user_id":     userID,
		})
		c.JSON(http.StatusNotFound, gin.H{"error": "Template not found"})
		return
	}

	if template.UserID != userID.(uuid.UUID) {
		logger.WarnWithGinContext(c, "Permission denied for template update", map[string]interface{}{
			"template_id":    templateID,
			"user_id":        userID,
			"template_owner": template.UserID,
		})
		c.JSON(http.StatusForbidden, gin.H{"error": "Permission denied"})
		return
	}

	// 更新模板
	template.Name = req.Name
	template.Description = req.Description
	template.Category = req.Category
	template.TemplateConfig = req.TemplateConfig
	template.IsPublic = req.IsPublic
	template.UpdatedAt = time.Now()

	if err := tc.templateService.Update(c.Request.Context(), template); err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to update template", map[string]interface{}{
			"template_id": templateID,
			"user_id":     userID,
			"name":        template.Name,
		})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update template", "details": err.Error()})
		return
	}

	logger.InfoWithGinContext(c, "Template updated successfully", map[string]interface{}{
		"template_id": templateID,
		"user_id":     userID,
		"name":        template.Name,
	})

	c.JSON(http.StatusOK, template)
}

// DeleteTemplate 删除模板
func (tc *TemplateController) DeleteTemplate(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	templateID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid template ID"})
		return
	}

	// 检查模板是否存在且用户有权限删除
	template, err := tc.templateService.GetByID(c.Request.Context(), templateID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Template not found"})
		return
	}

	if template.UserID != userID.(uuid.UUID) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Permission denied"})
		return
	}

	if err := tc.templateService.Delete(c.Request.Context(), templateID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete template", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Template deleted successfully"})
}

// UseTemplate 使用模板创建查询
func (tc *TemplateController) UseTemplate(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	templateID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid template ID"})
		return
	}

	var req model.UseTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format", "details": err.Error()})
		return
	}

	query, err := tc.templateService.UseTemplate(c.Request.Context(), templateID, userID.(uuid.UUID), req.Parameters)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to use template", "details": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, query)
}
