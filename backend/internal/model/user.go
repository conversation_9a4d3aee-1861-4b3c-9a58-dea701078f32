package model

import (
	"time"

	"github.com/google/uuid"
)

// 用户权限级别常量
const (
	PermissionNone  = 0 // 无权限
	PermissionAdmin = 1 // 管理员
	PermissionUser  = 2 // 普通用户
)

type User struct {
	ID                  uuid.UUID  `json:"id" db:"id"`
	Username            string     `json:"username" db:"username"`
	Name                string     `json:"name" db:"name" validate:"required,min=2,max=100"`
	Email               string     `json:"email" db:"email" validate:"required,email"`
	Phone               *string    `json:"phone,omitempty" db:"phone"`
	Password            string     `json:"-" db:"password_hash"`
	PasswordHash        string     `json:"-" db:"password_hash"`
	Role                string     `json:"role" db:"role" validate:"required,oneof=user admin"`
	Permission          int        `json:"permission" db:"permission"` // 0:无权限, 1:管理员, 2:普通用户
	IsActive            bool       `json:"is_active" db:"is_active"`
	PasswordUpdatedAt   *time.Time `json:"password_updated_at,omitempty" db:"password_updated_at"`
	LastLoginAt         *time.Time `json:"last_login_at,omitempty" db:"last_login_at"`
	FailedLoginAttempts int        `json:"failed_login_attempts" db:"failed_login_attempts"`
	LockedUntil         *time.Time `json:"locked_until,omitempty" db:"locked_until"`
	CreatedAt           time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt           time.Time  `json:"updated_at" db:"updated_at"`
}

type RegisterRequest = UserRegistrationRequest
type LoginRequest = UserLoginRequest
type LoginResponse = AuthResponse

type UserRegistrationRequest struct {
	Username string  `json:"username,omitempty"`
	Name     string  `json:"name" validate:"required,min=2,max=100"`
	Email    string  `json:"email" validate:"required,email"`
	Phone    *string `json:"phone,omitempty"`
	Password string  `json:"password" validate:"required,min=6"`
	Role     string  `json:"role" validate:"required,oneof=user admin"`
}

// CreateUserRequest 创建用户请求（管理员使用）
type CreateUserRequest struct {
	Name     string `json:"name" validate:"required,min=2,max=100"`
	Email    string `json:"email" validate:"required,email"`
	Phone    string `json:"phone,omitempty"`
	Password string `json:"password" validate:"required,min=6"`
	Role     string `json:"role" validate:"required,oneof=user admin"`
}

type UserLoginRequest struct {
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required"`
}

type UserResponse struct {
	ID        uuid.UUID `json:"id"`
	Username  string    `json:"username,omitempty"`
	Name      string    `json:"name"`
	Email     string    `json:"email"`
	Phone     *string   `json:"phone,omitempty"`
	Role      string    `json:"role"`
	CreatedAt time.Time `json:"created_at"`
}

type UserInfo = UserResponse

type AuthResponse struct {
	Token string       `json:"token"`
	User  UserResponse `json:"user"`
}
