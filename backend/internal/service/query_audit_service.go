package service

import (
	"context"
	"encoding/json"
	"time"

	"github.com/chungzy/medical-data-platform/internal/repository"
	"github.com/chungzy/medical-data-platform/pkg/logger"
	"github.com/google/uuid"
)

// QueryAuditService 查询审计服务
type QueryAuditService struct {
	queryRepo repository.QueryRepository
}

// QueryAuditLog 查询审计日志
type QueryAuditLog struct {
	ID            string        `json:"id"`
	QueryID       string        `json:"query_id"`
	UserID        string        `json:"user_id"`
	Action        string        `json:"action"`
	Status        string        `json:"status"`
	ErrorMessage  string        `json:"error_message,omitempty"`
	ExecutionTime time.Duration `json:"execution_time"`
	RowsReturned  int           `json:"rows_returned"`
	TablesUsed    []string      `json:"tables_used"`
	FieldsUsed    []string      `json:"fields_used"`
	IPAddress     string        `json:"ip_address,omitempty"`
	UserAgent     string        `json:"user_agent,omitempty"`
	Timestamp     time.Time     `json:"timestamp"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
}

// AuditSummary 审计摘要
type AuditSummary struct {
	TotalActions      int                    `json:"total_actions"`
	ActionsByType     map[string]int         `json:"actions_by_type"`
	ActionsByStatus   map[string]int         `json:"actions_by_status"`
	ActionsByUser     map[string]int         `json:"actions_by_user"`
	TablesAccessed    map[string]int         `json:"tables_accessed"`
	FieldsAccessed    map[string]int         `json:"fields_accessed"`
	TotalExecutionTime time.Duration         `json:"total_execution_time"`
	AvgExecutionTime   time.Duration         `json:"avg_execution_time"`
	TotalRowsReturned  int64                 `json:"total_rows_returned"`
	DateRange         DateRange             `json:"date_range"`
}

// DateRange 日期范围
type DateRange struct {
	StartDate time.Time `json:"start_date"`
	EndDate   time.Time `json:"end_date"`
}

// NewQueryAuditService 创建查询审计服务
func NewQueryAuditService(queryRepo repository.QueryRepository) *QueryAuditService {
	return &QueryAuditService{
		queryRepo: queryRepo,
	}
}

// LogQueryExecution 记录查询执行审计日志
func (qas *QueryAuditService) LogQueryExecution(ctx context.Context, auditLog *QueryAuditLog) error {
	// 生成审计日志ID
	if auditLog.ID == "" {
		auditLog.ID = uuid.New().String()
	}

	// 设置时间戳
	if auditLog.Timestamp.IsZero() {
		auditLog.Timestamp = time.Now()
	}

	// 序列化审计日志
	auditData, err := json.Marshal(auditLog)
	if err != nil {
		logger.Error("Failed to marshal audit log", err, map[string]interface{}{
			"audit_id": auditLog.ID,
			"query_id": auditLog.QueryID,
			"user_id":  auditLog.UserID,
		})
		return err
	}

	// 这里可以将审计日志存储到专门的审计表或外部审计系统
	// 目前使用日志记录
	logger.Info("Query audit log", map[string]interface{}{
		"audit_id":       auditLog.ID,
		"query_id":       auditLog.QueryID,
		"user_id":        auditLog.UserID,
		"action":         auditLog.Action,
		"status":         auditLog.Status,
		"execution_time": auditLog.ExecutionTime,
		"rows_returned":  auditLog.RowsReturned,
		"tables_used":    auditLog.TablesUsed,
		"fields_used":    auditLog.FieldsUsed,
		"timestamp":      auditLog.Timestamp,
		"audit_data":     string(auditData),
	})

	return nil
}

// LogDataAccess 记录数据访问审计日志
func (qas *QueryAuditService) LogDataAccess(ctx context.Context, userID, datasetID, tableName string, accessType string, metadata map[string]interface{}) error {
	auditLog := &QueryAuditLog{
		ID:        uuid.New().String(),
		UserID:    userID,
		Action:    "data_access",
		Status:    "success",
		Timestamp: time.Now(),
		Metadata:  metadata,
	}

	// 添加数据访问特定信息
	if auditLog.Metadata == nil {
		auditLog.Metadata = make(map[string]interface{})
	}
	auditLog.Metadata["dataset_id"] = datasetID
	auditLog.Metadata["table_name"] = tableName
	auditLog.Metadata["access_type"] = accessType

	return qas.LogQueryExecution(ctx, auditLog)
}

// LogUserAction 记录用户操作审计日志
func (qas *QueryAuditService) LogUserAction(ctx context.Context, userID, action string, metadata map[string]interface{}) error {
	auditLog := &QueryAuditLog{
		ID:        uuid.New().String(),
		UserID:    userID,
		Action:    action,
		Status:    "success",
		Timestamp: time.Now(),
		Metadata:  metadata,
	}

	return qas.LogQueryExecution(ctx, auditLog)
}

// LogSecurityEvent 记录安全事件审计日志
func (qas *QueryAuditService) LogSecurityEvent(ctx context.Context, userID, eventType, description string, severity string, metadata map[string]interface{}) error {
	auditLog := &QueryAuditLog{
		ID:        uuid.New().String(),
		UserID:    userID,
		Action:    "security_event",
		Status:    severity,
		Timestamp: time.Now(),
		Metadata:  metadata,
	}

	// 添加安全事件特定信息
	if auditLog.Metadata == nil {
		auditLog.Metadata = make(map[string]interface{})
	}
	auditLog.Metadata["event_type"] = eventType
	auditLog.Metadata["description"] = description
	auditLog.Metadata["severity"] = severity

	// 安全事件使用更高级别的日志
	logger.Warn("Security event", map[string]interface{}{
		"audit_id":    auditLog.ID,
		"user_id":     userID,
		"event_type":  eventType,
		"description": description,
		"severity":    severity,
		"timestamp":   auditLog.Timestamp,
		"metadata":    metadata,
	})

	return qas.LogQueryExecution(ctx, auditLog)
}

// GetAuditLogs 获取审计日志
func (qas *QueryAuditService) GetAuditLogs(ctx context.Context, filters AuditLogFilters) ([]*QueryAuditLog, error) {
	// 这里应该从审计日志存储中查询
	// 目前返回空列表，实际实现需要根据存储方式调整
	return []*QueryAuditLog{}, nil
}

// AuditLogFilters 审计日志过滤器
type AuditLogFilters struct {
	UserID     string     `json:"user_id,omitempty"`
	QueryID    string     `json:"query_id,omitempty"`
	Action     string     `json:"action,omitempty"`
	Status     string     `json:"status,omitempty"`
	StartDate  *time.Time `json:"start_date,omitempty"`
	EndDate    *time.Time `json:"end_date,omitempty"`
	Limit      int        `json:"limit"`
	Offset     int        `json:"offset"`
}

// GetAuditSummary 获取审计摘要
func (qas *QueryAuditService) GetAuditSummary(ctx context.Context, startDate, endDate time.Time, userID string) (*AuditSummary, error) {
	// 这里应该从审计日志存储中统计
	// 目前返回模拟数据
	summary := &AuditSummary{
		TotalActions: 0,
		ActionsByType: map[string]int{
			"execute_query": 0,
			"cancel_query":  0,
			"data_access":   0,
		},
		ActionsByStatus: map[string]int{
			"success": 0,
			"failed":  0,
		},
		ActionsByUser:     make(map[string]int),
		TablesAccessed:    make(map[string]int),
		FieldsAccessed:    make(map[string]int),
		TotalExecutionTime: 0,
		AvgExecutionTime:   0,
		TotalRowsReturned:  0,
		DateRange: DateRange{
			StartDate: startDate,
			EndDate:   endDate,
		},
	}

	return summary, nil
}

// GetUserActivity 获取用户活动
func (qas *QueryAuditService) GetUserActivity(ctx context.Context, userID string, days int) (*UserActivity, error) {
	// 这里应该从审计日志存储中查询用户活动
	activity := &UserActivity{
		UserID:            userID,
		TotalQueries:      0,
		SuccessfulQueries: 0,
		FailedQueries:     0,
		TotalExecutionTime: 0,
		AvgExecutionTime:   0,
		MostUsedTables:    []TableUsage{},
		MostUsedFields:    []FieldUsage{},
		ActivityByDay:     []DayActivity{},
		LastActivity:      time.Now(),
	}

	return activity, nil
}

// UserActivity 用户活动
type UserActivity struct {
	UserID             string        `json:"user_id"`
	TotalQueries       int           `json:"total_queries"`
	SuccessfulQueries  int           `json:"successful_queries"`
	FailedQueries      int           `json:"failed_queries"`
	TotalExecutionTime time.Duration `json:"total_execution_time"`
	AvgExecutionTime   time.Duration `json:"avg_execution_time"`
	MostUsedTables     []TableUsage  `json:"most_used_tables"`
	MostUsedFields     []FieldUsage  `json:"most_used_fields"`
	ActivityByDay      []DayActivity `json:"activity_by_day"`
	LastActivity       time.Time     `json:"last_activity"`
}

// TableUsage 表使用情况
type TableUsage struct {
	TableName string `json:"table_name"`
	Count     int    `json:"count"`
}

// FieldUsage 字段使用情况
type FieldUsage struct {
	FieldName string `json:"field_name"`
	Count     int    `json:"count"`
}

// DayActivity 日活动
type DayActivity struct {
	Date         time.Time `json:"date"`
	QueryCount   int       `json:"query_count"`
	SuccessCount int       `json:"success_count"`
	FailedCount  int       `json:"failed_count"`
}

// GetComplianceReport 获取合规报告
func (qas *QueryAuditService) GetComplianceReport(ctx context.Context, startDate, endDate time.Time) (*ComplianceReport, error) {
	report := &ComplianceReport{
		ReportPeriod: DateRange{
			StartDate: startDate,
			EndDate:   endDate,
		},
		TotalDataAccess:      0,
		UnauthorizedAttempts: 0,
		DataExportEvents:     0,
		SecurityViolations:   []SecurityViolation{},
		UserAccessSummary:    []UserAccessSummary{},
		TableAccessSummary:   []TableAccessSummary{},
		ComplianceScore:      100.0,
		Recommendations:      []string{},
	}

	return report, nil
}

// ComplianceReport 合规报告
type ComplianceReport struct {
	ReportPeriod         DateRange             `json:"report_period"`
	TotalDataAccess      int                   `json:"total_data_access"`
	UnauthorizedAttempts int                   `json:"unauthorized_attempts"`
	DataExportEvents     int                   `json:"data_export_events"`
	SecurityViolations   []SecurityViolation   `json:"security_violations"`
	UserAccessSummary    []UserAccessSummary   `json:"user_access_summary"`
	TableAccessSummary   []TableAccessSummary  `json:"table_access_summary"`
	ComplianceScore      float64               `json:"compliance_score"`
	Recommendations      []string              `json:"recommendations"`
}

// SecurityViolation 安全违规
type SecurityViolation struct {
	ID          string    `json:"id"`
	UserID      string    `json:"user_id"`
	ViolationType string  `json:"violation_type"`
	Description string    `json:"description"`
	Severity    string    `json:"severity"`
	Timestamp   time.Time `json:"timestamp"`
	Resolved    bool      `json:"resolved"`
}

// UserAccessSummary 用户访问摘要
type UserAccessSummary struct {
	UserID       string `json:"user_id"`
	AccessCount  int    `json:"access_count"`
	TablesAccessed []string `json:"tables_accessed"`
	LastAccess   time.Time `json:"last_access"`
}

// TableAccessSummary 表访问摘要
type TableAccessSummary struct {
	TableName   string `json:"table_name"`
	AccessCount int    `json:"access_count"`
	UniqueUsers int    `json:"unique_users"`
	LastAccess  time.Time `json:"last_access"`
}
