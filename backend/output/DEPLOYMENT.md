# 医疗数据平台服务器 - 部署包

这是医疗数据平台服务器的完整部署包，包含了运行服务所需的所有文件。

## 目录结构

```
.
├── bin/                    # 二进制文件
│   └── server             # 服务器可执行文件
├── configs/               # 配置文件
│   ├── config.yaml        # 主配置文件
│   └── config.example.yaml # 配置示例
├── scripts/               # 管理脚本
│   ├── start.sh          # 启动脚本
│   ├── stop.sh           # 停止脚本
│   ├── restart.sh        # 重启脚本
│   └── config.sh         # 配置管理脚本
├── migrations/            # 数据库迁移文件
├── logs/                  # 日志目录
├── exports/               # 导出文件目录
├── tmp/                   # 临时文件目录
└── DEPLOYMENT.md          # 本文档
```

## 快速开始

### 1. 初始化配置

```bash
# 创建配置文件
./scripts/config.sh init

# 编辑配置文件
vi configs/config.yaml
```

### 2. 启动服务

```bash
# 后台启动
./scripts/start.sh

# 前台启动（用于调试）
./scripts/start.sh --foreground
```

### 3. 检查服务状态

```bash
# 健康检查
curl http://localhost:8088/health

# 查看日志
tail -f logs/app.log
```

### 4. 停止服务

```bash
# 优雅停止
./scripts/stop.sh

# 强制停止
./scripts/stop.sh --force
```

### 5. 重启服务

```bash
# 重启服务
./scripts/restart.sh

# 强制重启
./scripts/restart.sh --force
```

## 配置说明

### 环境变量

可以通过环境变量覆盖配置：

- `DATABASE_URL`: 数据库连接URL
- `SERVER_PORT`: 服务器端口（默认8088）
- `LOG_LEVEL`: 日志级别（debug/info/warn/error）
- `GIN_MODE`: Gin模式（debug/release）

### 配置文件

主配置文件位于 `configs/config.yaml`，包含：

- 数据库配置
- 服务器配置
- 日志配置
- JWT配置
- 其他业务配置

## 数据库设置

### PostgreSQL

1. 创建数据库：
```sql
CREATE DATABASE medical_platform;
CREATE USER medical_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE medical_platform TO medical_user;
```

2. 运行迁移（如果有迁移工具）：
```bash
# 使用golang-migrate
migrate -path migrations -database "postgres://user:pass@localhost/dbname?sslmode=disable" up
```

## 监控和日志

### 日志文件

- `logs/app.log`: 应用日志
- `logs/gin.log`: HTTP请求日志

### 健康检查

- 端点: `GET /health`
- 返回: JSON格式的健康状态

### API文档

- Swagger UI: `http://localhost:8088/swagger/index.html`

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   lsof -i :8088
   ```

2. **权限问题**
   ```bash
   chmod +x bin/server
   chmod +x scripts/*.sh
   ```

3. **配置文件错误**
   ```bash
   ./scripts/config.sh check
   ```

4. **数据库连接失败**
   - 检查数据库是否运行
   - 验证连接配置
   - 检查网络连接

### 日志分析

```bash
# 查看错误日志
grep ERROR logs/app.log

# 实时监控日志
tail -f logs/app.log | grep ERROR

# 查看最近的日志
tail -100 logs/app.log
```

## 性能优化

### 系统要求

- CPU: 2核心以上
- 内存: 4GB以上
- 磁盘: 10GB可用空间
- 网络: 稳定的网络连接

### 调优建议

1. **数据库连接池**
   - 根据并发需求调整连接池大小

2. **日志级别**
   - 生产环境使用 `info` 或 `warn` 级别

3. **Gin模式**
   - 生产环境设置为 `release` 模式

## 安全建议

1. **配置文件权限**
   ```bash
   chmod 600 configs/config.yaml
   ```

2. **JWT密钥**
   - 使用强随机密钥
   - 定期轮换密钥

3. **数据库安全**
   - 使用专用数据库用户
   - 限制数据库权限
   - 启用SSL连接

4. **网络安全**
   - 使用防火墙限制访问
   - 启用HTTPS
   - 定期更新系统

## 备份和恢复

### 配置备份

```bash
./scripts/config.sh backup
```

### 数据库备份

```bash
pg_dump medical_platform > backup.sql
```

### 恢复配置

```bash
./scripts/config.sh restore backup_file.yaml
```

## 支持

如有问题，请查看：

1. 应用日志: `logs/app.log`
2. 配置文档: `configs/config.example.yaml`
3. 项目文档: 项目仓库的文档目录

---

**注意**: 这是一个部署包，包含预编译的二进制文件。如需修改源代码，请返回开发环境。
