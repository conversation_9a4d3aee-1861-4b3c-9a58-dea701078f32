"use client"

import { DashboardLayout } from "@/components/dashboard-layout"
import MimicQueryBuilder from "@/components/query-builder/mimic-query-builder"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Database, Users, FileSpreadsheet, Activity } from "lucide-react"

export default function MimicAnalysisPage() {
  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* 页面标题和说明 */}
        <div>
          <h1 className="text-3xl font-bold text-gray-900">MIMIC数据分析平台</h1>
          <p className="text-gray-600 mt-2">
            无需编写SQL代码，轻松提取和分析MIMIC-IV重症监护数据库中的医疗数据
          </p>
        </div>

        {/* 数据库概览 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Users className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">患者总数</p>
                  <p className="text-xl font-bold">382,278</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Activity className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">ICU住院次数</p>
                  <p className="text-xl font-bold">76,540</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <FileSpreadsheet className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">实验室记录</p>
                  <p className="text-xl font-bold">122M+</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <Database className="h-5 w-5 text-orange-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">数据版本</p>
                  <p className="text-xl font-bold">MIMIC-IV v2.2</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>



        {/* 主要查询构建器 */}
        <MimicQueryBuilder />


      </div>
    </DashboardLayout>
  )
}
