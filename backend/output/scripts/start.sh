#!/bin/bash

# Medical Data Platform Server - Start Script (Deployment Package)
# 医疗数据平台服务器启动脚本（部署包）

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
APP_ROOT="$(dirname "$SCRIPT_DIR")"
PID_FILE="$APP_ROOT/medical-platform.pid"
BINARY="$APP_ROOT/bin/server"
CONFIG_FILE="$APP_ROOT/configs/config.yaml"

# 检查二进制文件
if [[ ! -f "$BINARY" ]]; then
    log_error "服务器二进制文件不存在: $BINARY"
    exit 1
fi

# 检查配置文件
if [[ ! -f "$CONFIG_FILE" ]]; then
    log_warning "配置文件不存在: $CONFIG_FILE"
    log_info "将使用默认配置"
fi

# 检查是否已经运行
if [[ -f "$PID_FILE" ]]; then
    PID=$(cat "$PID_FILE")
    if kill -0 "$PID" 2>/dev/null; then
        log_error "服务器已经在运行 (PID: $PID)"
        exit 1
    else
        log_warning "清理过期的PID文件"
        rm -f "$PID_FILE"
    fi
fi

# 设置环境变量
export LOG_LEVEL=${LOG_LEVEL:-info}
export GIN_MODE=${GIN_MODE:-release}
export SERVER_PORT=${SERVER_PORT:-8088}

log_info "启动医疗数据平台服务器..."
log_info "配置:"
log_info "  - 二进制文件: $BINARY"
log_info "  - 配置文件: $CONFIG_FILE"
log_info "  - 日志级别: $LOG_LEVEL"
log_info "  - 服务端口: $SERVER_PORT"
log_info "  - PID文件: $PID_FILE"

# 切换到应用目录
cd "$APP_ROOT"

# 启动服务器
if [[ "$1" == "--foreground" || "$1" == "-f" ]]; then
    # 前台运行
    log_info "前台模式启动..."
    exec "$BINARY"
else
    # 后台运行
    log_info "后台模式启动..."
    nohup "$BINARY" > logs/app.log 2>&1 &
    echo $! > "$PID_FILE"

    # 等待启动
    sleep 2

    if kill -0 $(cat "$PID_FILE") 2>/dev/null; then
        log_success "服务器启动成功 (PID: $(cat "$PID_FILE"))"
        log_info "健康检查: http://localhost:$SERVER_PORT/health"
    else
        log_error "服务器启动失败"
        rm -f "$PID_FILE"
        exit 1
    fi
fi
