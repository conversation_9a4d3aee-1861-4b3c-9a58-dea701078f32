package repository

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/chungzy/medical-data-platform/internal/model"
)

type dictionaryRepository struct {
	db *sql.DB
}

func NewDictionaryRepository(db *sql.DB) DictionaryRepository {
	return &dictionaryRepository{db: db}
}

func (r *dictionaryRepository) SearchFields(ctx context.Context, query, dataset, category string, limit, offset int) ([]model.DataField, int, error) {
	var whereConditions []string
	var args []interface{}
	argIndex := 1

	// 构建WHERE条件
	if dataset != "" {
		whereConditions = append(whereConditions, fmt.Sprintf("dataset_id = $%d", argIndex))
		args = append(args, dataset)
		argIndex++
	}

	if category != "" {
		whereConditions = append(whereConditions, fmt.Sprintf("category = $%d", argIndex))
		args = append(args, category)
		argIndex++
	}

	if query != "" {
		whereConditions = append(whereConditions, fmt.Sprintf("(name ILIKE $%d OR name_en ILIKE $%d OR description ILIKE $%d OR code ILIKE $%d)", argIndex, argIndex, argIndex, argIndex))
		args = append(args, "%"+query+"%")
		argIndex++
	}

	whereClause := ""
	if len(whereConditions) > 0 {
		whereClause = "WHERE " + strings.Join(whereConditions, " AND ")
	}

	// 查询数据
	dataQuery := fmt.Sprintf(`
		SELECT id, dataset_id, name, name_en, code, data_type, category, description, unit, value_range, examples
		FROM data_fields %s
		ORDER BY category, name
		LIMIT $%d OFFSET $%d
	`, whereClause, argIndex, argIndex+1)

	args = append(args, limit, offset)

	rows, err := r.db.QueryContext(ctx, dataQuery, args...)
	if err != nil {
		return nil, 0, err
	}
	defer rows.Close()

	var fields []model.DataField
	for rows.Next() {
		var field model.DataField
		var examples []byte

		err := rows.Scan(
			&field.ID, &field.DatasetID, &field.Name, &field.NameEn,
			&field.Code, &field.DataType, &field.Category, &field.Description,
			&field.Unit, &field.ValueRange, &examples,
		)
		if err != nil {
			return nil, 0, err
		}

		if len(examples) > 0 {
			field.Examples = json.RawMessage(examples)
		}

		fields = append(fields, field)
	}

	// 查询总数
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM data_fields %s", whereClause)
	var total int
	err = r.db.QueryRowContext(ctx, countQuery, args[:len(args)-2]...).Scan(&total)
	if err != nil {
		return nil, 0, err
	}

	return fields, total, nil
}

func (r *dictionaryRepository) GetCategories(ctx context.Context, dataset string) ([]model.FieldCategory, error) {
	query := `
		SELECT category, COUNT(*) as count
		FROM data_fields
		WHERE dataset_id = $1
		GROUP BY category
		ORDER BY category
	`

	rows, err := r.db.QueryContext(ctx, query, dataset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var categories []model.FieldCategory
	for rows.Next() {
		var category model.FieldCategory
		err := rows.Scan(&category.Name, &category.Count)
		if err != nil {
			return nil, err
		}
		category.ID = category.Name // 简化处理，使用名称作为ID
		categories = append(categories, category)
	}

	return categories, rows.Err()
}

func (r *dictionaryRepository) GetFieldByID(ctx context.Context, id string) (*model.DataField, error) {
	query := `
		SELECT id, dataset_id, name, name_en, code, data_type, category, description, unit, value_range, examples
		FROM data_fields WHERE id = $1
	`

	field := &model.DataField{}
	var examples []byte

	err := r.db.QueryRowContext(ctx, query, id).Scan(
		&field.ID, &field.DatasetID, &field.Name, &field.NameEn,
		&field.Code, &field.DataType, &field.Category, &field.Description,
		&field.Unit, &field.ValueRange, &examples,
	)
	if err != nil {
		return nil, err
	}

	if len(examples) > 0 {
		field.Examples = json.RawMessage(examples)
	}

	return field, nil
}
