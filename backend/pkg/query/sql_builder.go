package query

import (
	"fmt"
	"strings"

	"github.com/chungzy/medical-data-platform/pkg/logger"
)

// SQLBuilder SQL查询构建器
type SQLBuilder struct {
	datasetID string
	schemas   map[string]string // 表名到schema的映射
}

// NewSQLBuilder 创建SQL构建器
func NewSQLBuilder(datasetID string) *SQLBuilder {
	builder := &SQLBuilder{
		datasetID: datasetID,
		schemas:   make(map[string]string),
	}
	builder.initializeSchemas()
	return builder
}

// initializeSchemas 初始化schema映射
func (sb *SQLBuilder) initializeSchemas() {
	// MIMIC-IV schema映射 - 支持所有31张表
	if sb.datasetID == "mimic-iv" || sb.datasetID == "mimic_iv" {
		sb.schemas = map[string]string{
			// mimiciv_hosp schema - 22张表
			"admissions":         "mimiciv_hosp",
			"d_hcpcs":            "mimiciv_hosp",
			"d_icd_diagnoses":    "mimiciv_hosp",
			"d_icd_procedures":   "mimiciv_hosp",
			"d_labitems":         "mimiciv_hosp",
			"diagnoses_icd":      "mimiciv_hosp",
			"drgcodes":           "mimiciv_hosp",
			"emar":               "mimiciv_hosp",
			"emar_detail":        "mimiciv_hosp",
			"hcpcsevents":        "mimiciv_hosp",
			"labevents":          "mimiciv_hosp",
			"microbiologyevents": "mimiciv_hosp",
			"omr":                "mimiciv_hosp",
			"patients":           "mimiciv_hosp",
			"pharmacy":           "mimiciv_hosp",
			"poe":                "mimiciv_hosp",
			"poe_detail":         "mimiciv_hosp",
			"prescriptions":      "mimiciv_hosp",
			"procedures_icd":     "mimiciv_hosp",
			"provider":           "mimiciv_hosp",
			"services":           "mimiciv_hosp",
			"transfers":          "mimiciv_hosp",

			// mimiciv_icu schema - 9张表
			"caregiver":        "mimiciv_icu",
			"chartevents":      "mimiciv_icu",
			"d_items":          "mimiciv_icu",
			"datetimeevents":   "mimiciv_icu",
			"icustays":         "mimiciv_icu",
			"ingredientevents": "mimiciv_icu",
			"inputevents":      "mimiciv_icu",
			"outputevents":     "mimiciv_icu",
			"procedureevents":  "mimiciv_icu",
		}
	}
}

// BuildSQL 构建SQL查询
func (sb *SQLBuilder) BuildSQL(dsl *MedicalQueryDSL) (string, []interface{}, error) {
	var args []interface{}
	argIndex := 1

	// 构建SELECT子句
	selectClause, err := sb.buildSelectClause(dsl)
	if err != nil {
		return "", nil, fmt.Errorf("failed to build SELECT clause: %w", err)
	}

	// 构建FROM子句
	fromClause, err := sb.buildFromClause(dsl)
	if err != nil {
		return "", nil, fmt.Errorf("failed to build FROM clause: %w", err)
	}

	// 构建WHERE子句
	whereClause, whereArgs, err := sb.buildWhereClause(dsl, &argIndex)
	if err != nil {
		return "", nil, fmt.Errorf("failed to build WHERE clause: %w", err)
	}
	args = append(args, whereArgs...)

	// 构建GROUP BY子句
	groupByClause := sb.buildGroupByClause(dsl)

	// 构建ORDER BY子句
	orderByClause := sb.buildOrderByClause(dsl)

	// 构建LIMIT子句
	limitClause := sb.buildLimitClause(dsl)

	// 组装完整SQL
	var sqlParts []string
	sqlParts = append(sqlParts, selectClause)
	sqlParts = append(sqlParts, fromClause)

	if whereClause != "" {
		sqlParts = append(sqlParts, whereClause)
	}

	if groupByClause != "" {
		sqlParts = append(sqlParts, groupByClause)
	}

	if orderByClause != "" {
		sqlParts = append(sqlParts, orderByClause)
	}

	if limitClause != "" {
		sqlParts = append(sqlParts, limitClause)
	}

	sql := strings.Join(sqlParts, "\n")

	logger.Info("Generated SQL query", map[string]interface{}{
		"dataset_id": sb.datasetID,
		"sql_length": len(sql),
		"arg_count":  len(args),
	})

	return sql, args, nil
}

// buildSelectClause 构建SELECT子句
func (sb *SQLBuilder) buildSelectClause(dsl *MedicalQueryDSL) (string, error) {
	if len(dsl.Fields) == 0 && len(dsl.Aggregations) == 0 {
		return "", fmt.Errorf("no fields or aggregations specified")
	}

	var selectItems []string

	// 添加普通字段
	for _, field := range dsl.Fields {
		fullField := sb.getFullFieldName(field.Table, field.Field)

		if field.Function != "" {
			// 应用函数
			selectItem := fmt.Sprintf("%s(%s)", field.Function, fullField)
			if field.Alias != "" {
				selectItem += " AS " + field.Alias
			}
			selectItems = append(selectItems, selectItem)
		} else {
			// 普通字段
			selectItem := fullField
			if field.Alias != "" {
				selectItem += " AS " + field.Alias
			}
			selectItems = append(selectItems, selectItem)
		}
	}

	// 添加聚合字段
	for _, agg := range dsl.Aggregations {
		fullField := sb.getFullFieldName(agg.Table, agg.Field)
		selectItem := fmt.Sprintf("%s(%s)", agg.Function, fullField)
		if agg.Alias != "" {
			selectItem += " AS " + agg.Alias
		}
		selectItems = append(selectItems, selectItem)
	}

	return "SELECT " + strings.Join(selectItems, ", "), nil
}

// buildFromClause 构建FROM子句
func (sb *SQLBuilder) buildFromClause(dsl *MedicalQueryDSL) (string, error) {
	if dsl.Cohort.PrimaryTable == "" {
		return "", fmt.Errorf("primary table not specified")
	}

	primaryTable := sb.getFullTableName(dsl.Cohort.PrimaryTable)
	fromClause := "FROM " + primaryTable

	// 收集需要JOIN的表
	joinTables := make(map[string]bool)

	// 从字段中收集表
	for _, field := range dsl.Fields {
		if field.Table != dsl.Cohort.PrimaryTable {
			joinTables[field.Table] = true
		}
	}

	// 从过滤条件中收集表
	for _, filter := range dsl.Filters {
		if filter.Table != dsl.Cohort.PrimaryTable {
			joinTables[filter.Table] = true
		}
	}

	// 从队列条件中收集表
	for _, criteria := range dsl.Cohort.InclusionCriteria {
		if criteria.Table != dsl.Cohort.PrimaryTable {
			joinTables[criteria.Table] = true
		}
	}
	for _, criteria := range dsl.Cohort.ExclusionCriteria {
		if criteria.Table != dsl.Cohort.PrimaryTable {
			joinTables[criteria.Table] = true
		}
	}

	// 构建JOIN子句
	for table := range joinTables {
		joinClause := sb.buildJoinClause(dsl.Cohort.PrimaryTable, table)
		if joinClause != "" {
			fromClause += "\n" + joinClause
		}
	}

	return fromClause, nil
}

// buildJoinClause 构建JOIN子句
func (sb *SQLBuilder) buildJoinClause(primaryTable, joinTable string) string {
	// 定义表之间的关联关系
	joinConditions := map[string]map[string]string{
		"patients": {
			"admissions":    "patients.subject_id = admissions.subject_id",
			"labevents":     "patients.subject_id = labevents.subject_id",
			"diagnoses_icd": "patients.subject_id = diagnoses_icd.subject_id",
			"prescriptions": "patients.subject_id = prescriptions.subject_id",
			"icustays":      "patients.subject_id = icustays.subject_id",
		},
		"admissions": {
			"patients":      "admissions.subject_id = patients.subject_id",
			"labevents":     "admissions.hadm_id = labevents.hadm_id",
			"diagnoses_icd": "admissions.hadm_id = diagnoses_icd.hadm_id",
			"prescriptions": "admissions.hadm_id = prescriptions.hadm_id",
			"icustays":      "admissions.hadm_id = icustays.hadm_id",
		},
		"icustays": {
			"patients":     "icustays.subject_id = patients.subject_id",
			"admissions":   "icustays.hadm_id = admissions.hadm_id",
			"chartevents":  "icustays.stay_id = chartevents.stay_id",
			"inputevents":  "icustays.stay_id = inputevents.stay_id",
			"outputevents": "icustays.stay_id = outputevents.stay_id",
		},
	}

	if conditions, exists := joinConditions[primaryTable]; exists {
		if condition, exists := conditions[joinTable]; exists {
			fullJoinTable := sb.getFullTableName(joinTable)
			return fmt.Sprintf("LEFT JOIN %s ON %s", fullJoinTable, condition)
		}
	}

	// 默认通过subject_id关联
	fullJoinTable := sb.getFullTableName(joinTable)
	primaryFullTable := sb.getFullTableName(primaryTable)
	return fmt.Sprintf("LEFT JOIN %s ON %s.subject_id = %s.subject_id",
		fullJoinTable, primaryFullTable, fullJoinTable)
}

// buildWhereClause 构建WHERE子句
func (sb *SQLBuilder) buildWhereClause(dsl *MedicalQueryDSL, argIndex *int) (string, []interface{}, error) {
	var conditions []string
	var args []interface{}

	// 添加队列包含条件
	for _, criteria := range dsl.Cohort.InclusionCriteria {
		condition, conditionArgs := sb.buildCondition(criteria.Field, criteria.Table, criteria.Operator, criteria.Value, argIndex)
		conditions = append(conditions, condition)
		args = append(args, conditionArgs...)
	}

	// 添加队列排除条件
	for _, criteria := range dsl.Cohort.ExclusionCriteria {
		condition, conditionArgs := sb.buildCondition(criteria.Field, criteria.Table, criteria.Operator, criteria.Value, argIndex)
		conditions = append(conditions, "NOT ("+condition+")")
		args = append(args, conditionArgs...)
	}

	// 添加过滤条件
	for _, filter := range dsl.Filters {
		condition, conditionArgs := sb.buildCondition(filter.Field, filter.Table, filter.Operator, filter.Value, argIndex)
		conditions = append(conditions, condition)
		args = append(args, conditionArgs...)
	}

	// 添加年龄范围条件
	if dsl.Cohort.AgeRange != nil {
		ageConditions := sb.buildAgeRangeCondition(dsl.Cohort.AgeRange, argIndex)
		if len(ageConditions) > 0 {
			conditions = append(conditions, ageConditions...)
			if dsl.Cohort.AgeRange.Min != nil {
				args = append(args, *dsl.Cohort.AgeRange.Min)
			}
			if dsl.Cohort.AgeRange.Max != nil {
				args = append(args, *dsl.Cohort.AgeRange.Max)
			}
		}
	}

	// 添加性别条件
	if dsl.Cohort.Gender != nil {
		genderCondition := sb.getFullFieldName("patients", "gender") + " = $" + fmt.Sprintf("%d", *argIndex)
		conditions = append(conditions, genderCondition)
		args = append(args, *dsl.Cohort.Gender)
		*argIndex++
	}

	// 添加时间范围条件
	if dsl.TimeRange != nil {
		timeConditions, timeArgs := sb.buildTimeRangeCondition(dsl.TimeRange, argIndex)
		conditions = append(conditions, timeConditions...)
		args = append(args, timeArgs...)
	}

	if len(conditions) == 0 {
		return "", args, nil
	}

	return "WHERE " + strings.Join(conditions, " AND "), args, nil
}

// buildCondition 构建单个条件
func (sb *SQLBuilder) buildCondition(field, table, operator string, value interface{}, argIndex *int) (string, []interface{}) {
	fullField := sb.getFullFieldName(table, field)
	placeholder := "$" + fmt.Sprintf("%d", *argIndex)
	*argIndex++

	switch operator {
	case OpEqual:
		return fullField + " = " + placeholder, []interface{}{value}
	case OpNotEqual:
		return fullField + " != " + placeholder, []interface{}{value}
	case OpGreaterThan:
		return fullField + " > " + placeholder, []interface{}{value}
	case OpGreaterThanOrEqual:
		return fullField + " >= " + placeholder, []interface{}{value}
	case OpLessThan:
		return fullField + " < " + placeholder, []interface{}{value}
	case OpLessThanOrEqual:
		return fullField + " <= " + placeholder, []interface{}{value}
	case OpLike:
		return fullField + " LIKE " + placeholder, []interface{}{value}
	case OpNotLike:
		return fullField + " NOT LIKE " + placeholder, []interface{}{value}
	case OpIsNull:
		*argIndex-- // 不需要参数
		return fullField + " IS NULL", []interface{}{}
	case OpIsNotNull:
		*argIndex-- // 不需要参数
		return fullField + " IS NOT NULL", []interface{}{}
	default:
		return fullField + " = " + placeholder, []interface{}{value}
	}
}

// buildAgeRangeCondition 构建年龄范围条件
func (sb *SQLBuilder) buildAgeRangeCondition(ageRange *AgeRange, argIndex *int) []string {
	var conditions []string
	ageField := sb.getFullFieldName("patients", "anchor_age")

	if ageRange.Min != nil {
		conditions = append(conditions, ageField+" >= $"+fmt.Sprintf("%d", *argIndex))
		*argIndex++
	}

	if ageRange.Max != nil {
		conditions = append(conditions, ageField+" <= $"+fmt.Sprintf("%d", *argIndex))
		*argIndex++
	}

	return conditions
}

// buildTimeRangeCondition 构建时间范围条件
func (sb *SQLBuilder) buildTimeRangeCondition(timeRange *TimeRange, argIndex *int) ([]string, []interface{}) {
	var conditions []string
	var args []interface{}

	timeField := sb.getFullFieldName(timeRange.Table, timeRange.Field)

	if timeRange.StartDate != nil {
		conditions = append(conditions, timeField+" >= $"+fmt.Sprintf("%d", *argIndex))
		args = append(args, *timeRange.StartDate)
		*argIndex++
	}

	if timeRange.EndDate != nil {
		conditions = append(conditions, timeField+" <= $"+fmt.Sprintf("%d", *argIndex))
		args = append(args, *timeRange.EndDate)
		*argIndex++
	}

	return conditions, args
}

// buildGroupByClause 构建GROUP BY子句
func (sb *SQLBuilder) buildGroupByClause(dsl *MedicalQueryDSL) string {
	if len(dsl.GroupBy) == 0 {
		return ""
	}

	var groupFields []string
	for _, field := range dsl.GroupBy {
		// 假设格式为 "table.field"
		parts := strings.Split(field, ".")
		if len(parts) == 2 {
			groupFields = append(groupFields, sb.getFullFieldName(parts[0], parts[1]))
		} else {
			groupFields = append(groupFields, field)
		}
	}

	return "GROUP BY " + strings.Join(groupFields, ", ")
}

// buildOrderByClause 构建ORDER BY子句
func (sb *SQLBuilder) buildOrderByClause(dsl *MedicalQueryDSL) string {
	if len(dsl.OrderBy) == 0 {
		return ""
	}

	var orderItems []string
	for _, order := range dsl.OrderBy {
		fullField := sb.getFullFieldName(order.Table, order.Field)
		orderItems = append(orderItems, fullField+" "+order.Direction)
	}

	return "ORDER BY " + strings.Join(orderItems, ", ")
}

// buildLimitClause 构建LIMIT子句
func (sb *SQLBuilder) buildLimitClause(dsl *MedicalQueryDSL) string {
	if dsl.Limit == nil {
		return ""
	}

	limitClause := fmt.Sprintf("LIMIT %d", *dsl.Limit)
	if dsl.Offset != nil && *dsl.Offset > 0 {
		limitClause += fmt.Sprintf(" OFFSET %d", *dsl.Offset)
	}

	return limitClause
}

// getFullTableName 获取完整表名
func (sb *SQLBuilder) getFullTableName(tableName string) string {
	if schema, exists := sb.schemas[tableName]; exists {
		return schema + "." + tableName
	}
	return tableName
}

// getFullFieldName 获取完整字段名
func (sb *SQLBuilder) getFullFieldName(tableName, fieldName string) string {
	fullTableName := sb.getFullTableName(tableName)
	return fullTableName + "." + fieldName
}
