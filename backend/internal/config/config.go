package config

import (
	"fmt"
	"io/ioutil"
	"log"

	"gopkg.in/yaml.v3"
)

type Config struct {
	Server      ServerConfig                `yaml:"server"`
	Database    DatabaseConfig              `yaml:"database"`
	Datasources map[string]DatasourceConfig `yaml:"datasources"`
	JWT         JWTConfig                   `yaml:"jwt"`
	App         AppConfig                   `yaml:"app"`
}

type ServerConfig struct {
	Port string `yaml:"port"`
	Mode string `yaml:"mode"`
	Host string `yaml:"host"`
}

type DatabaseConfig struct {
	Host     string `yaml:"host"`
	Port     int    `yaml:"port"`
	User     string `yaml:"user"`
	Password string `yaml:"password"`
	DBName   string `yaml:"dbname"`
	SSLMode  string `yaml:"sslmode"`
	URL      string `yaml:"-"` // 不序列化，动态生成
}

type DatasourceConfig struct {
	Enabled           bool   `yaml:"enabled"`
	Type              string `yaml:"type"`
	Host              string `yaml:"host"`
	Port              int    `yaml:"port"`
	User              string `yaml:"user"`
	Password          string `yaml:"password"`
	DBName            string `yaml:"dbname"`
	SSLMode           string `yaml:"sslmode"`
	Schema            string `yaml:"schema"`
	Description       string `yaml:"description"`
	MaxConnections    int    `yaml:"max_connections"`
	ConnectionTimeout int    `yaml:"connection_timeout"`
	QueryTimeout      int    `yaml:"query_timeout"`
	URL               string `yaml:"-"` // 不序列化，动态生成
}

type JWTConfig struct {
	SecretKey      string `yaml:"secret_key"`
	ExpirationTime int    `yaml:"expiration_hours"` // hours
}

type AppConfig struct {
	Name                    string `yaml:"name"`
	Version                 string `yaml:"version"`
	Environment             string `yaml:"environment"`
	LogLevel                string `yaml:"log_level"`
	MaxQueryResults         int    `yaml:"max_query_results"`
	ExportFileRetentionDays int    `yaml:"export_file_retention_days"`
	MaxConcurrentQueries    int    `yaml:"max_concurrent_queries"`

	// 日志配置
	Log LogConfig `yaml:"log"`

	// 文件清理配置
	FileCleanup FileCleanupConfig `yaml:"file_cleanup"`
}

// LogConfig 日志配置
type LogConfig struct {
	Dir              string `yaml:"dir"`               // 日志目录
	MaxSize          int    `yaml:"max_size"`          // 单个日志文件最大大小(MB)
	MaxBackups       int    `yaml:"max_backups"`       // 保留的日志文件数量
	MaxAge           int    `yaml:"max_age"`           // 保留天数
	Compress         bool   `yaml:"compress"`          // 是否压缩旧日志
	EnableConsole    bool   `yaml:"enable_console"`    // 是否输出到控制台
	EnableStructured bool   `yaml:"enable_structured"` // 是否使用结构化日志格式
}

// FileCleanupConfig 文件清理配置
type FileCleanupConfig struct {
	Enable           bool   `yaml:"enable"`             // 是否启用文件清理
	Interval         string `yaml:"interval"`           // 清理间隔，如"24h", "12h"
	ExportDir        string `yaml:"export_dir"`         // 导出文件目录
	TempDir          string `yaml:"temp_dir"`           // 临时文件目录
	CleanupOnStartup bool   `yaml:"cleanup_on_startup"` // 启动时是否立即清理一次
}

// Load 加载配置文件
func Load() *Config {
	// 查找配置文件
	configPath := findConfigFile()
	if configPath == "" {
		log.Fatal("Configuration file not found. Please create config.yaml")
	}

	// 读取配置文件
	data, err := ioutil.ReadFile(configPath)
	if err != nil {
		log.Fatalf("Failed to read config file: %v", err)
	}

	// 解析YAML
	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		log.Fatalf("Failed to parse config file: %v", err)
	}

	// 生成数据库连接URL
	config.Database.URL = fmt.Sprintf(
		"host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		config.Database.Host, config.Database.Port, config.Database.User,
		config.Database.Password, config.Database.DBName, config.Database.SSLMode,
	)

	// 生成数据源连接URL
	for name, datasource := range config.Datasources {
		if datasource.Enabled {
			datasource.URL = fmt.Sprintf(
				"host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
				datasource.Host, datasource.Port, datasource.User,
				datasource.Password, datasource.DBName, datasource.SSLMode,
			)
			config.Datasources[name] = datasource
		}
	}

	// 设置默认值
	setDefaults(&config)

	log.Printf("Configuration loaded from: %s", configPath)
	return &config
}

// findConfigFile 查找配置文件
func findConfigFile() string {
	// 可能的配置文件路径 - 优先从configs目录查找
	/*
		possiblePaths := []string{
			"configs/config.yaml",
			"configs/config.yml",
			"config.yaml",
			"config.yml",
			"./configs/config.yaml",
			"./configs/config.yml",
			"./config.yaml",
			"./config.yml",
		}

		for _, path := range possiblePaths {
			if _, err := os.Stat(path); err == nil {
				absPath, _ := filepath.Abs(path)
				return absPath
			}
		}*/

	return "./configs/config.yaml"
}

// setDefaults 设置默认值
func setDefaults(config *Config) {
	if config.Server.Port == "" {
		config.Server.Port = "8088"
	}
	if config.Server.Mode == "" {
		config.Server.Mode = "debug"
	}
	if config.Server.Host == "" {
		config.Server.Host = "localhost"
	}

	if config.Database.Host == "" {
		config.Database.Host = "localhost"
	}
	if config.Database.Port == 0 {
		config.Database.Port = 5432
	}
	if config.Database.User == "" {
		config.Database.User = "postgres"
	}
	if config.Database.DBName == "" {
		config.Database.DBName = "medical_data_platform"
	}
	if config.Database.SSLMode == "" {
		config.Database.SSLMode = "disable"
	}

	if config.JWT.SecretKey == "" {
		config.JWT.SecretKey = "your-secret-key-change-in-production"
	}
	if config.JWT.ExpirationTime == 0 {
		config.JWT.ExpirationTime = 24
	}

	if config.App.Name == "" {
		config.App.Name = "Medical Data Platform"
	}
	if config.App.Version == "" {
		config.App.Version = "1.0.0"
	}
	if config.App.Environment == "" {
		config.App.Environment = "development"
	}
	if config.App.LogLevel == "" {
		config.App.LogLevel = "info"
	}
	if config.App.MaxQueryResults == 0 {
		config.App.MaxQueryResults = 10000
	}
	if config.App.ExportFileRetentionDays == 0 {
		config.App.ExportFileRetentionDays = 7
	}
	if config.App.MaxConcurrentQueries == 0 {
		config.App.MaxConcurrentQueries = 5
	}

	// 设置日志配置默认值
	if config.App.Log.Dir == "" {
		config.App.Log.Dir = "logs"
	}
	if config.App.Log.MaxSize == 0 {
		config.App.Log.MaxSize = 100
	}
	if config.App.Log.MaxBackups == 0 {
		config.App.Log.MaxBackups = 10
	}
	if config.App.Log.MaxAge == 0 {
		config.App.Log.MaxAge = 30
	}
	// EnableConsole和EnableStructured默认为false，不需要特殊处理
	// Compress默认为false，不需要特殊处理

	// 设置文件清理配置默认值
	if config.App.FileCleanup.Interval == "" {
		config.App.FileCleanup.Interval = "24h"
	}
	if config.App.FileCleanup.ExportDir == "" {
		config.App.FileCleanup.ExportDir = "exports"
	}
	if config.App.FileCleanup.TempDir == "" {
		config.App.FileCleanup.TempDir = "tmp"
	}
	// Enable和CleanupOnStartup默认为false，不需要特殊处理
}

// GetDatabaseURL 获取数据库连接URL
func (c *Config) GetDatabaseURL() string {
	return c.Database.URL
}

// IsProduction 判断是否为生产环境
func (c *Config) IsProduction() bool {
	return c.App.Environment == "production"
}

// IsDevelopment 判断是否为开发环境
func (c *Config) IsDevelopment() bool {
	return c.App.Environment == "development"
}
