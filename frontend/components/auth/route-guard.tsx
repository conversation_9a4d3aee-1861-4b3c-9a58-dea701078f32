"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/lib/hooks/use-auth"
import { usePermissions, Permission, Role } from "@/lib/hooks/use-permissions"
import { toast } from "sonner"

interface RouteGuardProps {
  children: React.ReactNode
  requireAuth?: boolean
  requirePermission?: Permission
  requireRole?: Role
  requireAnyRole?: Role[]
  redirectTo?: string
  fallback?: React.ReactNode
}

export function RouteGuard({
  children,
  requireAuth = true,
  requirePermission,
  requireRole,
  requireAnyRole,
  redirectTo = "/login",
  fallback,
}: RouteGuardProps) {
  const router = useRouter()
  const { isAuthenticated, loading: authLoading } = useAuth()
  const { hasPermission, hasRole, hasAnyRole, loading: permissionLoading } = usePermissions()

  useEffect(() => {
    // 等待认证状态加载完成
    if (authLoading || permissionLoading) return

    // 检查认证要求
    if (requireAuth && !isAuthenticated) {
      toast.error("请先登录")
      router.push(redirectTo)
      return
    }

    // 如果不需要认证，直接通过
    if (!requireAuth) return

    // 检查权限要求
    if (requirePermission && !hasPermission(requirePermission)) {
      toast.error("权限不足")
      router.push("/unauthorized")
      return
    }

    // 检查角色要求
    if (requireRole && !hasRole(requireRole)) {
      toast.error("角色权限不足")
      router.push("/unauthorized")
      return
    }

    // 检查任意角色要求
    if (requireAnyRole && !hasAnyRole(requireAnyRole)) {
      toast.error("角色权限不足")
      router.push("/unauthorized")
      return
    }
  }, [
    authLoading,
    permissionLoading,
    isAuthenticated,
    requireAuth,
    requirePermission,
    requireRole,
    requireAnyRole,
    hasPermission,
    hasRole,
    hasAnyRole,
    router,
    redirectTo,
  ])

  // 显示加载状态
  if (authLoading || permissionLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">正在验证权限...</p>
        </div>
      </div>
    )
  }

  // 检查认证状态
  if (requireAuth && !isAuthenticated) {
    return fallback || (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">需要登录</h2>
          <p className="text-gray-600">正在跳转到登录页面...</p>
        </div>
      </div>
    )
  }

  // 检查权限
  if (requirePermission && !hasPermission(requirePermission)) {
    return fallback || (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">权限不足</h2>
          <p className="text-gray-600">您没有访问此页面的权限</p>
        </div>
      </div>
    )
  }

  // 检查角色
  if (requireRole && !hasRole(requireRole)) {
    return fallback || (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">角色权限不足</h2>
          <p className="text-gray-600">您的角色无法访问此页面</p>
        </div>
      </div>
    )
  }

  // 检查任意角色
  if (requireAnyRole && !hasAnyRole(requireAnyRole)) {
    return fallback || (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">角色权限不足</h2>
          <p className="text-gray-600">您的角色无法访问此页面</p>
        </div>
      </div>
    )
  }

  return <>{children}</>
}

// 管理员路由保护
export function AdminRouteGuard({ children }: { children: React.ReactNode }) {
  return (
    <RouteGuard requireRole="admin">
      {children}
    </RouteGuard>
  )
}

// 普通用户或管理员路由保护
export function UserRouteGuard({ children }: { children: React.ReactNode }) {
  return (
    <RouteGuard requireAnyRole={["user", "admin"]}>
      {children}
    </RouteGuard>
  )
}

// 数据集管理路由保护
export function DatasetManagementRouteGuard({ children }: { children: React.ReactNode }) {
  return (
    <RouteGuard requirePermission="dataset:write">
      {children}
    </RouteGuard>
  )
}

// 用户管理路由保护
export function UserManagementRouteGuard({ children }: { children: React.ReactNode }) {
  return (
    <RouteGuard requirePermission="user:read">
      {children}
    </RouteGuard>
  )
}

// 系统管理路由保护
export function SystemManagementRouteGuard({ children }: { children: React.ReactNode }) {
  return (
    <RouteGuard requirePermission="system:admin">
      {children}
    </RouteGuard>
  )
}
