# 增强型查询构建器实现计划

## 📅 项目时间线

### 第一阶段：基础架构搭建 (1-2周)

#### 后端开发
- [ ] **依赖管理**
  - 添加 excelize/v2 依赖用于Excel导出
  - 更新 go.mod 文件
  - 配置开发环境

- [ ] **数据库扩展**
  ```sql
  -- 添加查询配置表
  CREATE TABLE enhanced_queries (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    dataset VARCHAR(100) NOT NULL,
    config JSONB NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
  );
  
  -- 扩展导出记录表
  ALTER TABLE export_records ADD COLUMN export_options JSONB;
  ```

- [ ] **API接口实现**
  - `POST /api/enhanced-queries/generate-sql`
  - `POST /api/enhanced-queries/execute` 
  - `GET /api/enhanced-queries/table-relationships/:dataset`
  - `POST /api/enhanced-queries/export`

#### 前端开发
- [ ] **组件库扩展**
  - 安装必要的UI组件依赖
  - 创建增强查询构建器基础组件结构
  - 设置路由和页面结构

### 第二阶段：核心功能开发 (2-3周)

#### 人群筛选器模块
- [ ] **UI组件**
  - CohortFilter 主组件
  - ConditionRow 条件行组件  
  - 字段选择下拉框
  - 操作符选择器
  - 逻辑连接器 (AND/OR)

- [ ] **功能实现**
  - 条件添加/删除/编辑
  - 实时SQL预览
  - 人群规模估算
  - 验证和错误处理

#### 表关联配置器模块  
- [ ] **UI组件**
  - TableJoiner 主组件
  - JoinRow 关联行组件
  - 表关系图可视化
  - 关联条件智能提示

- [ ] **功能实现**
  - JOIN类型配置
  - 关联条件配置
  - 表别名管理
  - 关联验证检查

#### 字段选择器模块
- [ ] **UI组件**
  - FieldSelector 主组件
  - 可用字段树形列表
  - 已选字段管理
  - 字段搜索和筛选

- [ ] **功能实现**
  - 分表字段展示
  - 批量选择操作
  - 字段拖拽排序
  - 字段别名设置

### 第三阶段：SQL生成与执行 (1-2周)

#### SQL生成引擎
- [ ] **后端实现**
  - 复杂SQL构建器
  - CTE (Common Table Expression) 支持
  - 多表JOIN优化
  - 查询性能分析

- [ ] **前端集成**
  - SQL预览组件
  - 语法高亮显示
  - 可编辑SQL支持
  - 执行计划展示

#### 查询执行器
- [ ] **异步执行**
  - 后台任务队列
  - 执行进度跟踪
  - 结果缓存机制
  - 错误处理和重试

- [ ] **结果展示**
  - 分页数据表格
  - 查询统计信息
  - 数据质量检查
  - 导出预配置

### 第四阶段：导出功能增强 (1-2周)

#### Excel导出增强
- [ ] **多工作表支持**
  - 汇总信息工作表
  - 查询结果工作表
  - 人群分析工作表
  - 数据字典工作表

- [ ] **样式和格式**
  - 专业样式模板
  - 自动列宽调整
  - 数据类型格式化
  - 条件格式设置

#### 导出选项配置
- [ ] **UI组件**
  - 导出配置面板
  - 格式选择器
  - 选项复选框
  - 进度显示器

- [ ] **功能实现**
  - 异步导出处理
  - 大文件分块导出
  - 下载链接生成
  - 导出历史管理

### 第五阶段：用户体验优化 (1周)

#### 交互体验
- [ ] **智能推荐**
  - 字段关联推荐
  - 历史查询模板
  - 常用条件快速填充
  - 上下文帮助提示

- [ ] **响应式设计**
  - 移动端适配
  - 平板端优化
  - 桌面端多屏支持
  - 键盘快捷键

#### 性能优化
- [ ] **前端优化**
  - 组件懒加载
  - 虚拟化长列表
  - 防抖搜索
  - 缓存策略

- [ ] **后端优化**
  - 查询结果缓存
  - 数据库连接池
  - SQL执行计划优化
  - 内存使用优化

## 🛠️ 技术实现要点

### 1. 前端架构重点

```typescript
// 状态管理结构
interface QueryBuilderState {
  // 基础配置
  dataset: string
  mainTable: string
  
  // 人群筛选
  cohortConditions: QueryCondition[]
  cohortSize?: number
  
  // 表关联
  joins: TableJoin[]
  tableRelationships: TableRelationship[]
  
  // 字段选择
  availableFields: Field[]
  selectedFields: Field[]
  
  // 查询结果
  generatedSQL: string
  queryResult?: QueryResult
  
  // 导出配置
  exportOptions: ExportOptions
}

// API调用封装
class EnhancedQueryAPI {
  static async generateSQL(config: QueryConfig): Promise<SQLResponse>
  static async executeQuery(config: QueryConfig): Promise<QueryResult>
  static async getTableRelationships(dataset: string): Promise<TableRelationship[]>
  static async exportData(queryId: string, options: ExportOptions): Promise<ExportRecord>
}
```

### 2. 后端架构重点

```go
// 服务层接口
type EnhancedQueryService interface {
    GenerateSQL(ctx context.Context, config *QueryConfig) (*SQLResponse, error)
    ExecuteQuery(ctx context.Context, userID string, config *QueryConfig) (*QueryResult, error)
    GetTableRelationships(dataset string) ([]*TableRelationship, error)
    CreateExport(userID, queryID string, options map[string]interface{}) (*ExportRecord, error)
}

// SQL构建器
type SQLBuilder struct {
    dataset string
    config  *QueryConfig
}

func (b *SQLBuilder) BuildCohortQuery() string
func (b *SQLBuilder) BuildMainQuery() string
func (b *SQLBuilder) BuildCompleteQuery() string
func (b *SQLBuilder) ValidateQuery() []string
func (b *SQLBuilder) OptimizeQuery() []string
```

### 3. 数据库设计扩展

```sql
-- 增强查询配置表
CREATE TABLE enhanced_query_configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    dataset VARCHAR(100) NOT NULL,
    main_table VARCHAR(100) NOT NULL,
    cohort_conditions JSONB,
    table_joins JSONB,
    selected_fields JSONB,
    additional_conditions JSONB,
    query_options JSONB,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 查询执行历史表
CREATE TABLE query_execution_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    config_id UUID REFERENCES enhanced_query_configs(id),
    user_id UUID NOT NULL REFERENCES users(id),
    sql_query TEXT NOT NULL,
    execution_time_ms INTEGER,
    result_count INTEGER,
    cohort_size INTEGER,
    status VARCHAR(20) NOT NULL,
    error_message TEXT,
    executed_at TIMESTAMP DEFAULT NOW()
);

-- 导出记录扩展
ALTER TABLE export_records ADD COLUMN config_id UUID REFERENCES enhanced_query_configs(id);
ALTER TABLE export_records ADD COLUMN export_type VARCHAR(50) DEFAULT 'enhanced';
ALTER TABLE export_records ADD COLUMN export_options JSONB;
```

## 📊 开发里程碑

### 里程碑1：MVP版本 (第3周结束)
- ✅ 基础人群筛选功能
- ✅ 简单表关联配置
- ✅ 基础字段选择
- ✅ SQL生成和预览
- ✅ 简单查询执行

### 里程碑2：完整功能版本 (第5周结束)
- ✅ 完整的UI交互体验
- ✅ 复杂查询支持
- ✅ Excel导出功能
- ✅ 性能优化
- ✅ 错误处理完善

### 里程碑3：生产就绪版本 (第6周结束)
- ✅ 全面测试覆盖
- ✅ 性能基准测试
- ✅ 安全性审查
- ✅ 用户文档完善
- ✅ 部署配置

## 🧪 测试策略

### 单元测试
- [ ] 前端组件测试 (Jest + React Testing Library)
- [ ] 后端服务测试 (Go testing)
- [ ] SQL构建器测试
- [ ] 导出功能测试

### 集成测试
- [ ] API接口测试
- [ ] 数据库操作测试
- [ ] 文件导出测试
- [ ] 端到端用户流程测试

### 性能测试
- [ ] 大数据集查询测试
- [ ] 并发用户测试
- [ ] 内存使用测试
- [ ] 导出性能测试

## 📚 文档计划

### 用户文档
- [ ] 功能介绍文档
- [ ] 使用教程（视频+图文）
- [ ] 常见问题解答
- [ ] 最佳实践指南

### 开发文档
- [ ] API接口文档
- [ ] 数据库设计文档
- [ ] 部署指南
- [ ] 维护手册

## 🚀 部署计划

### 开发环境
- [ ] Docker容器化配置
- [ ] 开发数据库准备
- [ ] 示例数据导入
- [ ] 环境变量配置

### 生产环境
- [ ] 服务器资源评估
- [ ] 数据库性能调优
- [ ] 负载均衡配置
- [ ] 监控和日志配置

---

## 📋 任务分配建议

### 后端开发 (2人，4周)
**主要任务**：
- 增强查询API开发
- SQL构建引擎实现
- 导出服务开发
- 性能优化

### 前端开发 (2人，4周)  
**主要任务**：
- 查询构建器UI开发
- 交互体验设计
- 状态管理实现
- 响应式适配

### 测试工程师 (1人，2周)
**主要任务**：
- 测试用例设计
- 自动化测试实现
- 性能测试执行
- 质量保证

### 产品经理 (1人，持续)
**主要任务**：
- 需求细化和确认
- 用户体验设计
- 项目进度跟踪
- 验收测试

这个实现计划提供了详细的开发路线图，可以根据团队规模和时间要求进行调整。 