package service

import (
	"context"
	"encoding/json"

	"github.com/chungzy/medical-data-platform/internal/model"
	"github.com/chungzy/medical-data-platform/internal/repository"

	"github.com/google/uuid"
)

type TemplateService struct {
	templateRepo repository.TemplateRepository
	queryRepo    repository.QueryRepository
}

func NewTemplateService(templateRepo repository.TemplateRepository, queryRepo repository.QueryRepository) *TemplateService {
	return &TemplateService{
		templateRepo: templateRepo,
		queryRepo:    queryRepo,
	}
}

func (s *TemplateService) Create(ctx context.Context, template *model.QueryTemplate) error {
	return s.templateRepo.Create(ctx, template)
}

func (s *TemplateService) GetByID(ctx context.Context, id uuid.UUID) (*model.QueryTemplate, error) {
	return s.templateRepo.GetByID(ctx, id)
}

func (s *TemplateService) GetTemplates(ctx context.Context, userID uuid.UUID, category string, isPublic bool, search string) ([]*model.QueryTemplate, error) {
	// 根据是否公开获取不同的模板
	var templates []model.QueryTemplate
	var err error

	if isPublic {
		templates, _, err = s.templateRepo.GetAll(ctx, category, "", 100, 0)
	} else {
		templates, _, err = s.templateRepo.GetByUserID(ctx, userID, 100, 0)
	}

	if err != nil {
		return nil, err
	}

	// 转换为指针切片
	result := make([]*model.QueryTemplate, len(templates))
	for i := range templates {
		result[i] = &templates[i]
	}

	return result, nil
}

func (s *TemplateService) Update(ctx context.Context, template *model.QueryTemplate) error {
	return s.templateRepo.Update(ctx, template)
}

func (s *TemplateService) Delete(ctx context.Context, id uuid.UUID) error {
	return s.templateRepo.Delete(ctx, id)
}

func (s *TemplateService) UseTemplate(ctx context.Context, templateID uuid.UUID, userID uuid.UUID, parameters map[string]interface{}) (*model.QueryHistory, error) {
	// 获取模板
	template, err := s.templateRepo.GetByID(ctx, templateID)
	if err != nil {
		return nil, err
	}

	// 增加使用次数
	s.templateRepo.IncrementUsage(ctx, templateID)

	// 基于模板创建查询请求
	var queryConfig model.QueryExecuteRequest
	if err := json.Unmarshal(template.TemplateConfig, &queryConfig); err != nil {
		return nil, err
	}

	// 应用参数替换
	if parameters != nil {
		// 这里可以实现参数替换逻辑
		// 例如替换查询条件中的占位符
	}

	// 创建查询历史记录
	name := template.Name + " (from template)"
	history := &model.QueryHistory{
		ID:          uuid.New(),
		UserID:      userID,
		Name:        &name,
		DatasetID:   template.DatasetID,
		QueryConfig: template.TemplateConfig,
		Status:      "completed",
	}

	// 保存查询历史
	if err := s.queryRepo.SaveQueryHistory(ctx, history); err != nil {
		return nil, err
	}

	return history, nil
}
