package query

import (
	"context"
	"fmt"
	"time"

	"github.com/chungzy/medical-data-platform/internal/adapter"
	"github.com/chungzy/medical-data-platform/internal/model"
	"github.com/chungzy/medical-data-platform/pkg/logger"
)

// EnhancedQueryBuilder 增强查询构建器
type EnhancedQueryBuilder struct {
	adapterManager *adapter.AdapterManager
	cache          QueryCache
	optimizer      *QueryOptimizer
}

// NewEnhancedQueryBuilder 创建增强查询构建器
func NewEnhancedQueryBuilder(adapterManager *adapter.AdapterManager) *EnhancedQueryBuilder {
	return &EnhancedQueryBuilder{
		adapterManager: adapterManager,
		cache:          NewMemoryCache(),
		optimizer:      NewQueryOptimizer(),
	}
}

// BuildAndExecute 构建并执行查询
func (qb *EnhancedQueryBuilder) BuildAndExecute(ctx context.Context, dsl *model.QueryDSL, options *QueryOptions) (*QueryResult, error) {
	startTime := time.Now()

	// 获取适配器
	datasetAdapter, err := qb.adapterManager.GetAdapter(dsl.Dataset)
	if err != nil {
		return nil, fmt.Errorf("failed to get adapter: %w", err)
	}

	// 验证查询
	if err := datasetAdapter.ValidateQuery(dsl); err != nil {
		return nil, fmt.Errorf("query validation failed: %w", err)
	}

	// 检查缓存
	if options.EnableCache {
		if cached := qb.cache.Get(dsl); cached != nil {
			logger.Info("Query cache hit", map[string]interface{}{
				"dataset": dsl.Dataset,
			})
			cached.CacheHit = true
			return cached, nil
		}
	}

	// 优化查询
	optimizedDSL := qb.optimizer.Optimize(dsl)

	// 构建SQL
	sql, args, err := datasetAdapter.BuildQuery(optimizedDSL)
	if err != nil {
		return nil, fmt.Errorf("failed to build query: %w", err)
	}

	// 应用SQL优化
	optimizedSQL := datasetAdapter.OptimizeQuery(sql)

	// 执行查询
	var data []map[string]interface{}
	var total int

	if options.WithCount {
		data, total, err = datasetAdapter.ExecuteQueryWithCount(ctx, optimizedSQL, args)
	} else {
		data, err = datasetAdapter.ExecuteQuery(ctx, optimizedSQL, args)
		total = len(data)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to execute query: %w", err)
	}

	executionTime := int(time.Since(startTime).Milliseconds())

	// 构建结果
	result := &QueryResult{
		Data:          data,
		Total:         total,
		ExecutionTime: executionTime,
		CacheHit:      false,
		SQL:           optimizedSQL,
		Warnings:      qb.generateWarnings(optimizedDSL, executionTime),
	}

	// 缓存结果
	if options.EnableCache && len(data) > 0 {
		qb.cache.Set(dsl, result, options.CacheTimeout)
	}

	return result, nil
}

// BuildQuery 仅构建查询，不执行
func (qb *EnhancedQueryBuilder) BuildQuery(dsl *model.QueryDSL) (*QueryPlan, error) {
	// 获取适配器
	datasetAdapter, err := qb.adapterManager.GetAdapter(dsl.Dataset)
	if err != nil {
		return nil, fmt.Errorf("failed to get adapter: %w", err)
	}

	// 验证查询
	if err := datasetAdapter.ValidateQuery(dsl); err != nil {
		return nil, fmt.Errorf("query validation failed: %w", err)
	}

	// 优化查询
	optimizedDSL := qb.optimizer.Optimize(dsl)

	// 构建SQL
	sql, args, err := datasetAdapter.BuildQuery(optimizedDSL)
	if err != nil {
		return nil, fmt.Errorf("failed to build query: %w", err)
	}

	// 应用SQL优化
	optimizedSQL := datasetAdapter.OptimizeQuery(sql)

	// 估算查询复杂度和行数
	complexity := datasetAdapter.GetQueryComplexity(optimizedDSL)
	estimatedRows := datasetAdapter.EstimateRowCount(optimizedDSL)

	return &QueryPlan{
		SQL:           optimizedSQL,
		Args:          args,
		Complexity:    complexity,
		EstimatedRows: estimatedRows,
		Warnings:      qb.generateWarnings(optimizedDSL, 0),
		Metadata: map[string]interface{}{
			"dataset":        dsl.Dataset,
			"tables":         qb.getInvolvedTables(optimizedDSL),
			"optimization":   qb.getOptimizationSuggestions(optimizedDSL),
		},
	}, nil
}

// ValidateQuery 验证查询
func (qb *EnhancedQueryBuilder) ValidateQuery(dsl *model.QueryDSL) *ValidationResult {
	result := &ValidationResult{
		Valid:    true,
		Errors:   []string{},
		Warnings: []string{},
	}

	// 获取适配器
	datasetAdapter, err := qb.adapterManager.GetAdapter(dsl.Dataset)
	if err != nil {
		result.Valid = false
		result.Errors = append(result.Errors, fmt.Sprintf("Dataset not supported: %s", dsl.Dataset))
		return result
	}

	// 使用适配器验证
	if err := datasetAdapter.ValidateQuery(dsl); err != nil {
		result.Valid = false
		result.Errors = append(result.Errors, err.Error())
	}

	// 添加性能警告
	if len(dsl.Select) > 20 {
		result.Warnings = append(result.Warnings, "Large number of selected fields may impact performance")
	}

	if len(dsl.Joins) > 5 {
		result.Warnings = append(result.Warnings, "Complex joins may impact performance")
	}

	estimatedRows := datasetAdapter.EstimateRowCount(dsl)
	if estimatedRows > 100000 {
		result.Warnings = append(result.Warnings, "Query may return large result set")
	}

	return result
}

// PreviewData 预览数据
func (qb *EnhancedQueryBuilder) PreviewData(ctx context.Context, dataset, table string, limit int) ([]map[string]interface{}, error) {
	// 获取适配器
	datasetAdapter, err := qb.adapterManager.GetAdapter(dataset)
	if err != nil {
		return nil, fmt.Errorf("failed to get adapter: %w", err)
	}

	return datasetAdapter.PreviewData(ctx, table, limit)
}

// GetSampleData 获取样本数据
func (qb *EnhancedQueryBuilder) GetSampleData(ctx context.Context, dataset, table string, fields []string, limit int) ([]map[string]interface{}, error) {
	// 获取适配器
	datasetAdapter, err := qb.adapterManager.GetAdapter(dataset)
	if err != nil {
		return nil, fmt.Errorf("failed to get adapter: %w", err)
	}

	return datasetAdapter.GetSampleData(ctx, table, fields, limit)
}

// GetMetadata 获取元数据
func (qb *EnhancedQueryBuilder) GetMetadata(ctx context.Context, dataset string) (*MetadataInfo, error) {
	// 获取适配器
	datasetAdapter, err := qb.adapterManager.GetAdapter(dataset)
	if err != nil {
		return nil, fmt.Errorf("failed to get adapter: %w", err)
	}

	// 获取表信息
	tables, err := datasetAdapter.GetTables(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get tables: %w", err)
	}

	// 获取字段分类
	categories, err := datasetAdapter.GetFieldCategories(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get field categories: %w", err)
	}

	// 获取所有字段（简化处理，实际应该分表获取）
	var allFields []model.DataField
	for _, table := range tables {
		fields, err := datasetAdapter.GetFields(ctx, table.Name)
		if err != nil {
			logger.Error("Failed to get fields for table", err, map[string]interface{}{
				"dataset": dataset,
				"table":   table.Name,
			})
			continue
		}
		allFields = append(allFields, fields...)
	}

	return &MetadataInfo{
		Tables:     tables,
		Fields:     allFields,
		Categories: categories,
		Statistics: map[string]interface{}{
			"total_tables": len(tables),
			"total_fields": len(allFields),
			"adapter_info": map[string]interface{}{
				"name":        datasetAdapter.GetName(),
				"description": datasetAdapter.GetDescription(),
				"version":     datasetAdapter.GetVersion(),
				"schemas":     datasetAdapter.GetSchemas(),
			},
		},
	}, nil
}

// 辅助方法

func (qb *EnhancedQueryBuilder) generateWarnings(dsl *model.QueryDSL, executionTime int) []string {
	var warnings []string

	if executionTime > 10000 { // 超过10秒
		warnings = append(warnings, "Query execution time is high")
	}

	if len(dsl.Select) > 50 {
		warnings = append(warnings, "Large number of selected fields")
	}

	if len(dsl.Joins) > 10 {
		warnings = append(warnings, "Complex query with many joins")
	}

	return warnings
}

func (qb *EnhancedQueryBuilder) getInvolvedTables(dsl *model.QueryDSL) []string {
	tables := []string{}
	
	if dsl.From != "" {
		tables = append(tables, dsl.From)
	}
	
	for _, join := range dsl.Joins {
		tables = append(tables, join.Table)
	}
	
	return tables
}

func (qb *EnhancedQueryBuilder) getOptimizationSuggestions(dsl *model.QueryDSL) []string {
	suggestions := []string{}

	if len(dsl.Select) > 20 {
		suggestions = append(suggestions, "Consider reducing the number of selected fields")
	}

	if dsl.Where == nil {
		suggestions = append(suggestions, "Consider adding WHERE conditions to improve performance")
	}

	if dsl.Limit == nil || *dsl.Limit > 10000 {
		suggestions = append(suggestions, "Consider adding LIMIT to reduce result set size")
	}

	return suggestions
}
