package app

import (
	"context"
	"database/sql"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"

	"github.com/chungzy/medical-data-platform/internal/adapter"
	"github.com/chungzy/medical-data-platform/internal/api"
	"github.com/chungzy/medical-data-platform/internal/config"
	"github.com/chungzy/medical-data-platform/internal/middleware"
	"github.com/chungzy/medical-data-platform/internal/query"
	"github.com/chungzy/medical-data-platform/internal/repository"
	"github.com/chungzy/medical-data-platform/internal/router"
	"github.com/chungzy/medical-data-platform/internal/service"
	"github.com/chungzy/medical-data-platform/internal/worker"
	"github.com/chungzy/medical-data-platform/pkg/database"
	"github.com/chungzy/medical-data-platform/pkg/jwt"
	"github.com/chungzy/medical-data-platform/pkg/logger"
)

// Application 应用程序结构
type Application struct {
	Config            *config.Config
	DB                *sql.DB
	ConnectionManager *database.ConnectionManager
	AdapterManager    *adapter.AdapterManager
	QueryBuilder      *query.EnhancedQueryBuilder
	Router            *router.Router
	WorkerManager     *worker.WorkerManager
	Server            *http.Server
}

// Initialize 初始化应用程序
func Initialize(cfg *config.Config, db *sql.DB, jwtManager *jwt.JWTManager) *Application {
	logger.Info("Initializing enhanced application components")

	// 初始化连接管理器
	connectionManager, err := database.NewConnectionManager(cfg)
	if err != nil {
		logger.Error("Failed to initialize connection manager", err)
		panic(err)
	}
	logger.Info("Connection manager initialized", map[string]interface{}{
		"enabled_datasources": connectionManager.ListEnabledDatasources(),
	})

	// 初始化适配器管理器
	adapterManager := adapter.NewAdapterManager(db)

	// 注册数据集适配器
	registerAdapters(adapterManager, connectionManager)

	// 初始化查询构建器
	queryBuilder := query.NewEnhancedQueryBuilder(adapterManager)
	logger.Info("Enhanced query builder initialized")

	// 初始化Schema管理器
	dsm := database.NewDataSourceManager()
	// 添加MIMIC-IV数据源
	mimicConfig := database.DataSourceConfig{
		Enabled:     true,
		Type:        "postgresql",
		Host:        cfg.Database.Host,
		Port:        cfg.Database.Port,
		User:        cfg.Database.User,
		Password:    cfg.Database.Password,
		DBName:      cfg.Database.DBName,
		SSLMode:     cfg.Database.SSLMode,
		Description: "MIMIC-IV Database",
	}
	dsm.AddDataSource("mimic-iv", mimicConfig)
	schemaManager := database.NewSchemaManager(dsm)
	logger.Info("Schema manager initialized")

	// 初始化Repository层
	userRepo := repository.NewUserRepository(db)
	queryRepo := repository.NewQueryRepository(db)

	medicalQueryRepo := repository.NewMedicalQueryRepository(db)
	datasetRepo := repository.NewDatasetRepository(db)
	dictionaryRepo := repository.NewDictionaryRepository(db)
	templateRepo := repository.NewTemplateRepository(db)
	exportRepo := repository.NewExportRepository(db)
	analyticsRepo := repository.NewAnalyticsRepository(db)
	schemaRepo := repository.NewSchemaRepository(schemaManager)
	logger.Info("Repository layer initialized")

	// 初始化Service层
	passwordService := service.NewPasswordService(service.GetDefaultPolicy())
	userService := service.NewUserService(userRepo, passwordService)
	permissionService := service.NewSimplePermissionService(userRepo)
	queryService := service.NewQueryService(queryRepo, db)

	// 创建医学字段服务
	fieldService := service.NewMedicalFieldService(schemaRepo)
	medicalQueryService := service.NewMedicalQueryService(medicalQueryRepo, queryRepo, dictionaryRepo, datasetRepo, fieldService)
	datasetService := service.NewDatasetService(datasetRepo)

	dictionaryService := service.NewDictionaryService(dictionaryRepo)
	templateService := service.NewTemplateService(templateRepo, queryRepo)
	exportService := service.NewExportService(queryRepo, exportRepo, queryService)
	historyService := service.NewHistoryService(queryRepo, templateRepo)
	analyticsService := service.NewAnalyticsService(queryRepo, analyticsRepo, queryService)

	// 初始化增强服务
	previewService := service.NewPreviewService(queryBuilder)
	enhancedExportService := service.NewEnhancedExportServiceV2(queryBuilder, exportRepo, queryRepo)
	logger.Info("Service layer initialized")

	// 初始化Controller层
	authController := api.NewAuthController(userService, jwtManager, cfg)
	queryController := api.NewQueryController(queryService)
	medicalQueryController := api.NewMedicalQueryController(medicalQueryService)
	datasetController := api.NewDatasetController(datasetService)
	dictionaryController := api.NewDictionaryController(dictionaryService)
	templateController := api.NewTemplateController(templateService)
	exportController := api.NewExportController(exportService)
	historyController := api.NewHistoryController(historyService)
	analyticsController := api.NewAnalyticsController(analyticsService)

	// 初始化增强控制器
	previewController := api.NewPreviewController(previewService)
	enhancedController := api.NewEnhancedController(queryBuilder, enhancedExportService, exportService)
	systemController := api.NewSystemController(connectionManager, adapterManager)
	logger.Info("Controller layer initialized")

	// 创建路由器
	appRouter := router.NewRouter(
		authController,
		queryController,
		medicalQueryController,
		datasetController,
		dictionaryController,
		templateController,
		exportController,
		historyController,
		analyticsController,
		previewController,
		enhancedController,
		systemController,
		jwtManager,
		permissionService,
	)
	logger.Info("Router initialized")

	// 初始化Worker管理器
	workerManager := worker.InitializeWorkers(cfg)
	logger.Info("Worker manager initialized")

	// 设置Gin模式
	if cfg.Server.Mode == "production" {
		gin.SetMode(gin.ReleaseMode)
		logger.Info("Running in production mode")
	} else {
		logger.Info("Running in development mode")
	}

	// 创建Gin引擎
	ginRouter := gin.New()

	// 设置Gin使用我们的日志写入器
	gin.DefaultWriter = logger.GetGinWriter()
	gin.DefaultErrorWriter = logger.GetGinWriter()

	// 设置中间件
	ginRouter.Use(gin.Recovery())
	ginRouter.Use(middleware.RequestIDMiddleware())
	ginRouter.Use(middleware.LoggingMiddleware())
	ginRouter.Use(middleware.CORSMiddleware())
	ginRouter.Use(middleware.AuthMiddleware(jwtManager))

	// 设置路由
	routerEngine := appRouter.Setup()
	logger.Info("Routes configured")

	// 创建HTTP服务器
	port := cfg.Server.Port
	if port == "" {
		port = "8088"
	}

	server := &http.Server{
		Addr:    ":" + port,
		Handler: routerEngine,
	}

	logger.Info("HTTP server configured", map[string]interface{}{
		"port": port,
	})

	return &Application{
		Config:            cfg,
		DB:                db,
		ConnectionManager: connectionManager,
		AdapterManager:    adapterManager,
		QueryBuilder:      queryBuilder,
		Router:            appRouter,
		WorkerManager:     workerManager,
		Server:            server,
	}
}

// Start 启动应用程序
func (app *Application) Start() error {
	logger.Info("Starting application")

	// 启动所有worker
	if err := app.WorkerManager.StartAll(); err != nil {
		logger.Error("Failed to start workers", map[string]interface{}{
			"error": err.Error(),
		})
		return err
	}

	// 异步启动HTTP服务器
	go func() {
		logger.Info("Starting HTTP server", map[string]interface{}{
			"addr": app.Server.Addr,
		})

		if err := app.Server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Error("HTTP server failed", map[string]interface{}{
				"error": err.Error(),
			})
		}
	}()

	logger.Info("Application started successfully")
	return nil
}

// Shutdown 优雅关闭应用程序
func (app *Application) Shutdown(ctx context.Context) error {
	logger.Info("Shutting down application")

	// 优雅关闭HTTP服务器
	if err := app.Server.Shutdown(ctx); err != nil {
		logger.Error("Failed to shutdown HTTP server", map[string]interface{}{
			"error": err.Error(),
		})
		return err
	}
	logger.Info("HTTP server shutdown completed")

	// 停止所有worker
	app.WorkerManager.StopAll()

	logger.Info("Application shutdown completed")
	return nil
}

// Stop 停止应用程序（保持向后兼容）
func (app *Application) Stop() {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := app.Shutdown(ctx); err != nil {
		logger.Error("Failed to shutdown application gracefully", map[string]interface{}{
			"error": err.Error(),
		})
	}
}

// registerAdapters 注册数据集适配器
func registerAdapters(adapterManager *adapter.AdapterManager, connectionManager *database.ConnectionManager) {
	// 注册启用的数据源适配器
	for _, datasourceName := range connectionManager.ListEnabledDatasources() {
		db, err := connectionManager.GetDatasource(datasourceName)
		if err != nil {
			logger.Error("Failed to get datasource connection", err, map[string]interface{}{
				"datasource": datasourceName,
			})
			continue
		}

		var datasetAdapter adapter.DatasetAdapter
		switch datasourceName {
		case "mimic_iv":
			datasetAdapter = adapter.NewMimicAdapter(db)
		case "eicu":
			// datasetAdapter = adapter.NewEicuAdapter(db) // 待实现
			logger.Info("eICU adapter not implemented yet", map[string]interface{}{
				"datasource": datasourceName,
			})
			continue
		case "nhanes":
			// datasetAdapter = adapter.NewNhanesAdapter(db) // 待实现
			logger.Info("NHANES adapter not implemented yet", map[string]interface{}{
				"datasource": datasourceName,
			})
			continue
		case "pic":
			// datasetAdapter = adapter.NewPicAdapter(db) // 待实现
			logger.Info("PIC adapter not implemented yet", map[string]interface{}{
				"datasource": datasourceName,
			})
			continue
		default:
			logger.Warn("Unknown datasource type", map[string]interface{}{
				"datasource": datasourceName,
			})
			continue
		}

		adapterManager.RegisterAdapter(datasourceName, datasetAdapter)
		logger.Info("Registered adapter", map[string]interface{}{
			"datasource": datasourceName,
			"adapter":    datasetAdapter.GetName(),
		})
	}
}
