-- MIMIC-IV Schema Definition
-- This file contains the standardized schema structure for MIMIC-IV database
-- Used for schema validation and development reference

-- Hospital Schema Tables
-- Core patient and admission information
CREATE SCHEMA IF NOT EXISTS mimiciv_hosp;

-- ICU Schema Tables  
-- Intensive care unit specific data
CREATE SCHEMA IF NOT EXISTS mimiciv_icu;

-- Derived Schema Tables
-- Computed and derived data tables
CREATE SCHEMA IF NOT EXISTS mimiciv_derived;

-- Core Hospital Tables
-- patients: Patient demographics and basic information
-- admissions: Hospital admission records
-- labevents: Laboratory test results
-- diagnoses_icd: ICD diagnosis codes
-- prescriptions: Medication prescriptions
-- procedures_icd: ICD procedure codes

-- Core ICU Tables
-- icustays: ICU stay records
-- chartevents: Charted vital signs and measurements
-- inputevents: Fluid and medication inputs
-- outputevents: Patient outputs (urine, etc.)

-- Table Categories for Field Classification
-- Demographics: patients.subject_id, patients.gender, patients.anchor_age
-- Admission: admissions.hadm_id, admissions.admittime, admissions.dischtime
-- Laboratory: labevents.itemid, labevents.value, labevents.valueuom
-- Diagnosis: diagnoses_icd.icd_code, diagnoses_icd.icd_version
-- Medication: prescriptions.drug, prescriptions.dose_val_rx
-- Vitals: chartevents.itemid, chartevents.value, chartevents.valuenum

-- Common Field Types
-- BIGINT: Primary keys and foreign keys
-- VARCHAR: Text fields and codes
-- TIMESTAMP: Date and time fields
-- NUMERIC: Measurement values
-- INTEGER: Count and categorical fields

-- Key Relationships
-- patients.subject_id -> admissions.subject_id
-- admissions.hadm_id -> diagnoses_icd.hadm_id
-- admissions.hadm_id -> prescriptions.hadm_id
-- icustays.stay_id -> chartevents.stay_id
-- icustays.stay_id -> inputevents.stay_id
