#!/bin/bash

# Medical Data Platform Server - Stop Script
# 医疗数据平台服务器停止脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 配置文件
PID_FILE="$PROJECT_ROOT/medical-platform.pid"
BINARY_NAME="medical-platform"

# 显示帮助信息
show_help() {
    echo "Medical Data Platform Server - Stop Script"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help          显示帮助信息"
    echo "  -f, --force         强制停止（使用SIGKILL）"
    echo "  -d, --docker        停止Docker容器"
    echo "  -p, --production    停止生产环境Docker服务"
    echo "  -t, --timeout SEC   等待超时时间（默认30秒）"
    echo ""
    echo "Examples:"
    echo "  $0                  # 优雅停止本地服务"
    echo "  $0 -f               # 强制停止本地服务"
    echo "  $0 -d               # 停止开发环境Docker服务"
    echo "  $0 -p               # 停止生产环境Docker服务"
}

# 停止本地服务
stop_local_service() {
    local force_kill=${1:-false}
    local timeout=${2:-30}
    
    log_info "正在停止医疗数据平台服务..."
    
    # 检查PID文件是否存在
    if [[ ! -f "$PID_FILE" ]]; then
        log_warning "PID文件不存在: $PID_FILE"
        
        # 尝试通过进程名查找
        local pids=$(pgrep -f "$BINARY_NAME" 2>/dev/null || true)
        if [[ -z "$pids" ]]; then
            log_warning "未找到运行中的服务进程"
            return 0
        else
            log_info "通过进程名找到运行中的服务: PIDs $pids"
            for pid in $pids; do
                stop_process_by_pid "$pid" "$force_kill" "$timeout"
            done
            return 0
        fi
    fi
    
    # 读取PID
    local pid=$(cat "$PID_FILE")
    if [[ -z "$pid" ]]; then
        log_error "PID文件为空"
        return 1
    fi
    
    # 检查进程是否存在
    if ! kill -0 "$pid" 2>/dev/null; then
        log_warning "进程 $pid 不存在，清理PID文件"
        rm -f "$PID_FILE"
        return 0
    fi
    
    # 停止进程
    stop_process_by_pid "$pid" "$force_kill" "$timeout"
    
    # 清理PID文件
    rm -f "$PID_FILE"
    log_success "PID文件已清理"
}

# 根据PID停止进程
stop_process_by_pid() {
    local pid=$1
    local force_kill=${2:-false}
    local timeout=${3:-30}
    
    log_info "正在停止进程 $pid..."
    
    if [[ "$force_kill" == "true" ]]; then
        # 强制停止
        log_warning "强制停止进程 $pid"
        kill -KILL "$pid" 2>/dev/null || true
        sleep 1
    else
        # 优雅停止
        log_info "发送SIGTERM信号到进程 $pid"
        kill -TERM "$pid" 2>/dev/null || true
        
        # 等待进程退出
        local count=0
        while kill -0 "$pid" 2>/dev/null && [[ $count -lt $timeout ]]; do
            sleep 1
            count=$((count + 1))
            if [[ $((count % 5)) -eq 0 ]]; then
                log_info "等待进程退出... ($count/${timeout}s)"
            fi
        done
        
        # 检查进程是否仍在运行
        if kill -0 "$pid" 2>/dev/null; then
            log_warning "进程未在超时时间内退出，发送SIGKILL信号"
            kill -KILL "$pid" 2>/dev/null || true
            sleep 1
        fi
    fi
    
    # 最终检查
    if kill -0 "$pid" 2>/dev/null; then
        log_error "无法停止进程 $pid"
        return 1
    else
        log_success "进程 $pid 已停止"
        return 0
    fi
}

# 停止Docker开发环境
stop_docker_dev() {
    log_info "正在停止Docker开发环境..."
    
    cd "$PROJECT_ROOT"
    
    if [[ -f "docker-compose.yml" ]]; then
        docker-compose down
        log_success "Docker开发环境已停止"
    else
        log_error "docker-compose.yml文件不存在"
        return 1
    fi
}

# 停止Docker生产环境
stop_docker_prod() {
    log_info "正在停止Docker生产环境..."
    
    cd "$PROJECT_ROOT"
    
    if [[ -f "docker-compose.prod.yml" ]]; then
        docker-compose -f docker-compose.prod.yml down
        log_success "Docker生产环境已停止"
    else
        log_error "docker-compose.prod.yml文件不存在"
        return 1
    fi
}

# 主函数
main() {
    local force_kill=false
    local docker_mode=false
    local production_mode=false
    local timeout=30
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -f|--force)
                force_kill=true
                shift
                ;;
            -d|--docker)
                docker_mode=true
                shift
                ;;
            -p|--production)
                production_mode=true
                shift
                ;;
            -t|--timeout)
                timeout="$2"
                if ! [[ "$timeout" =~ ^[0-9]+$ ]]; then
                    log_error "超时时间必须是数字"
                    exit 1
                fi
                shift 2
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 显示停止信息
    echo "========================================"
    echo "  医疗数据平台服务器 - 停止脚本"
    echo "========================================"
    echo "时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "模式: $(if [[ "$production_mode" == "true" ]]; then echo "生产环境Docker"; elif [[ "$docker_mode" == "true" ]]; then echo "开发环境Docker"; else echo "本地服务"; fi)"
    echo "强制停止: $(if [[ "$force_kill" == "true" ]]; then echo "是"; else echo "否"; fi)"
    echo "超时时间: ${timeout}秒"
    echo "========================================"
    
    # 执行停止操作
    if [[ "$production_mode" == "true" ]]; then
        stop_docker_prod
    elif [[ "$docker_mode" == "true" ]]; then
        stop_docker_dev
    else
        stop_local_service "$force_kill" "$timeout"
    fi
    
    log_success "医疗数据平台服务已停止"
}

# 信号处理
trap 'log_error "脚本被中断"; exit 1' INT TERM

# 执行主函数
main "$@" 