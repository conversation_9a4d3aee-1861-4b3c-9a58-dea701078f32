 -- 医疗数据平台数据库建表语句
-- 创建时间: 2024-01-14

-- 1. 用户表
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(100) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20),
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL CHECK (role IN ('student', 'doctor', 'researcher', 'teacher', 'analyst')),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 2. 数据集表
CREATE TABLE IF NOT EXISTS datasets (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    version VARCHAR(20) DEFAULT '1.0',
    patient_count INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'maintenance')),
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 3. 数据集表信息
CREATE TABLE IF NOT EXISTS dataset_tables (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    dataset_id VARCHAR(50) REFERENCES datasets(id) ON DELETE CASCADE,
    table_name VARCHAR(100) NOT NULL,
    display_name VARCHAR(200),
    description TEXT,
    record_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(dataset_id, table_name)
);

-- 4. 数据字段表
CREATE TABLE IF NOT EXISTS data_fields (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    dataset_id VARCHAR(50) REFERENCES datasets(id) ON DELETE CASCADE,
    table_name VARCHAR(100),
    field_name VARCHAR(100) NOT NULL,
    display_name VARCHAR(200),
    description TEXT,
    data_type VARCHAR(50),
    category VARCHAR(100),
    is_required BOOLEAN DEFAULT false,
    default_value TEXT,
    constraints JSONB,
    examples TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(dataset_id, table_name, field_name)
);

-- 5. 查询历史表
CREATE TABLE IF NOT EXISTS query_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(200),
    dataset_id VARCHAR(50) REFERENCES datasets(id) ON DELETE SET NULL,
    query_config JSONB NOT NULL,
    status VARCHAR(20) DEFAULT 'completed' CHECK (status IN ('pending', 'running', 'completed', 'failed')),
    record_count INTEGER DEFAULT 0,
    execution_time INTEGER DEFAULT 0, -- 执行时间(毫秒)
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 6. 查询模板表
CREATE TABLE IF NOT EXISTS query_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    dataset_id VARCHAR(50) REFERENCES datasets(id) ON DELETE SET NULL,
    template_config JSONB NOT NULL,
    is_public BOOLEAN DEFAULT false,
    usage_count INTEGER DEFAULT 0,
    rating DECIMAL(3,2) DEFAULT 0.0,
    author VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 7. 数据导出记录表
CREATE TABLE IF NOT EXISTS export_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    query_id UUID REFERENCES query_history(id) ON DELETE SET NULL,
    export_type VARCHAR(20) NOT NULL CHECK (export_type IN ('csv', 'excel', 'json', 'pdf')),
    file_name VARCHAR(255),
    file_path VARCHAR(500),
    file_size BIGINT DEFAULT 0,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    download_count INTEGER DEFAULT 0,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 8. 用户活动日志表
CREATE TABLE IF NOT EXISTS user_activity_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50),
    resource_id VARCHAR(100),
    details JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 9. 系统配置表
CREATE TABLE IF NOT EXISTS system_configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value JSONB,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 10. 数据集访问权限表
CREATE TABLE IF NOT EXISTS dataset_permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    dataset_id VARCHAR(50) REFERENCES datasets(id) ON DELETE CASCADE,
    permission_type VARCHAR(20) NOT NULL CHECK (permission_type IN ('read', 'write', 'admin')),
    granted_by UUID REFERENCES users(id) ON DELETE SET NULL,
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(user_id, dataset_id)
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active);

CREATE INDEX IF NOT EXISTS idx_datasets_status ON datasets(status);
CREATE INDEX IF NOT EXISTS idx_datasets_last_updated ON datasets(last_updated);

CREATE INDEX IF NOT EXISTS idx_data_fields_dataset_id ON data_fields(dataset_id);
CREATE INDEX IF NOT EXISTS idx_data_fields_category ON data_fields(category);
CREATE INDEX IF NOT EXISTS idx_data_fields_field_name ON data_fields(field_name);

CREATE INDEX IF NOT EXISTS idx_query_history_user_id ON query_history(user_id);
CREATE INDEX IF NOT EXISTS idx_query_history_dataset_id ON query_history(dataset_id);
CREATE INDEX IF NOT EXISTS idx_query_history_status ON query_history(status);
CREATE INDEX IF NOT EXISTS idx_query_history_created_at ON query_history(created_at);

CREATE INDEX IF NOT EXISTS idx_query_templates_user_id ON query_templates(user_id);
CREATE INDEX IF NOT EXISTS idx_query_templates_category ON query_templates(category);
CREATE INDEX IF NOT EXISTS idx_query_templates_is_public ON query_templates(is_public);
CREATE INDEX IF NOT EXISTS idx_query_templates_usage_count ON query_templates(usage_count);

CREATE INDEX IF NOT EXISTS idx_export_records_user_id ON export_records(user_id);
CREATE INDEX IF NOT EXISTS idx_export_records_status ON export_records(status);
CREATE INDEX IF NOT EXISTS idx_export_records_created_at ON export_records(created_at);

CREATE INDEX IF NOT EXISTS idx_user_activity_logs_user_id ON user_activity_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_user_activity_logs_action ON user_activity_logs(action);
CREATE INDEX IF NOT EXISTS idx_user_activity_logs_created_at ON user_activity_logs(created_at);

CREATE INDEX IF NOT EXISTS idx_dataset_permissions_user_id ON dataset_permissions(user_id);
CREATE INDEX IF NOT EXISTS idx_dataset_permissions_dataset_id ON dataset_permissions(dataset_id);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要自动更新updated_at字段的表创建触发器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_query_templates_updated_at BEFORE UPDATE ON query_templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_configs_updated_at BEFORE UPDATE ON system_configs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入一些初始数据
INSERT INTO system_configs (config_key, config_value, description) VALUES
('max_query_results', '10000', '单次查询最大返回结果数'),
('export_file_retention_days', '7', '导出文件保留天数'),
('max_concurrent_queries', '5', '用户最大并发查询数'),
('supported_export_formats', '["csv", "excel", "json", "pdf"]', '支持的导出格式')
ON CONFLICT (config_key) DO NOTHING;

-- 插入示例数据集
INSERT INTO datasets (id, name, description, version, patient_count, status) VALUES
('mimic-iii', 'MIMIC-III Critical Care Database', 'MIMIC-III是一个大型、免费可用的重症监护数据库', '1.4', 46520, 'active'),
('mimic-iv', 'MIMIC-IV Critical Care Database', 'MIMIC-IV是MIMIC-III的更新版本', '2.0', 76540, 'active'),
('eicu', 'eICU Collaborative Research Database', '多中心重症监护数据库', '2.0', 139367, 'active')
ON CONFLICT (id) DO NOTHING;