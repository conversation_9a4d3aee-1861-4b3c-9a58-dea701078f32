"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import {
  HelpCircle,
  Search,
  BookOpen,
  Video,
  FileText,
  ExternalLink,
  ChevronRight,
  Database,
  Users,
  BarChart3,
} from "lucide-react"
import { DashboardLayout } from "@/components/dashboard-layout"

export default function HelpPage() {
  const [searchTerm, setSearchTerm] = useState("")

  const helpCategories = [
    {
      id: "getting-started",
      title: "快速入门",
      icon: BookOpen,
      description: "平台基础使用指南",
      articles: [
        { title: "平台介绍和注册", type: "article", duration: "5分钟" },
        { title: "首次登录设置", type: "video", duration: "3分钟" },
        { title: "界面功能概览", type: "article", duration: "8分钟" },
      ],
    },
    {
      id: "datasets",
      title: "数据集管理",
      icon: Database,
      description: "医学数据库使用指南",
      articles: [
        { title: "MIMIC-IV数据申请流程", type: "article", duration: "15分钟" },
        { title: "EICU数据导入教程", type: "video", duration: "12分钟" },
        { title: "NHANES数据字典说明", type: "article", duration: "10分钟" },
        { title: "PIC数据库特点介绍", type: "article", duration: "8分钟" },
      ],
    },
    {
      id: "query-builder",
      title: "查询构建",
      icon: Search,
      description: "可视化查询工具使用",
      articles: [
        { title: "查询构建器基础操作", type: "video", duration: "10分钟" },
        { title: "复杂条件设置技巧", type: "article", duration: "12分钟" },
        { title: "时间窗口查询方法", type: "article", duration: "8分钟" },
        { title: "多表关联查询", type: "video", duration: "15分钟" },
      ],
    },
    {
      id: "visualization",
      title: "数据可视化",
      icon: BarChart3,
      description: "图表制作和分析",
      articles: [
        { title: "图表类型选择指南", type: "article", duration: "6分钟" },
        { title: "自定义图表样式", type: "video", duration: "8分钟" },
        { title: "数据导出和分享", type: "article", duration: "5分钟" },
      ],
    },
    {
      id: "research",
      title: "科研应用",
      icon: Users,
      description: "学术研究最佳实践",
      articles: [
        { title: "回顾性研究数据提取", type: "article", duration: "20分钟" },
        { title: "队列研究设计要点", type: "video", duration: "18分钟" },
        { title: "统计分析前数据准备", type: "article", duration: "15分钟" },
        { title: "SCI论文数据表制作", type: "article", duration: "12分钟" },
      ],
    },
  ]

  const quickLinks = [
    { title: "数据申请指南", url: "#", type: "external" },
    { title: "常见问题FAQ", url: "#", type: "internal" },
    { title: "视频教程合集", url: "#", type: "external" },
    { title: "用户社区论坛", url: "#", type: "external" },
    { title: "技术支持联系", url: "#", type: "internal" },
  ]

  const tutorials = [
    {
      title: "MIMIC-IV数据库完整教程",
      description: "从申请到分析的完整流程",
      duration: "45分钟",
      level: "初级",
      type: "video-series",
    },
    {
      title: "ICU患者死亡率分析案例",
      description: "实际科研案例演示",
      duration: "30分钟",
      level: "中级",
      type: "case-study",
    },
    {
      title: "心血管疾病风险预测模型",
      description: "机器学习在医学数据中的应用",
      duration: "60分钟",
      level: "高级",
      type: "advanced",
    },
  ]

  const filteredCategories = helpCategories.filter(
    (category) =>
      searchTerm === "" ||
      category.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      category.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      category.articles.some((article) => article.title.toLowerCase().includes(searchTerm.toLowerCase())),
  )

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">帮助中心</h1>
            <p className="text-gray-600 mt-1">学习如何使用医学数据分析平台</p>
          </div>
        </div>

        {/* Search */}
        <Card>
          <CardContent className="pt-6">
            <div className="relative max-w-md mx-auto">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="搜索帮助文档..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </CardContent>
        </Card>

        <Tabs defaultValue="categories" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="categories">分类浏览</TabsTrigger>
            <TabsTrigger value="tutorials">视频教程</TabsTrigger>
            <TabsTrigger value="quick-links">快速链接</TabsTrigger>
          </TabsList>

          <TabsContent value="categories" className="space-y-6">
            {/* Help Categories */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {filteredCategories.map((category) => {
                const Icon = category.icon
                return (
                  <Card key={category.id} className="hover:shadow-lg transition-shadow">
                    <CardHeader>
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-blue-100 rounded-lg">
                          <Icon className="h-6 w-6 text-blue-600" />
                        </div>
                        <div>
                          <CardTitle className="text-lg">{category.title}</CardTitle>
                          <CardDescription>{category.description}</CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {category.articles.map((article, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between p-2 hover:bg-gray-50 rounded-lg cursor-pointer"
                          >
                            <div className="flex items-center space-x-3">
                              {article.type === "video" ? (
                                <Video className="h-4 w-4 text-red-500" />
                              ) : (
                                <FileText className="h-4 w-4 text-blue-500" />
                              )}
                              <div>
                                <p className="text-sm font-medium">{article.title}</p>
                                <p className="text-xs text-gray-500">{article.duration}</p>
                              </div>
                            </div>
                            <ChevronRight className="h-4 w-4 text-gray-400" />
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </TabsContent>

          <TabsContent value="tutorials" className="space-y-6">
            {/* Featured Tutorials */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {tutorials.map((tutorial, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-center justify-between mb-2">
                      <Badge
                        variant={
                          tutorial.level === "初级"
                            ? "secondary"
                            : tutorial.level === "中级"
                              ? "default"
                              : "destructive"
                        }
                      >
                        {tutorial.level}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {tutorial.duration}
                      </Badge>
                    </div>
                    <CardTitle className="text-lg">{tutorial.title}</CardTitle>
                    <CardDescription>{tutorial.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button className="w-full">
                      <Video className="h-4 w-4 mr-2" />
                      开始学习
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* All Tutorials */}
            <Card>
              <CardHeader>
                <CardTitle>所有教程</CardTitle>
                <CardDescription>按主题浏览完整教程列表</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {helpCategories.map((category) => {
                    const Icon = category.icon
                    return (
                      <div key={category.id} className="border rounded-lg p-4">
                        <div className="flex items-center space-x-3 mb-3">
                          <Icon className="h-5 w-5 text-blue-600" />
                          <h3 className="font-medium">{category.title}</h3>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                          {category.articles.map((article, index) => (
                            <div
                              key={index}
                              className="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded cursor-pointer"
                            >
                              {article.type === "video" ? (
                                <Video className="h-3 w-3 text-red-500" />
                              ) : (
                                <FileText className="h-3 w-3 text-blue-500" />
                              )}
                              <span className="text-sm">{article.title}</span>
                              <span className="text-xs text-gray-500 ml-auto">{article.duration}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="quick-links" className="space-y-6">
            {/* Quick Links */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {quickLinks.map((link, index) => (
                <Card key={index} className="hover:shadow-md transition-shadow cursor-pointer">
                  <CardContent className="pt-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-gray-100 rounded-lg">
                          {link.type === "external" ? (
                            <ExternalLink className="h-4 w-4 text-gray-600" />
                          ) : (
                            <HelpCircle className="h-4 w-4 text-gray-600" />
                          )}
                        </div>
                        <span className="font-medium">{link.title}</span>
                      </div>
                      <ChevronRight className="h-4 w-4 text-gray-400" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Contact Support */}
            <Card>
              <CardHeader>
                <CardTitle>需要更多帮助？</CardTitle>
                <CardDescription>如果您在使用过程中遇到问题，可以通过以下方式获取支持</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 border rounded-lg">
                    <h4 className="font-medium mb-2">技术支持</h4>
                    <p className="text-sm text-gray-600 mb-3">遇到技术问题或bug，请联系我们的技术团队</p>
                    <Button variant="outline" size="sm">
                      提交工单
                    </Button>
                  </div>

                  <div className="p-4 border rounded-lg">
                    <h4 className="font-medium mb-2">用户社区</h4>
                    <p className="text-sm text-gray-600 mb-3">与其他用户交流经验，分享使用技巧</p>
                    <Button variant="outline" size="sm">
                      <ExternalLink className="h-3 w-3 mr-1" />
                      访问论坛
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
