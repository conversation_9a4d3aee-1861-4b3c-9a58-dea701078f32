package middleware

import (
	"net/http"

	"github.com/chungzy/medical-data-platform/internal/model"
	"github.com/chungzy/medical-data-platform/internal/service"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// RequirePermissionLevel 权限级别检查中间件
func RequirePermissionLevel(permissionService *service.SimplePermissionService, minLevel int) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查开发模式
		devMode := "true"    //  os.Getenv("DEV_MODE")
		bypassAuth := "true" // .. os.Getenv("BYPASS_AUTH")
		if devMode == "true" && bypassAuth == "true" {
			// 开发模式：绕过权限检查
			c.Next()
			return
		}

		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "user not authenticated"})
			c.Abort()
			return
		}

		uid, ok := userID.(uuid.UUID)
		if !ok {
			c.<PERSON>(http.StatusUnauthorized, gin.H{"error": "invalid user ID"})
			c.Abort()
			return
		}

		userLevel, err := permissionService.GetUserPermissionLevel(c.Request.Context(), uid)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "permission check failed"})
			c.Abort()
			return
		}

		// 检查权限级别
		hasPermission := false
		switch minLevel {
		case model.PermissionAdmin:
			// 需要管理员权限
			hasPermission = userLevel == model.PermissionAdmin
		case model.PermissionUser:
			// 需要普通用户或管理员权限
			hasPermission = userLevel == model.PermissionUser || userLevel == model.PermissionAdmin
		default:
			// 默认需要至少普通用户权限
			hasPermission = userLevel >= model.PermissionUser
		}

		if !hasPermission {
			c.JSON(http.StatusForbidden, gin.H{
				"error":          "insufficient permissions",
				"required_level": minLevel,
				"user_level":     userLevel,
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireAdmin 管理员权限中间件
func RequireAdmin(permissionService *service.SimplePermissionService) gin.HandlerFunc {
	return RequirePermissionLevel(permissionService, model.PermissionAdmin)
}

// RequireUser 普通用户权限中间件
func RequireUser(permissionService *service.SimplePermissionService) gin.HandlerFunc {
	return RequirePermissionLevel(permissionService, model.PermissionUser)
}

// RequireAccess 基础访问权限中间件
func RequireAccess(permissionService *service.SimplePermissionService) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "user not authenticated"})
			c.Abort()
			return
		}

		uid, ok := userID.(uuid.UUID)
		if !ok {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "invalid user ID"})
			c.Abort()
			return
		}

		hasAccess, err := permissionService.HasAccess(c.Request.Context(), uid)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "access check failed"})
			c.Abort()
			return
		}

		if !hasAccess {
			c.JSON(http.StatusForbidden, gin.H{"error": "access denied"})
			c.Abort()
			return
		}

		c.Next()
	}
}

// CheckAction 检查特定操作权限
func CheckAction(permissionService *service.SimplePermissionService, action string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "user not authenticated"})
			c.Abort()
			return
		}

		uid, ok := userID.(uuid.UUID)
		if !ok {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "invalid user ID"})
			c.Abort()
			return
		}

		canPerform, err := permissionService.CheckPermission(c.Request.Context(), uid, action)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "permission check failed"})
			c.Abort()
			return
		}

		if !canPerform {
			c.JSON(http.StatusForbidden, gin.H{
				"error":  "insufficient permissions for action",
				"action": action,
			})
			c.Abort()
			return
		}

		c.Next()
	}
}
