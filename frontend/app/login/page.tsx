"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Database, Mail, Lock, User, Phone } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useAuth } from "@/lib/hooks/use-auth"
import { toast } from "sonner"
import { PasswordStrength, PasswordConfirm } from "@/components/auth/password-strength"

export default function LoginPage() {
  // 检查开发模式
  const isDevMode = process.env.NEXT_PUBLIC_DEV_MODE === 'true'
  const bypassAuth = process.env.NEXT_PUBLIC_BYPASS_AUTH === 'true'

  const [loginForm, setLoginForm] = useState({
    email: isDevMode && bypassAuth ? "<EMAIL>" : "",
    password: isDevMode && bypassAuth ? "dev123456" : "",
  })

  const [registerForm, setRegisterForm] = useState({
    name: "",
    email: "",
    phone: "",
    password: "",
    confirmPassword: "",
    role: "user",
  })

  const [loading, setLoading] = useState(false)
  const router = useRouter()
  const { login, register } = useAuth()

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const result = await login(loginForm.email, loginForm.password)
      if (result.success) {
        toast.success("登录成功！")
        router.push("/dashboard")
      } else {
        toast.error(result.error || "登录失败")
      }
    } catch (error) {
      toast.error("网络错误，请稍后重试")
    } finally {
      setLoading(false)
    }
  }

  // 开发模式快速登录
  const handleDevLogin = async () => {
    setLoading(true)
    try {
      const result = await login("<EMAIL>", "")
      if (result.success) {
        toast.success("开发模式登录成功！")
        router.push("/dashboard")
      } else {
        toast.error("开发模式登录失败")
      }
    } catch (error) {
      toast.error("登录失败")
    } finally {
      setLoading(false)
    }
  }

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault()

    if (registerForm.password !== registerForm.confirmPassword) {
      toast.error("两次输入的密码不一致")
      return
    }

    if (registerForm.password.length < 6) {
      toast.error("密码长度至少6位")
      return
    }

    setLoading(true)

    try {
      const result = await register({
        name: registerForm.name,
        email: registerForm.email,
        phone: registerForm.phone || undefined,
        password: registerForm.password,
        role: registerForm.role,
      })

      if (result.success) {
        toast.success("注册成功！")
        router.push("/dashboard")
      } else {
        toast.error(result.error || "注册失败")
      }
    } catch (error) {
      toast.error("网络错误，请稍后重试")
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* 开发模式提示 */}
        {isDevMode && bypassAuth && (
          <div className="mb-4 p-3 bg-yellow-100 border border-yellow-300 rounded-lg">
            <div className="flex items-center space-x-2">
              <span className="text-yellow-600">🚀</span>
              <span className="text-sm font-medium text-yellow-800">开发模式已启用</span>
            </div>
            <p className="text-xs text-yellow-700 mt-1">
              可以使用快速登录功能，或者使用预填充的账号密码
            </p>
          </div>
        )}

        {/* Logo */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <Database className="h-8 w-8 text-blue-600" />
            <h1 className="text-2xl font-bold text-gray-900">MedData Analytics</h1>
          </div>
          <p className="text-gray-600">医学数据分析平台</p>
        </div>

        <Tabs defaultValue="login" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="login">登录</TabsTrigger>
            <TabsTrigger value="register">注册</TabsTrigger>
          </TabsList>

          <TabsContent value="login">
            <Card>
              <CardHeader>
                <CardTitle>登录账户</CardTitle>
                <CardDescription>使用您的邮箱和密码登录</CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleLogin} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="email">邮箱</Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="email"
                        type="email"
                        placeholder="<EMAIL>"
                        className="pl-10"
                        value={loginForm.email}
                        onChange={(e) => setLoginForm({ ...loginForm, email: e.target.value })}
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="password">密码</Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="password"
                        type="password"
                        placeholder="••••••••"
                        className="pl-10"
                        value={loginForm.password}
                        onChange={(e) => setLoginForm({ ...loginForm, password: e.target.value })}
                        required
                      />
                    </div>
                  </div>

                  <Button type="submit" className="w-full" disabled={loading}>
                    {loading ? "登录中..." : "登录"}
                  </Button>

                  {/* 开发模式快速登录 */}
                  {isDevMode && bypassAuth && (
                    <div className="space-y-2">
                      <div className="relative">
                        <div className="absolute inset-0 flex items-center">
                          <span className="w-full border-t border-gray-300" />
                        </div>
                        <div className="relative flex justify-center text-xs uppercase">
                          <span className="bg-white px-2 text-gray-500">开发模式</span>
                        </div>
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        className="w-full border-yellow-300 text-yellow-700 hover:bg-yellow-50"
                        onClick={handleDevLogin}
                        disabled={loading}
                      >
                        🚀 快速登录（免密码）
                      </Button>
                      <p className="text-xs text-center text-yellow-600">
                        开发环境专用，自动获得管理员权限
                      </p>
                    </div>
                  )}

                  <div className="text-center">
                    <Link href="/forgot-password" className="text-sm text-blue-600 hover:underline">
                      忘记密码？
                    </Link>
                  </div>
                </form>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="register">
            <Card>
              <CardHeader>
                <CardTitle>创建账户</CardTitle>
                <CardDescription>填写信息创建新账户</CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleRegister} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">姓名</Label>
                    <div className="relative">
                      <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="name"
                        placeholder="您的姓名"
                        className="pl-10"
                        value={registerForm.name}
                        onChange={(e) => setRegisterForm({ ...registerForm, name: e.target.value })}
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="reg-email">邮箱</Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="reg-email"
                        type="email"
                        placeholder="<EMAIL>"
                        className="pl-10"
                        value={registerForm.email}
                        onChange={(e) => setRegisterForm({ ...registerForm, email: e.target.value })}
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone">手机号</Label>
                    <div className="relative">
                      <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="phone"
                        placeholder="手机号码"
                        className="pl-10"
                        value={registerForm.phone}
                        onChange={(e) => setRegisterForm({ ...registerForm, phone: e.target.value })}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="role">用户角色</Label>
                    <select
                      className="w-full p-2 border border-gray-300 rounded-md"
                      value={registerForm.role}
                      onChange={(e) => setRegisterForm({ ...registerForm, role: e.target.value })}
                    >
                      <option value="user">普通用户</option>
                      <option value="admin">管理员</option>
                    </select>
                  </div>

                  <PasswordStrength
                    value={registerForm.password}
                    onChange={(value) => setRegisterForm({ ...registerForm, password: value })}
                    label="密码"
                    placeholder="请输入密码"
                    showStrength={true}
                    showRequirements={true}
                  />

                  <PasswordConfirm
                    password={registerForm.password}
                    confirmPassword={registerForm.confirmPassword}
                    onConfirmPasswordChange={(value) => setRegisterForm({ ...registerForm, confirmPassword: value })}
                  />

                  <Button type="submit" className="w-full" disabled={loading}>
                    {loading ? "注册中..." : "注册"}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="text-center mt-6 text-sm text-gray-600">
          <Link href="/" className="hover:text-blue-600">
            返回首页
          </Link>
        </div>
      </div>
    </div>
  )
}
