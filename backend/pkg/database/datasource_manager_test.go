package database

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewDataSourceManager(t *testing.T) {
	dsm := NewDataSourceManager()
	
	assert.NotNil(t, dsm)
	assert.NotNil(t, dsm.connections)
	assert.NotNil(t, dsm.configs)
	assert.NotNil(t, dsm.statuses)
	assert.Equal(t, 0, len(dsm.connections))
}

func TestAddDataSource_Disabled(t *testing.T) {
	dsm := NewDataSourceManager()
	
	config := DataSourceConfig{
		Enabled:     false,
		Description: "Test disabled datasource",
	}
	
	err := dsm.AddDataSource("test-disabled", config)
	assert.NoError(t, err)
	
	// 检查状态
	status, exists := dsm.GetStatus("test-disabled")
	assert.True(t, exists)
	assert.False(t, status.IsHealthy)
	assert.Equal(t, "Data source is disabled", status.ErrorMsg)
	
	// 检查连接不存在
	_, err = dsm.GetConnection("test-disabled")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "data source not found")
}

func TestAddDataSource_InvalidConfig(t *testing.T) {
	dsm := NewDataSourceManager()
	
	config := DataSourceConfig{
		Enabled:           true,
		Type:              "postgres",
		Host:              "invalid-host",
		Port:              5432,
		User:              "test",
		Password:          "test",
		DBName:            "test",
		SSLMode:           "disable",
		ConnectionTimeout: 1, // 1秒超时，确保快速失败
	}
	
	err := dsm.AddDataSource("test-invalid", config)
	assert.Error(t, err)
	
	// 检查状态记录了错误
	status, exists := dsm.GetStatus("test-invalid")
	assert.True(t, exists)
	assert.False(t, status.IsHealthy)
	assert.NotEmpty(t, status.ErrorMsg)
}

func TestGetAllStatuses(t *testing.T) {
	dsm := NewDataSourceManager()
	
	// 添加一个禁用的数据源
	config1 := DataSourceConfig{
		Enabled:     false,
		Description: "Disabled source",
	}
	err := dsm.AddDataSource("disabled", config1)
	require.NoError(t, err)
	
	// 添加一个无效的数据源
	config2 := DataSourceConfig{
		Enabled:           true,
		Type:              "postgres",
		Host:              "invalid-host",
		Port:              5432,
		User:              "test",
		Password:          "test",
		DBName:            "test",
		SSLMode:           "disable",
		ConnectionTimeout: 1,
	}
	err = dsm.AddDataSource("invalid", config2)
	assert.Error(t, err) // 预期失败
	
	// 获取所有状态
	statuses := dsm.GetAllStatuses()
	assert.Equal(t, 2, len(statuses))
	
	// 检查禁用的数据源
	disabledStatus := statuses["disabled"]
	assert.False(t, disabledStatus.IsHealthy)
	assert.Equal(t, "Data source is disabled", disabledStatus.ErrorMsg)
	
	// 检查无效的数据源
	invalidStatus := statuses["invalid"]
	assert.False(t, invalidStatus.IsHealthy)
	assert.NotEmpty(t, invalidStatus.ErrorMsg)
}

func TestIsDataSourceHealthy(t *testing.T) {
	dsm := NewDataSourceManager()
	
	// 测试不存在的数据源
	assert.False(t, dsm.IsDataSourceHealthy("nonexistent"))
	
	// 添加禁用的数据源
	config := DataSourceConfig{
		Enabled:     false,
		Description: "Test source",
	}
	err := dsm.AddDataSource("test", config)
	require.NoError(t, err)
	
	// 测试禁用的数据源
	assert.False(t, dsm.IsDataSourceHealthy("test"))
}

func TestGetHealthyDataSources(t *testing.T) {
	dsm := NewDataSourceManager()
	
	// 初始状态应该没有健康的数据源
	healthy := dsm.GetHealthyDataSources()
	assert.Equal(t, 0, len(healthy))
	
	// 添加一些数据源
	config1 := DataSourceConfig{
		Enabled:     false,
		Description: "Disabled source",
	}
	err := dsm.AddDataSource("disabled", config1)
	require.NoError(t, err)
	
	config2 := DataSourceConfig{
		Enabled:           true,
		Type:              "postgres",
		Host:              "invalid-host",
		Port:              5432,
		User:              "test",
		Password:          "test",
		DBName:            "test",
		SSLMode:           "disable",
		ConnectionTimeout: 1,
	}
	err = dsm.AddDataSource("invalid", config2)
	assert.Error(t, err)
	
	// 仍然应该没有健康的数据源
	healthy = dsm.GetHealthyDataSources()
	assert.Equal(t, 0, len(healthy))
}

func TestHealthCheck_NonexistentDataSource(t *testing.T) {
	dsm := NewDataSourceManager()
	
	err := dsm.HealthCheck("nonexistent")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "data source not found")
}

func TestClose(t *testing.T) {
	dsm := NewDataSourceManager()
	
	// 添加一些数据源
	config := DataSourceConfig{
		Enabled:     false,
		Description: "Test source",
	}
	err := dsm.AddDataSource("test", config)
	require.NoError(t, err)
	
	// 关闭管理器
	dsm.Close()
	
	// 检查所有映射都被清空
	assert.Equal(t, 0, len(dsm.connections))
	assert.Equal(t, 0, len(dsm.configs))
	assert.Equal(t, 0, len(dsm.statuses))
}

func TestDataSourceStatus_Fields(t *testing.T) {
	status := DataSourceStatus{
		ID:          "test-id",
		Name:        "Test DataSource",
		IsHealthy:   true,
		LastCheck:   time.Now(),
		ErrorMsg:    "",
		Description: "Test description",
	}
	
	assert.Equal(t, "test-id", status.ID)
	assert.Equal(t, "Test DataSource", status.Name)
	assert.True(t, status.IsHealthy)
	assert.Empty(t, status.ErrorMsg)
	assert.Equal(t, "Test description", status.Description)
	assert.False(t, status.LastCheck.IsZero())
}

func TestDataSourceConfig_Fields(t *testing.T) {
	config := DataSourceConfig{
		Enabled:           true,
		Type:              "postgres",
		Host:              "localhost",
		Port:              5432,
		User:              "testuser",
		Password:          "testpass",
		DBName:            "testdb",
		SSLMode:           "disable",
		Schema:            "public",
		Description:       "Test database",
		MaxConnections:    10,
		ConnectionTimeout: 30,
		QueryTimeout:      60,
	}
	
	assert.True(t, config.Enabled)
	assert.Equal(t, "postgres", config.Type)
	assert.Equal(t, "localhost", config.Host)
	assert.Equal(t, 5432, config.Port)
	assert.Equal(t, "testuser", config.User)
	assert.Equal(t, "testpass", config.Password)
	assert.Equal(t, "testdb", config.DBName)
	assert.Equal(t, "disable", config.SSLMode)
	assert.Equal(t, "public", config.Schema)
	assert.Equal(t, "Test database", config.Description)
	assert.Equal(t, 10, config.MaxConnections)
	assert.Equal(t, 30, config.ConnectionTimeout)
	assert.Equal(t, 60, config.QueryTimeout)
}
