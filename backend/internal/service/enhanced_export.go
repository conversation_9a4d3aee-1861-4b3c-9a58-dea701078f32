package service

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"

	"github.com/chungzy/medical-data-platform/internal/model"
	"github.com/chungzy/medical-data-platform/internal/repository"
)

type EnhancedExportService interface {
	CreateEnhancedExport(userID, queryID, format string, options map[string]interface{}) (*model.ExportRecord, error)
	GetExportProgress(exportID string) (*ExportProgress, error)
}

type enhancedExportService struct {
	exportRepo repository.ExportRepository
	queryRepo  repository.QueryRepository
}

// 导出进度结构
type ExportProgress struct {
	ID        string     `json:"id"`
	Status    string     `json:"status"`
	Progress  int        `json:"progress"`
	StartTime time.Time  `json:"startTime"`
	EndTime   *time.Time `json:"endTime,omitempty"`
	Error     string     `json:"error,omitempty"`
	FilePath  string     `json:"filePath,omitempty"`
	FileSize  int64      `json:"fileSize,omitempty"`
}

func NewEnhancedExportService(exportRepo repository.ExportRepository, queryRepo repository.QueryRepository) EnhancedExportService {
	return &enhancedExportService{
		exportRepo: exportRepo,
		queryRepo:  queryRepo,
	}
}

// 创建增强导出任务
func (s *enhancedExportService) CreateEnhancedExport(userID, queryID, format string, options map[string]interface{}) (*model.ExportRecord, error) {
	ctx := context.Background()

	// 创建导出记录
	exportRecord := &model.ExportRecord{
		ID:         uuid.New(),
		UserID:     uuid.MustParse(userID),
		ExportType: format,
		Status:     "pending",
		ExpiresAt:  time.Now().Add(7 * 24 * time.Hour), // 7天后过期
		CreatedAt:  time.Now(),
	}

	// 如果queryID不为空，设置QueryID
	if queryID != "" {
		queryUUID := uuid.MustParse(queryID)
		exportRecord.QueryID = &queryUUID
	}

	// 保存导出记录
	if err := s.exportRepo.Create(ctx, exportRecord); err != nil {
		return nil, fmt.Errorf("failed to create export record: %w", err)
	}

	// 启动异步导出任务
	go s.processEnhancedExport(exportRecord, options)

	return exportRecord, nil
}

// 处理增强导出任务
func (s *enhancedExportService) processEnhancedExport(record *model.ExportRecord, options map[string]interface{}) {
	ctx := context.Background()

	// 更新状态为处理中
	record.Status = "processing"
	s.exportRepo.Update(ctx, record)

	// 模拟导出过程
	time.Sleep(2 * time.Second)

	// 模拟导出成功
	record.Status = "completed"
	filePath := fmt.Sprintf("exports/enhanced_%s.%s", record.ID.String(), record.ExportType)
	record.FilePath = &filePath
	fileName := fmt.Sprintf("enhanced_export_%s.%s", record.ID.String(), record.ExportType)
	record.FileName = &fileName
	var fileSize int64 = 1024 // 模拟文件大小
	record.FileSize = &fileSize

	s.exportRepo.Update(ctx, record)
}

// 获取导出进度
func (s *enhancedExportService) GetExportProgress(exportID string) (*ExportProgress, error) {
	ctx := context.Background()
	exportUUID := uuid.MustParse(exportID)

	record, err := s.exportRepo.GetByID(ctx, exportUUID)
	if err != nil {
		return nil, err
	}

	progress := &ExportProgress{
		ID:        record.ID.String(),
		Status:    record.Status,
		StartTime: record.CreatedAt,
	}

	// 根据状态设置进度
	switch record.Status {
	case "pending":
		progress.Progress = 0
	case "processing":
		progress.Progress = 50
	case "completed":
		progress.Progress = 100
		endTime := time.Now()
		progress.EndTime = &endTime
		if record.FilePath != nil {
			progress.FilePath = *record.FilePath
		}
		if record.FileSize != nil {
			progress.FileSize = *record.FileSize
		}
	case "failed":
		progress.Progress = 0
		progress.Error = "导出失败"
	}

	return progress, nil
}
