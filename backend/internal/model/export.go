package model

import (
	"time"

	"github.com/google/uuid"
)

type ExportRecord struct {
	ID            uuid.UUID  `json:"id" db:"id"`
	UserID        uuid.UUID  `json:"user_id" db:"user_id"`
	QueryID       *uuid.UUID `json:"query_id,omitempty" db:"query_id"`
	ExportType    string     `json:"export_type" db:"export_type"`
	FileName      *string    `json:"file_name,omitempty" db:"file_name"`
	FilePath      *string    `json:"file_path,omitempty" db:"file_path"`
	FileSize      *int64     `json:"file_size,omitempty" db:"file_size"`
	Status        string     `json:"status" db:"status"`
	ErrorMessage  *string    `json:"error_message,omitempty" db:"error_message"`
	DownloadCount int        `json:"download_count" db:"download_count"`
	ExpiresAt     time.Time  `json:"expires_at" db:"expires_at"`
	CreatedAt     time.Time  `json:"created_at" db:"created_at"`
}
