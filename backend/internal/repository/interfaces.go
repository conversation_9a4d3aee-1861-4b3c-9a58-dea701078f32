package repository

import (
	"context"

	"github.com/chungzy/medical-data-platform/internal/model"

	"github.com/google/uuid"
)

// UserRepository 用户数据访问接口
type UserRepository interface {
	Create(ctx context.Context, user *model.User) error
	GetByID(ctx context.Context, id uuid.UUID) (*model.User, error)
	GetByEmail(ctx context.Context, email string) (*model.User, error)
	Update(ctx context.Context, user *model.User) error
	Delete(ctx context.Context, id uuid.UUID) error
}

// DatasetRepository 数据集数据访问接口
type DatasetRepository interface {
	GetAll(ctx context.Context) ([]model.Dataset, error)
	GetByID(ctx context.Context, id string) (*model.Dataset, error)
	GetFields(ctx context.Context, datasetID string) ([]model.DataField, error)
	GetTables(ctx context.Context, datasetID string) ([]model.DatasetTable, error)
}

// DictionaryRepository 数据字典数据访问接口
type DictionaryRepository interface {
	SearchFields(ctx context.Context, query, dataset, category string, limit, offset int) ([]model.DataField, int, error)
	GetCategories(ctx context.Context, dataset string) ([]model.FieldCategory, error)
	GetFieldByID(ctx context.Context, id string) (*model.DataField, error)
}

// QueryRepository 查询数据访问接口
type QueryRepository interface {
	ExecuteQuery(ctx context.Context, sql string, args []interface{}) ([]map[string]interface{}, error)
	ExecuteQueryWithCount(ctx context.Context, sql string, args []interface{}) ([]map[string]interface{}, int, error)
	SaveQueryHistory(ctx context.Context, history *model.QueryHistory) error
	GetQueryHistory(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.QueryHistory, int, error)
	GetQueryHistoryByID(ctx context.Context, id uuid.UUID) (*model.QueryHistory, error)
	DeleteQueryHistory(ctx context.Context, id uuid.UUID) error
}

// TemplateRepository 查询模板数据访问接口
type TemplateRepository interface {
	Create(ctx context.Context, template *model.QueryTemplate) error
	GetAll(ctx context.Context, category, dataset string, limit, offset int) ([]model.QueryTemplate, int, error)
	GetByID(ctx context.Context, id uuid.UUID) (*model.QueryTemplate, error)
	GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.QueryTemplate, int, error)
	Update(ctx context.Context, template *model.QueryTemplate) error
	Delete(ctx context.Context, id uuid.UUID) error
	IncrementUsage(ctx context.Context, id uuid.UUID) error
}

// AnalyticsRepository 统计分析数据访问接口
type AnalyticsRepository interface {
	GetBasicStats(ctx context.Context, sql string, args []interface{}, fields []string) (map[string]interface{}, error)
	GetAggregatedData(ctx context.Context, sql string, args []interface{}, groupBy string) ([]map[string]interface{}, error)
}

// ExportRepository 导出仓库接口
type ExportRepository interface {
	Create(ctx context.Context, record *model.ExportRecord) error
	GetByID(ctx context.Context, id uuid.UUID) (*model.ExportRecord, error)
	Update(ctx context.Context, record *model.ExportRecord) error
	IncrementDownloadCount(ctx context.Context, id uuid.UUID) error
	GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.ExportRecord, int, error)
	DeleteExpired(ctx context.Context) error
}
