# 高级查询构建器技术实现

## 🏗️ 架构概览

### 组件架构
```
AdvancedQueryBuilder (主容器)
├── DatasetSelector (数据集选择)
├── CohortBuilder (人群构建)
├── FieldSelector (字段选择)
├── FilterBuilder (过滤器构建)
├── QueryPreview (查询预览)
└── QueryResults (结果展示)
```

### 数据流
```
用户交互 → 状态更新 → DSL构建 → API调用 → 结果展示
```

## 📦 核心组件

### 1. AdvancedQueryBuilder
**文件**: `components/query-builder/advanced-query-builder.tsx`

**职责**:
- 管理全局查询状态
- 协调各个子组件
- 处理步骤导航逻辑
- 执行查询和保存操作

**关键状态**:
```typescript
interface QueryBuilderState {
  selectedDataset: Dataset | null
  cohort: any
  selectedFields: SelectedField[]
  filters: FilterCondition[]
  timeRange: any
  groupBy: string[]
  orderBy: any[]
  limit: number
}
```

### 2. DatasetSelector
**文件**: `components/query-builder/dataset-selector.tsx`

**职责**:
- 展示可用数据集
- 提供搜索和过滤功能
- 显示数据集详细信息
- 处理数据集选择

**特性**:
- 响应式网格布局
- 状态指示器（健康/警告/错误）
- 访问级别标识
- 特性标签展示

### 3. CohortBuilder
**文件**: `components/query-builder/cohort-builder.tsx`

**职责**:
- 构建研究人群筛选条件
- 支持多种条件类型
- 提供人群规模预估
- 管理条件逻辑关系

**条件类型**:
- 人口统计学（年龄、性别）
- 时间条件（入院时间、住院时长）
- 临床条件（生命体征、实验室指标）

### 4. FieldSelector
**文件**: `components/query-builder/field-selector.tsx`

**职责**:
- 按分类展示可用字段
- 提供字段搜索和过滤
- 管理字段选择状态
- 显示字段详细信息

**字段分类**:
```typescript
const categories = [
  "demographics",      // 人口统计学
  "temporal",          // 时间信息
  "clinical_measurements", // 临床测量
  "diagnoses",         // 诊断信息
  "medications",       // 药物治疗
  "procedures",        // 医疗程序
  "administrative"     // 管理信息
]
```

### 5. FilterBuilder
**文件**: `components/query-builder/filter-builder.tsx`

**职责**:
- 构建复杂过滤条件
- 支持多种数据类型和操作符
- 管理逻辑组合（AND/OR）
- 提供SQL预览

**操作符支持**:
```typescript
const operators = {
  string: ["=", "!=", "LIKE", "NOT_LIKE", "IN", "NOT_IN", "IS_NULL", "IS_NOT_NULL"],
  number: ["=", "!=", ">", ">=", "<", "<=", "BETWEEN", "NOT_BETWEEN", "IS_NULL", "IS_NOT_NULL"],
  date: ["=", "!=", ">", ">=", "<", "<=", "BETWEEN", "IS_NULL", "IS_NOT_NULL"],
  boolean: ["=", "IS_NULL", "IS_NOT_NULL"]
}
```

### 6. QueryPreview
**文件**: `components/query-builder/query-preview.tsx`

**职责**:
- 生成和展示查询DSL
- 提供SQL预览
- 分析查询复杂度
- 验证查询有效性

**复杂度评估**:
```typescript
const calculateComplexity = (queryDSL) => {
  let score = 0
  score += (queryDSL.fields?.length || 0) > 20 ? 3 : 
           (queryDSL.fields?.length || 0) > 10 ? 2 : 1
  score += (queryDSL.filters?.length || 0) > 10 ? 3 : 
           (queryDSL.filters?.length || 0) > 5 ? 2 : 1
  // ... 更多评估逻辑
  return { score, level: score >= 8 ? "复杂" : score >= 5 ? "中等" : "简单" }
}
```

### 7. QueryResults
**文件**: `components/query-builder/query-results.tsx`

**职责**:
- 展示查询结果数据
- 提供搜索、排序、分页
- 生成统计信息
- 支持多种导出格式

**结果处理**:
```typescript
const processData = (data, searchTerm, sortField, sortDirection) => {
  let filtered = data.filter(row => 
    Object.values(row).some(value => 
      String(value).toLowerCase().includes(searchTerm.toLowerCase())
    )
  )
  
  if (sortField) {
    filtered.sort((a, b) => {
      const comparison = a[sortField] < b[sortField] ? -1 : 1
      return sortDirection === "asc" ? comparison : -comparison
    })
  }
  
  return filtered
}
```

## 🔌 API 集成

### API 客户端
**文件**: `lib/api.ts`

**新增方法**:
```typescript
// 医学查询相关
async getFieldCategories(datasetId?: string)
async getTableFields(datasetId: string, tableName: string)
async executeMedicalQuery(queryDSL: any)
async saveMedicalQuery(queryDSL: any)
async getMedicalQueryHistory(limit = 10, offset = 0)
async getMedicalQueryResult(queryId: string)
```

### 错误处理策略
```typescript
const handleApiCall = async (apiCall, fallback) => {
  try {
    const response = await apiCall()
    return response.data
  } catch (error) {
    console.warn("API调用失败，使用降级方案:", error)
    return fallback()
  }
}
```

## 🎨 UI/UX 设计

### 设计系统
- **颜色方案**: 基于 Tailwind CSS 的一致性配色
- **图标库**: Lucide React 图标
- **组件库**: Shadcn/ui 组件
- **字体**: 系统默认字体栈

### 响应式设计
```css
/* 移动端优先 */
.query-builder {
  @apply grid grid-cols-1;
}

/* 平板端 */
@media (min-width: 768px) {
  .query-builder {
    @apply grid-cols-2;
  }
}

/* 桌面端 */
@media (min-width: 1024px) {
  .query-builder {
    @apply grid-cols-3;
  }
}
```

### 交互设计
- **渐进式披露**: 复杂功能分步展示
- **即时反馈**: 实时验证和状态更新
- **上下文帮助**: 悬浮提示和帮助文档
- **键盘导航**: 完整的键盘访问支持

## 🔄 状态管理

### 本地状态
使用 React useState 和 useReducer 管理组件内部状态

### 状态同步
```typescript
// 父子组件状态同步
const handleStateChange = useCallback((newState) => {
  setQueryState(prev => ({ ...prev, ...newState }))
}, [])

// 状态持久化
useEffect(() => {
  localStorage.setItem('queryBuilder', JSON.stringify(queryState))
}, [queryState])
```

### 状态验证
```typescript
const validateState = (state) => {
  const errors = []
  if (!state.selectedDataset) errors.push("请选择数据集")
  if (state.selectedFields.length === 0) errors.push("请选择字段")
  return errors
}
```

## 🚀 性能优化

### 代码分割
```typescript
// 懒加载组件
const AdvancedQueryBuilder = lazy(() => 
  import("@/components/query-builder/advanced-query-builder")
)
```

### 虚拟化
```typescript
// 大列表虚拟化
import { FixedSizeList as List } from "react-window"

const VirtualizedFieldList = ({ fields }) => (
  <List
    height={400}
    itemCount={fields.length}
    itemSize={60}
    itemData={fields}
  >
    {FieldItem}
  </List>
)
```

### 缓存策略
```typescript
// API 响应缓存
const cache = new Map()

const cachedApiCall = async (key, apiCall) => {
  if (cache.has(key)) {
    return cache.get(key)
  }
  
  const result = await apiCall()
  cache.set(key, result)
  return result
}
```

## 🧪 测试策略

### 单元测试
```typescript
// 组件测试
describe("FieldSelector", () => {
  it("should render field categories", () => {
    render(<FieldSelector categories={mockCategories} />)
    expect(screen.getByText("人口统计学")).toBeInTheDocument()
  })
})
```

### 集成测试
```typescript
// 端到端测试
describe("Query Builder Flow", () => {
  it("should complete full query building process", async () => {
    // 1. 选择数据集
    await user.click(screen.getByText("MIMIC-IV"))
    
    // 2. 添加字段
    await user.click(screen.getByText("患者ID"))
    
    // 3. 执行查询
    await user.click(screen.getByText("执行查询"))
    
    // 4. 验证结果
    expect(await screen.findByText("查询执行成功")).toBeInTheDocument()
  })
})
```

## 📈 监控和分析

### 性能监控
```typescript
// 查询性能追踪
const trackQueryPerformance = (queryDSL, executionTime) => {
  analytics.track("query_executed", {
    dataset: queryDSL.dataset_id,
    fieldCount: queryDSL.fields.length,
    filterCount: queryDSL.filters.length,
    executionTime
  })
}
```

### 用户行为分析
```typescript
// 用户交互追踪
const trackUserInteraction = (action, context) => {
  analytics.track("user_interaction", {
    action,
    context,
    timestamp: Date.now()
  })
}
```

## 🔮 未来扩展

### 计划功能
1. **AI 查询助手**: 自然语言转查询
2. **可视化图表**: 内置数据可视化
3. **实时协作**: 多用户同时编辑
4. **查询模板**: 预定义查询模板
5. **数据血缘**: 查询依赖关系追踪

### 技术升级
1. **React 18**: 并发特性和 Suspense
2. **TypeScript 5**: 更好的类型推导
3. **Vite**: 更快的构建工具
4. **Web Workers**: 后台数据处理

---

这个技术实现为高级查询构建器提供了坚实的基础，支持未来的功能扩展和性能优化。
