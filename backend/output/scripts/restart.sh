#!/bin/bash

# Medical Data Platform Server - Restart Script (Deployment Package)
# 医疗数据平台服务器重启脚本（部署包）

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 显示帮助
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo "Options:"
    echo "  -f, --force     强制停止后重启"
    echo "  -t, --timeout   停止超时时间（默认30秒）"
    echo "  -w, --wait      停止后等待时间（默认5秒）"
    echo "  -h, --help      显示帮助"
}

# 重启服务
restart_service() {
    local force_kill=${1:-false}
    local timeout=${2:-30}
    local wait_time=${3:-5}

    log_info "重启医疗数据平台服务器..."

    # 停止服务
    log_info "步骤 1/3: 停止服务"
    if [[ "$force_kill" == "true" ]]; then
        bash "$SCRIPT_DIR/stop.sh" --force --timeout "$timeout"
    else
        bash "$SCRIPT_DIR/stop.sh" --timeout "$timeout"
    fi

    # 等待
    log_info "步骤 2/3: 等待 ${wait_time} 秒..."
    sleep "$wait_time"

    # 启动服务
    log_info "步骤 3/3: 启动服务"
    bash "$SCRIPT_DIR/start.sh"

    log_success "服务重启完成"
}

# 主函数
main() {
    local force_kill=false
    local timeout=30
    local wait_time=5

    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -f|--force)
                force_kill=true
                shift
                ;;
            -t|--timeout)
                timeout="$2"
                shift 2
                ;;
            -w|--wait)
                wait_time="$2"
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done

    restart_service "$force_kill" "$timeout" "$wait_time"
}

main "$@"
