"use client"

import React, { useState, useEffect } from "react"
import * as medicalQueryAPI from "@/lib/api/medical-query"
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Checkbox } from "@/components/ui/checkbox"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Progress } from "@/components/ui/progress"
import { 
  Users, 
  Database, 
  FileSpreadsheet, 
  Calendar,
  Activity,
  TestTube,
  Heart,
  Brain,
  Pill,
  Plus, 
  Trash2, 
  Download,
  Play,
  Eye,
  ChevronRight,
  ChevronLeft,
  Info,
  CheckCircle,
  AlertCircle
} from "lucide-react"

// 扩展API类型以包含React组件
interface MedicalFieldCategoryWithIcon extends Omit<medicalQueryAPI.MedicalFieldCategory, 'icon'> {
  icon: React.ReactNode
}

// 使用API类型
type CohortCriteria = medicalQueryAPI.CohortCriteria
type DataDimension = medicalQueryAPI.DataDimension
type MedicalField = medicalQueryAPI.MedicalField
type MedicalQueryConfig = medicalQueryAPI.MedicalQueryRequest

export default function MedicalQueryBuilder() {
  const [currentStep, setCurrentStep] = useState(1)
  const [queryConfig, setQueryConfig] = useState<MedicalQueryConfig>({
    studyName: "",
    cohortCriteria: [],
    dataDimensions: [],
    timeRange: { type: 'admission' },
    outputFormat: 'csv',
    maxRecords: 1000
  })
  const [estimatedResults, setEstimatedResults] = useState<number>(0)
  const [loading, setLoading] = useState(false)
  const [queryResults, setQueryResults] = useState<medicalQueryAPI.MedicalQueryResponse | null>(null)

  // 医学字段分类数据
  const [medicalCategories, setMedicalCategories] = useState<MedicalFieldCategoryWithIcon[]>([])

  // 加载医学字段分类
  useEffect(() => {
    const loadMedicalCategories = async () => {
      try {
        const categories = await medicalQueryAPI.getMedicalCategories()
        const categoriesWithIcons: MedicalFieldCategoryWithIcon[] = categories.map(category => ({
          ...category,
          icon: getCategoryIcon(category.id)
        }))
        console.log('medical categories:', categoriesWithIcons)
        setMedicalCategories(categoriesWithIcons)
      } catch (error) {
        console.log('Failed to load medical categories:', error)
        console.error('Failed to load medical categories:', error)
        // 使用默认分类作为后备
        setMedicalCategories(getDefaultCategories())
      }
    }

    loadMedicalCategories()
  }, [])

  // 获取分类图标
  const getCategoryIcon = (categoryId: string): React.ReactNode => {
    const iconMap: Record<string, React.ReactNode> = {
      demographics: <Users className="h-5 w-5" />,
      admission: <Heart className="h-5 w-5" />,
      laboratory: <TestTube className="h-5 w-5" />,
      diagnosis: <Brain className="h-5 w-5" />,
      medication: <Pill className="h-5 w-5" />
    }
    return iconMap[categoryId] || <Database className="h-5 w-5" />
  }

  // 默认分类（作为后备）
  const getDefaultCategories = (): MedicalFieldCategoryWithIcon[] => [
    {
      id: 'demographics',
      name: '基本信息',
      icon: <Users className="h-5 w-5" />,
      description: '患者基本人口学信息',
      fields: [
        {
          id: 'mimiciv_hosp.patients.anchor_age',
          name: '年龄',
          nameEn: 'anchor_age',
          type: 'number',
          description: '患者锚定年龄',
          table: 'mimiciv_hosp.patients',
          category: 'demographics',
          unit: '岁',
          examples: ['65', '76', '89']
        },
        {
          id: 'mimiciv_hosp.patients.gender',
          name: '性别',
          nameEn: 'gender',
          type: 'string',
          description: '患者性别',
          table: 'mimiciv_hosp.patients',
          category: 'demographics',
          examples: ['M', 'F']
        },
        {
          id: 'mimiciv_hosp.patients.subject_id',
          name: '患者ID',
          nameEn: 'subject_id',
          type: 'integer',
          description: '患者唯一标识符',
          table: 'mimiciv_hosp.patients',
          category: 'demographics',
          examples: ['10000032', '10000980', '10001217']
        }
      ]
    },
    {
      id: 'admission',
      name: '住院信息',
      icon: <Heart className="h-5 w-5" />,
      description: '住院相关信息',
      fields: [
        {
          id: 'mimiciv_hosp.admissions.admission_type',
          name: '入院类型',
          nameEn: 'admission_type',
          type: 'string',
          description: '入院类型分类',
          table: 'mimiciv_hosp.admissions',
          category: 'admission',
          examples: ['EMERGENCY', 'ELECTIVE', 'URGENT']
        },
        {
          id: 'mimiciv_hosp.admissions.admittime',
          name: '入院时间',
          nameEn: 'admittime',
          type: 'datetime',
          description: '患者入院时间',
          table: 'mimiciv_hosp.admissions',
          category: 'admission',
          examples: ['2180-07-15 14:00:00', '2181-03-22 09:30:00']
        },
        {
          id: 'mimiciv_hosp.admissions.insurance',
          name: '保险类型',
          nameEn: 'insurance',
          type: 'string',
          description: '患者保险类型',
          table: 'mimiciv_hosp.admissions',
          category: 'admission',
          examples: ['Medicare', 'Medicaid', 'Private']
        }
      ]
    },
    {
      id: 'laboratory',
      name: '检验结果',
      icon: <TestTube className="h-5 w-5" />,
      description: '实验室检查结果',
      fields: [
        {
          id: 'lab_value',
          name: '检验值',
          nameEn: 'valuenum',
          type: 'number',
          description: '检验数值',
          table: 'labevents',
          category: 'laboratory'
        }
      ]
    },
    {
      id: 'diagnosis',
      name: '诊断信息',
      icon: <Brain className="h-5 w-5" />,
      description: '疾病诊断相关信息',
      fields: [
        {
          id: 'icd_code',
          name: 'ICD诊断码',
          nameEn: 'icd_code',
          type: 'string',
          description: 'ICD诊断代码',
          table: 'diagnoses_icd',
          category: 'diagnosis'
        }
      ]
    },
    {
      id: 'medication',
      name: '用药信息',
      icon: <Pill className="h-5 w-5" />,
      description: '药物使用相关信息',
      fields: [
        {
          id: 'drug',
          name: '药物名称',
          nameEn: 'drug',
          type: 'string',
          description: '药物名称',
          table: 'prescriptions',
          category: 'medication'
        }
      ]
    }
  ]

  // 步骤导航
  const steps = [
    { id: 1, name: '研究人群', description: '定义研究对象' },
    { id: 2, name: '数据维度', description: '选择需要的数据' },
    { id: 3, name: '时间范围', description: '设置时间条件' },
    { id: 4, name: '预览导出', description: '查看结果并导出' }
  ]

  const nextStep = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  // 人群筛选条件行组件
  const CohortCriteriaRow = ({
    criteria,
    index,
    onUpdate,
    onRemove,
    showLogic
  }: {
    criteria: CohortCriteria
    index: number
    onUpdate: (criteria: CohortCriteria) => void
    onRemove: () => void
    showLogic: boolean
  }) => {
    const operators = [
      { value: '=', label: '等于' },
      { value: '!=', label: '不等于' },
      { value: '>', label: '大于' },
      { value: '<', label: '小于' },
      { value: '>=', label: '大于等于' },
      { value: '<=', label: '小于等于' },
      { value: 'LIKE', label: '包含' },
      { value: 'IN', label: '属于' }
    ]

    return (
      <div className="p-4 border rounded-lg bg-gray-50">
        {showLogic && (
          <div className="mb-3">
            <Select
              value={criteria.logic}
              onValueChange={(value: 'AND' | 'OR') =>
                onUpdate({ ...criteria, logic: value })
              }
            >
              <SelectTrigger className="w-24">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="AND">并且</SelectItem>
                <SelectItem value="OR">或者</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
          <div>
            <Label className="text-xs">数据类别</Label>
            <Select
              value={criteria.category}
              onValueChange={(value) =>
                onUpdate({ ...criteria, category: value, field: '' })
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="选择类别" />
              </SelectTrigger>
              <SelectContent>
                {medicalCategories.map(category => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label className="text-xs">字段</Label>
            <Select
              value={criteria.field}
              onValueChange={(value) => onUpdate({ ...criteria, field: value })}
              disabled={!criteria.category}
            >
              <SelectTrigger>
                <SelectValue placeholder="选择字段" />
              </SelectTrigger>
              <SelectContent>
                {criteria.category &&
                  medicalCategories
                    .find(cat => cat.id === criteria.category)
                    ?.fields.map(field => (
                      <SelectItem key={field.id} value={field.id}>
                        {field.name}
                      </SelectItem>
                    ))
                }
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label className="text-xs">条件</Label>
            <Select
              value={criteria.operator}
              onValueChange={(value) => onUpdate({ ...criteria, operator: value })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {operators.map(op => (
                  <SelectItem key={op.value} value={op.value}>
                    {op.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="flex gap-2">
            <div className="flex-1">
              <Label className="text-xs">值</Label>
              <Input
                placeholder="输入值"
                value={criteria.value}
                onChange={(e) => onUpdate({ ...criteria, value: e.target.value })}
              />
            </div>
            <Button
              size="sm"
              variant="ghost"
              className="mt-5"
              onClick={onRemove}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {criteria.field && (
          <div className="mt-2 text-xs text-gray-600">
            {(() => {
              const category = medicalCategories.find(cat => cat.id === criteria.category)
              const field = category?.fields.find(f => f.id === criteria.field)
              return field ? `${field.description}${field.unit ? ` (${field.unit})` : ''}` : ''
            })()}
          </div>
        )}
      </div>
    )
  }

  // 添加人群筛选条件
  const addCohortCriteria = () => {
    const newCriteria: CohortCriteria = {
      id: Date.now().toString(),
      category: '',
      field: '',
      operator: '=',
      value: '',
      description: '',
      logic: queryConfig.cohortCriteria.length > 0 ? 'AND' : undefined
    }
    setQueryConfig(prev => ({
      ...prev,
      cohortCriteria: [...prev.cohortCriteria, newCriteria]
    }))
  }

  // 更新人群筛选条件
  const updateCohortCriteria = (index: number, criteria: CohortCriteria) => {
    setQueryConfig(prev => ({
      ...prev,
      cohortCriteria: prev.cohortCriteria.map((item, i) => 
        i === index ? criteria : item
      )
    }))
  }

  // 删除人群筛选条件
  const removeCohortCriteria = (index: number) => {
    setQueryConfig(prev => ({
      ...prev,
      cohortCriteria: prev.cohortCriteria.filter((_, i) => i !== index)
    }))
  }

  // 切换数据维度选择
  const toggleDataDimension = (categoryId: string) => {
    const category = medicalCategories.find(cat => cat.id === categoryId)
    if (!category) return

    setQueryConfig(prev => {
      const existingIndex = prev.dataDimensions.findIndex(dim => dim.category === categoryId)
      
      if (existingIndex >= 0) {
        // 移除已选择的维度
        return {
          ...prev,
          dataDimensions: prev.dataDimensions.filter((_, i) => i !== existingIndex)
        }
      } else {
        // 添加新的维度
        return {
          ...prev,
          dataDimensions: [...prev.dataDimensions, {
            category: categoryId,
            fields: category.fields,
            isSelected: true
          }]
        }
      }
    })
  }

  // 研究人群定义组件
  const CohortDefinitionStep = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          定义研究人群
        </CardTitle>
        <p className="text-sm text-gray-600">
          设置条件来筛选您要研究的患者群体
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Label htmlFor="study-name">研究名称</Label>
          <Input
            id="study-name"
            placeholder="例如：糖尿病患者住院分析"
            value={queryConfig.studyName}
            onChange={(e) => setQueryConfig(prev => ({ ...prev, studyName: e.target.value }))}
          />
        </div>

        <div>
          <div className="flex items-center justify-between mb-3">
            <Label>筛选条件</Label>
            <Button size="sm" variant="outline" onClick={addCohortCriteria}>
              <Plus className="h-4 w-4 mr-1" />
              添加条件
            </Button>
          </div>

          {queryConfig.cohortCriteria.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Users className="h-12 w-12 mx-auto mb-2 opacity-50" />
              <p>尚未设置筛选条件</p>
              <p className="text-sm">点击"添加条件"开始定义研究人群</p>
            </div>
          ) : (
            <div className="space-y-3">
              {queryConfig.cohortCriteria.map((criteria, index) => (
                <CohortCriteriaRow
                  key={criteria.id}
                  criteria={criteria}
                  index={index}
                  onUpdate={(updated) => updateCohortCriteria(index, updated)}
                  onRemove={() => removeCohortCriteria(index)}
                  showLogic={index > 0}
                />
              ))}
            </div>
          )}
        </div>

        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            系统将根据您设置的条件筛选出符合要求的患者，然后获取这些患者的相关数据。
          </AlertDescription>
        </Alert>
      </CardContent>
    </Card>
  )

  // 数据维度选择组件
  const DataDimensionStep = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" />
          选择数据维度
        </CardTitle>
        <p className="text-sm text-gray-600">
          选择您需要获取的数据类型
        </p>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {medicalCategories.map((category) => {
            const isSelected = queryConfig.dataDimensions.some(dim => dim.category === category.id)
            return (
              <Card
                key={category.id}
                className={`cursor-pointer transition-all hover:shadow-md ${
                  isSelected ? 'ring-2 ring-blue-500 bg-blue-50' : ''
                }`}
                onClick={() => toggleDataDimension(category.id)}
              >
                <CardContent className="p-4">
                  <div className="flex items-start gap-3">
                    <div className={`p-2 rounded-lg ${
                      isSelected ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-600'
                    }`}>
                      {category.icon}
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium text-sm">{category.name}</h3>
                      <p className="text-xs text-gray-600 mt-1">{category.description}</p>
                      <div className="mt-2">
                        <Badge variant="secondary" className="text-xs">
                          {category.fields.length} 个字段
                        </Badge>
                      </div>
                    </div>
                    {isSelected && (
                      <CheckCircle className="h-5 w-5 text-blue-500" />
                    )}
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {queryConfig.dataDimensions.length > 0 && (
          <div className="mt-6">
            <h4 className="font-medium mb-3">已选择的数据维度：</h4>
            <div className="space-y-2">
              {queryConfig.dataDimensions.map((dimension) => {
                const category = medicalCategories.find(cat => cat.id === dimension.category)
                return (
                  <div key={dimension.category} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-2">
                      {category?.icon}
                      <span className="font-medium">{category?.name}</span>
                      <Badge variant="outline">{dimension.fields.length} 字段</Badge>
                    </div>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => toggleDataDimension(dimension.category)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                )
              })}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )

  // 预览导出组件
  const PreviewExportStep = () => {
    const executeQuery = async () => {
      setLoading(true)
      try {
        // 验证查询配置
        const validationErrors = medicalQueryAPI.validateQueryConfig(queryConfig)
        if (validationErrors.length > 0) {
          alert('查询配置错误:\n' + validationErrors.join('\n'))
          setLoading(false)
          return
        }

        // 执行查询
        const response = await medicalQueryAPI.executeMedicalQuery(queryConfig)
        setQueryResults(response)
        setEstimatedResults(response.total)
      } catch (error) {
        console.error('查询执行失败:', error)
        alert('查询执行失败，请检查网络连接或联系管理员')
      } finally {
        setLoading(false)
      }
    }

    const downloadResults = async (format: 'csv' | 'excel' | 'json') => {
      if (!queryResults?.queryId) {
        alert('没有可下载的查询结果')
        return
      }

      try {
        const exportResult = await medicalQueryAPI.exportQueryResults(queryResults.queryId, format)
        medicalQueryAPI.downloadExportFile(exportResult.downloadUrl, `${queryConfig.studyName}.${format}`)
      } catch (error) {
        console.error('下载失败:', error)
        alert('下载失败，请稍后重试')
      }
    }

    return (
      <div className="space-y-6">
        {/* 查询摘要 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              查询摘要
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">研究名称</h4>
              <p className="text-gray-600">{queryConfig.studyName || '未命名研究'}</p>
            </div>

            <div>
              <h4 className="font-medium mb-2">研究人群条件</h4>
              {queryConfig.cohortCriteria.length === 0 ? (
                <p className="text-gray-500">无筛选条件（所有患者）</p>
              ) : (
                <div className="space-y-1">
                  {queryConfig.cohortCriteria.map((criteria, index) => {
                    const category = medicalCategories.find(cat => cat.id === criteria.category)
                    const field = category?.fields.find(f => f.id === criteria.field)
                    return (
                      <div key={criteria.id} className="text-sm">
                        {index > 0 && (
                          <span className="text-blue-600 font-medium">
                            {criteria.logic === 'AND' ? '并且' : '或者'}{' '}
                          </span>
                        )}
                        <span>
                          {field?.name} {criteria.operator === '=' ? '等于' : criteria.operator} {criteria.value}
                        </span>
                      </div>
                    )
                  })}
                </div>
              )}
            </div>

            <div>
              <h4 className="font-medium mb-2">数据维度</h4>
              {queryConfig.dataDimensions.length === 0 ? (
                <p className="text-gray-500">未选择数据维度</p>
              ) : (
                <div className="flex flex-wrap gap-2">
                  {queryConfig.dataDimensions.map((dimension) => {
                    const category = medicalCategories.find(cat => cat.id === dimension.category)
                    return (
                      <Badge key={dimension.category} variant="secondary">
                        {category?.name} ({dimension.fields.length} 字段)
                      </Badge>
                    )
                  })}
                </div>
              )}
            </div>

            <div>
              <h4 className="font-medium mb-2">时间范围</h4>
              <p className="text-sm text-gray-600">
                类型: {
                  queryConfig.timeRange.type === 'admission' ? '入院时间' :
                  queryConfig.timeRange.type === 'discharge' ? '出院时间' :
                  queryConfig.timeRange.type === 'lab' ? '检验时间' : '自定义时间'
                }
                {queryConfig.timeRange.start && ` | 开始: ${queryConfig.timeRange.start}`}
                {queryConfig.timeRange.end && ` | 结束: ${queryConfig.timeRange.end}`}
              </p>
            </div>
          </CardContent>
        </Card>

        {/* 执行查询 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Play className="h-5 w-5" />
              执行查询
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-3">
              <Button
                onClick={executeQuery}
                disabled={loading || queryConfig.dataDimensions.length === 0}
                className="flex-1"
              >
                {loading ? (
                  <>
                    <Activity className="h-4 w-4 mr-2 animate-spin" />
                    执行中...
                  </>
                ) : (
                  <>
                    <Play className="h-4 w-4 mr-2" />
                    执行查询
                  </>
                )}
              </Button>
            </div>

            {loading && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>正在执行查询...</span>
                  <span>预计 2-5 秒</span>
                </div>
                <Progress value={33} className="h-2" />
              </div>
            )}

            {queryResults && (
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  查询完成！找到 {queryResults.total} 条记录，执行时间 {queryResults.executionTime}ms
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>

        {/* 结果预览和导出 */}
        {queryResults && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Download className="h-5 w-5" />
                结果导出
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">导出格式</h4>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => downloadResults('csv')}
                  >
                    <FileSpreadsheet className="h-4 w-4 mr-1" />
                    CSV
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => downloadResults('excel')}
                  >
                    <FileSpreadsheet className="h-4 w-4 mr-1" />
                    Excel
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => downloadResults('json')}
                  >
                    <Database className="h-4 w-4 mr-1" />
                    JSON
                  </Button>
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-2">数据预览</h4>
                <div className="border rounded-lg overflow-hidden">
                  <div className="bg-gray-50 px-4 py-2 border-b">
                    <span className="text-sm font-medium">前 5 条记录</span>
                  </div>
                  <div className="p-4">
                    <pre className="text-xs bg-gray-100 p-3 rounded overflow-x-auto">
                      {JSON.stringify(queryResults.data.slice(0, 5), null, 2)}
                    </pre>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    )
  }

  // 时间范围设置组件
  const TimeRangeStep = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          设置时间范围
        </CardTitle>
        <p className="text-sm text-gray-600">
          定义数据的时间范围
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Label>时间类型</Label>
          <Select
            value={queryConfig.timeRange.type}
            onValueChange={(value: any) =>
              setQueryConfig(prev => ({
                ...prev,
                timeRange: { ...prev.timeRange, type: value }
              }))
            }
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="admission">入院时间</SelectItem>
              <SelectItem value="discharge">出院时间</SelectItem>
              <SelectItem value="lab">检验时间</SelectItem>
              <SelectItem value="custom">自定义时间</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label>开始时间</Label>
            <Input
              type="date"
              value={queryConfig.timeRange.start || ''}
              onChange={(e) => setQueryConfig(prev => ({
                ...prev,
                timeRange: { ...prev.timeRange, start: e.target.value }
              }))}
            />
          </div>
          <div>
            <Label>结束时间</Label>
            <Input
              type="date"
              value={queryConfig.timeRange.end || ''}
              onChange={(e) => setQueryConfig(prev => ({
                ...prev,
                timeRange: { ...prev.timeRange, end: e.target.value }
              }))}
            />
          </div>
        </div>

        <div>
          <Label>最大记录数</Label>
          <Select
            value={queryConfig.maxRecords.toString()}
            onValueChange={(value) =>
              setQueryConfig(prev => ({ ...prev, maxRecords: parseInt(value) }))
            }
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="100">100 条</SelectItem>
              <SelectItem value="500">500 条</SelectItem>
              <SelectItem value="1000">1,000 条</SelectItem>
              <SelectItem value="5000">5,000 条</SelectItem>
              <SelectItem value="10000">10,000 条</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardContent>
    </Card>
  )

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* 页面标题 */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">医学数据查询构建器</h1>
        <p className="text-gray-600">为医学研究设计的直观数据查询工具</p>
      </div>

      {/* 步骤指示器 */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                  currentStep >= step.id
                    ? 'bg-blue-500 border-blue-500 text-white'
                    : 'border-gray-300 text-gray-500'
                }`}>
                  {currentStep > step.id ? (
                    <CheckCircle className="h-6 w-6" />
                  ) : (
                    <span className="text-sm font-medium">{step.id}</span>
                  )}
                </div>
                <div className="ml-3">
                  <p className={`text-sm font-medium ${
                    currentStep >= step.id ? 'text-blue-600' : 'text-gray-500'
                  }`}>
                    {step.name}
                  </p>
                  <p className="text-xs text-gray-500">{step.description}</p>
                </div>
                {index < steps.length - 1 && (
                  <ChevronRight className="h-5 w-5 text-gray-400 mx-4" />
                )}
              </div>
            ))}
          </div>
          <div className="mt-4">
            <Progress value={(currentStep / steps.length) * 100} className="h-2" />
          </div>
        </CardContent>
      </Card>

      {/* 步骤内容 */}
      <div>
        {currentStep === 1 && <CohortDefinitionStep />}
        {currentStep === 2 && <DataDimensionStep />}
        {currentStep === 3 && <TimeRangeStep />}
        {currentStep === 4 && <PreviewExportStep />}
      </div>

      {/* 导航按钮 */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={prevStep}
          disabled={currentStep === 1}
        >
          <ChevronLeft className="h-4 w-4 mr-1" />
          上一步
        </Button>
        <Button
          onClick={nextStep}
          disabled={currentStep === 4}
        >
          下一步
          <ChevronRight className="h-4 w-4 ml-1" />
        </Button>
      </div>
    </div>
  )
}
