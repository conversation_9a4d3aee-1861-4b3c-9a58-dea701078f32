package api

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/chungzy/medical-data-platform/internal/adapter"
	"github.com/chungzy/medical-data-platform/pkg/database"
	"github.com/chungzy/medical-data-platform/pkg/logger"
)

// SystemController 系统控制器
type SystemController struct {
	connectionManager *database.ConnectionManager
	adapterManager    *adapter.AdapterManager
}

// NewSystemController 创建系统控制器
func NewSystemController(
	connectionManager *database.ConnectionManager,
	adapterManager *adapter.AdapterManager,
) *SystemController {
	return &SystemController{
		connectionManager: connectionManager,
		adapterManager:    adapterManager,
	}
}

// HealthCheck 健康检查
func (sc *SystemController) HealthCheck(c *gin.Context) {
	c.Set("business_operation", "health_check")

	logger.InfoWithGinContext(c, "Performing health check")

	status := sc.connectionManager.HealthCheck()

	allHealthy := true
	for _, healthy := range status {
		if !healthy {
			allHealthy = false
			break
		}
	}

	adapters := sc.adapterManager.ListAdapters()

	response := gin.H{
		"status":      "healthy",
		"datasources": status,
		"adapters":    adapters,
		"timestamp":   c.GetTime("request_time"),
	}

	if allHealthy {
		logger.InfoWithGinContext(c, "Health check passed", map[string]interface{}{
			"datasources_count": len(status),
			"adapters_count":    len(adapters),
		})
		c.JSON(http.StatusOK, response)
	} else {
		logger.WarnWithGinContext(c, "Health check failed - some datasources unhealthy", map[string]interface{}{
			"datasources": status,
		})
		response["status"] = "unhealthy"
		c.JSON(http.StatusServiceUnavailable, response)
	}
}

// GetDatasourceStatus 获取数据源状态
func (sc *SystemController) GetDatasourceStatus(c *gin.Context) {
	c.Set("business_operation", "get_datasource_status")

	logger.InfoWithGinContext(c, "Getting datasource status")

	status := sc.connectionManager.HealthCheck()
	adapters := sc.adapterManager.ListAdapters()
	enabled := sc.connectionManager.ListEnabledDatasources()

	logger.InfoWithGinContext(c, "Datasource status retrieved", map[string]interface{}{
		"datasources_count": len(status),
		"adapters_count":    len(adapters),
		"enabled_count":     len(enabled),
	})

	c.JSON(http.StatusOK, gin.H{
		"datasources": status,
		"adapters":    adapters,
		"enabled":     enabled,
		"timestamp":   c.GetTime("request_time"),
	})
}
