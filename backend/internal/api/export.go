package api

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"

	"github.com/chungzy/medical-data-platform/internal/model"
	"github.com/chungzy/medical-data-platform/internal/service"
	"github.com/chungzy/medical-data-platform/pkg/logger"
)

type ExportController struct {
	exportService *service.ExportService
}

func NewExportController(exportService *service.ExportService) *ExportController {
	return &ExportController{
		exportService: exportService,
	}
}

// ExportData 导出数据
func (ec *ExportController) ExportData(c *gin.Context) {
	c.Set("business_operation", "export_data")

	logger.InfoWithGinContext(c, "Starting data export")

	userID, exists := c.Get("user_id")
	if !exists {
		logger.WarnWithGinContext(c, "User ID not found in context")
		c.<PERSON>(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req model.ExportRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to parse export request")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format", "details": err.Error()})
		return
	}

	logger.InfoWithGinContext(c, "Export request parsed successfully", map[string]interface{}{
		"query_id": req.QueryID,
		"format":   req.Format,
		"user_id":  userID,
	})

	exportRecord, err := ec.exportService.CreateExport(c.Request.Context(), userID.(uuid.UUID), &req)
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to create export", map[string]interface{}{
			"query_id": req.QueryID,
			"format":   req.Format,
			"user_id":  userID,
		})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create export", "details": err.Error()})
		return
	}

	logger.InfoWithGinContext(c, "Export created successfully", map[string]interface{}{
		"export_id": exportRecord.ID,
		"status":    exportRecord.Status,
		"user_id":   userID,
	})

	c.JSON(http.StatusAccepted, gin.H{
		"export_id": exportRecord.ID,
		"status":    exportRecord.Status,
		"message":   "Export request submitted successfully",
	})
}

// GetExportStatus 获取导出状态
func (ec *ExportController) GetExportStatus(c *gin.Context) {
	c.Set("business_operation", "get_export_status")

	logger.InfoWithGinContext(c, "Getting export status")

	userID, exists := c.Get("user_id")
	if !exists {
		logger.WarnWithGinContext(c, "User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	exportID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Invalid export ID format", map[string]interface{}{
			"export_id_param": c.Param("id"),
		})
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid export ID"})
		return
	}

	logger.InfoWithGinContext(c, "Export ID parsed successfully", map[string]interface{}{
		"export_id": exportID,
		"user_id":   userID,
	})

	exportRecord, err := ec.exportService.GetExportStatus(c.Request.Context(), exportID, userID.(uuid.UUID))
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Export not found", map[string]interface{}{
			"export_id": exportID,
			"user_id":   userID,
		})
		c.JSON(http.StatusNotFound, gin.H{"error": "Export not found"})
		return
	}

	logger.InfoWithGinContext(c, "Export status retrieved successfully", map[string]interface{}{
		"export_id": exportID,
		"status":    exportRecord.Status,
		"file_name": exportRecord.FileName,
		"user_id":   userID,
	})

	c.JSON(http.StatusOK, gin.H{
		"export_id":  exportRecord.ID,
		"status":     exportRecord.Status,
		"file_name":  exportRecord.FileName,
		"file_size":  exportRecord.FileSize,
		"created_at": exportRecord.CreatedAt,
		"expires_at": exportRecord.ExpiresAt,
	})
}

// DownloadExport 下载导出文件
func (ec *ExportController) DownloadExport(c *gin.Context) {
	c.Set("business_operation", "download_export")

	logger.InfoWithGinContext(c, "Starting export file download")

	userID, exists := c.Get("user_id")
	if !exists {
		logger.WarnWithGinContext(c, "User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	exportID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Invalid export ID format", map[string]interface{}{
			"export_id_param": c.Param("id"),
		})
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid export ID"})
		return
	}

	logger.InfoWithGinContext(c, "Getting download info", map[string]interface{}{
		"export_id": exportID,
		"user_id":   userID,
	})

	filePath, fileName, err := ec.exportService.GetDownloadInfo(c.Request.Context(), exportID, userID.(uuid.UUID))
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Export file not found or expired", map[string]interface{}{
			"export_id": exportID,
			"user_id":   userID,
		})
		c.JSON(http.StatusNotFound, gin.H{"error": "Export file not found or expired"})
		return
	}

	logger.InfoWithGinContext(c, "Starting file download", map[string]interface{}{
		"export_id": exportID,
		"file_path": filePath,
		"file_name": fileName,
		"user_id":   userID,
	})

	// 设置下载响应头
	c.Header("Content-Disposition", "attachment; filename="+fileName)
	c.Header("Content-Type", "application/octet-stream")

	// 发送文件
	c.File(filePath)

	// 增加下载次数
	if err := ec.exportService.IncrementDownloadCount(c.Request.Context(), exportID); err != nil {
		logger.WarnWithGinContext(c, "Failed to increment download count", map[string]interface{}{
			"export_id": exportID,
			"error":     err.Error(),
		})
	}

	logger.InfoWithGinContext(c, "File download completed successfully", map[string]interface{}{
		"export_id": exportID,
		"file_name": fileName,
		"user_id":   userID,
	})
}
