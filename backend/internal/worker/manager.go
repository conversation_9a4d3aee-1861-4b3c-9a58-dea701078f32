package worker

import (
	"context"
	"sync"

	"github.com/chungzy/medical-data-platform/internal/config"
	"github.com/chungzy/medical-data-platform/pkg/logger"
)

// Worker 定义worker接口
type Worker interface {
	// Start 启动worker
	Start() error
	// Stop 停止worker
	Stop()
	// GetName 获取worker名称
	GetName() string
	// GetStatus 获取worker状态
	GetStatus() map[string]interface{}
}

// WorkerManager worker管理器
type WorkerManager struct {
	config  *config.Config
	workers map[string]Worker
	mu      sync.RWMutex
	ctx     context.Context
	cancel  context.CancelFunc
}

// NewWorkerManager 创建worker管理器
func NewWorkerManager(cfg *config.Config) *WorkerManager {
	ctx, cancel := context.WithCancel(context.Background())
	return &WorkerManager{
		config:  cfg,
		workers: make(map[string]Worker),
		ctx:     ctx,
		cancel:  cancel,
	}
}

// RegisterWorker 注册worker
func (wm *WorkerManager) Register<PERSON>orker(worker Worker) {
	wm.mu.Lock()
	defer wm.mu.Unlock()

	name := worker.GetName()
	wm.workers[name] = worker

	logger.Info("Worker registered", map[string]interface{}{
		"worker_name": name,
	})
}

// StartAll 启动所有worker
func (wm *WorkerManager) StartAll() error {
	wm.mu.RLock()
	defer wm.mu.RUnlock()

	logger.Info("Starting all workers", map[string]interface{}{
		"worker_count": len(wm.workers),
	})

	for name, worker := range wm.workers {
		logger.Info("Starting worker", map[string]interface{}{
			"worker_name": name,
		})

		if err := worker.Start(); err != nil {
			logger.Error("Failed to start worker", map[string]interface{}{
				"worker_name": name,
				"error":       err.Error(),
			})
			return err
		}

		logger.Info("Worker started successfully", map[string]interface{}{
			"worker_name": name,
		})
	}

	logger.Info("All workers started successfully")
	return nil
}

// StopAll 停止所有worker
func (wm *WorkerManager) StopAll() {
	wm.mu.RLock()
	defer wm.mu.RUnlock()

	logger.Info("Stopping all workers", map[string]interface{}{
		"worker_count": len(wm.workers),
	})

	// 使用WaitGroup确保所有worker都正确停止
	var wg sync.WaitGroup

	for name, worker := range wm.workers {
		wg.Add(1)
		go func(name string, worker Worker) {
			defer wg.Done()

			logger.Info("Stopping worker", map[string]interface{}{
				"worker_name": name,
			})

			worker.Stop()

			logger.Info("Worker stopped", map[string]interface{}{
				"worker_name": name,
			})
		}(name, worker)
	}

	wg.Wait()
	wm.cancel()

	logger.Info("All workers stopped successfully")
}

// GetWorkerStatus 获取指定worker状态
func (wm *WorkerManager) GetWorkerStatus(name string) (map[string]interface{}, bool) {
	wm.mu.RLock()
	defer wm.mu.RUnlock()

	worker, exists := wm.workers[name]
	if !exists {
		return nil, false
	}

	return worker.GetStatus(), true
}

// GetAllWorkersStatus 获取所有worker状态
func (wm *WorkerManager) GetAllWorkersStatus() map[string]interface{} {
	wm.mu.RLock()
	defer wm.mu.RUnlock()

	status := make(map[string]interface{})

	for name, worker := range wm.workers {
		status[name] = worker.GetStatus()
	}

	return status
}

// GetWorkerNames 获取所有worker名称
func (wm *WorkerManager) GetWorkerNames() []string {
	wm.mu.RLock()
	defer wm.mu.RUnlock()

	names := make([]string, 0, len(wm.workers))
	for name := range wm.workers {
		names = append(names, name)
	}

	return names
}
