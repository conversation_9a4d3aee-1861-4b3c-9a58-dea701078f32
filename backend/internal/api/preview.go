package api

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/chungzy/medical-data-platform/internal/service"
	"github.com/chungzy/medical-data-platform/pkg/logger"
)

// PreviewController 预览控制器
type PreviewController struct {
	previewService *service.PreviewService
}

// NewPreviewController 创建预览控制器
func NewPreviewController(previewService *service.PreviewService) *PreviewController {
	return &PreviewController{
		previewService: previewService,
	}
}

// PreviewTable 表预览
func (pc *PreviewController) PreviewTable(c *gin.Context) {
	c.Set("business_operation", "preview_table")

	var req service.TablePreviewRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.ErrorWithGinContext(c, err, "Invalid request for table preview")
		c.<PERSON>(http.StatusBadRequest, gin.H{"error": "Invalid request", "details": err.Error()})
		return
	}

	logger.InfoWithGinContext(c, "Previewing table data", map[string]interface{}{
		"dataset": req.Dataset,
		"table":   req.Table,
		"limit":   req.Limit,
	})

	response, err := pc.previewService.PreviewTableData(c.Request.Context(), &req)
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to preview table data")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to preview table", "details": err.Error()})
		return
	}

	logger.InfoWithGinContext(c, "Table preview completed successfully", map[string]interface{}{
		"dataset":     req.Dataset,
		"table":       req.Table,
		"record_count": response.Count,
	})

	c.JSON(http.StatusOK, response)
}

// PreviewQuery 查询预览
func (pc *PreviewController) PreviewQuery(c *gin.Context) {
	c.Set("business_operation", "preview_query")

	var req service.QueryPreviewRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.ErrorWithGinContext(c, err, "Invalid request for query preview")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request", "details": err.Error()})
		return
	}

	logger.InfoWithGinContext(c, "Previewing query result", map[string]interface{}{
		"dataset":  req.QueryDSL.Dataset,
		"query_id": req.QueryID,
	})

	response, err := pc.previewService.PreviewQueryResult(c.Request.Context(), &req)
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to preview query")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to preview query", "details": err.Error()})
		return
	}

	logger.InfoWithGinContext(c, "Query preview completed", map[string]interface{}{
		"dataset":        req.QueryDSL.Dataset,
		"query_id":       req.QueryID,
		"valid":          response.Valid,
		"record_count":   response.Count,
		"execution_time": response.ExecutionTime,
	})

	c.JSON(http.StatusOK, response)
}

// GetDatasetOverview 获取数据集概览
func (pc *PreviewController) GetDatasetOverview(c *gin.Context) {
	c.Set("business_operation", "get_dataset_overview")

	dataset := c.Param("dataset")
	if dataset == "" {
		logger.ErrorWithGinContext(c, nil, "Dataset parameter is required")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Dataset parameter is required"})
		return
	}

	logger.InfoWithGinContext(c, "Getting dataset overview", map[string]interface{}{
		"dataset": dataset,
	})

	response, err := pc.previewService.GetDatasetOverview(c.Request.Context(), dataset)
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to get dataset overview")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get dataset overview", "details": err.Error()})
		return
	}

	logger.InfoWithGinContext(c, "Dataset overview retrieved successfully", map[string]interface{}{
		"dataset":      dataset,
		"table_count":  len(response.Tables),
		"field_count":  len(response.FieldCategories),
	})

	c.JSON(http.StatusOK, response)
}

// GetSampleData 获取样本数据
func (pc *PreviewController) GetSampleData(c *gin.Context) {
	c.Set("business_operation", "get_sample_data")

	var req service.SampleDataRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.ErrorWithGinContext(c, err, "Invalid request for sample data")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request", "details": err.Error()})
		return
	}

	logger.InfoWithGinContext(c, "Getting sample data", map[string]interface{}{
		"dataset": req.Dataset,
		"table":   req.Table,
		"fields":  req.Fields,
		"limit":   req.Limit,
	})

	response, err := pc.previewService.GetSampleData(c.Request.Context(), &req)
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to get sample data")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get sample data", "details": err.Error()})
		return
	}

	logger.InfoWithGinContext(c, "Sample data retrieved successfully", map[string]interface{}{
		"dataset":      req.Dataset,
		"table":        req.Table,
		"record_count": response.Count,
	})

	c.JSON(http.StatusOK, response)
}
