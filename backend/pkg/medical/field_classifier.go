package medical

import (
	"regexp"
	"strings"
)

// FieldClassifier 医学字段分类器
type FieldClassifier struct {
	patterns map[string][]*regexp.Regexp
	weights  map[string]float64
}

// ClassificationResult 分类结果
type ClassificationResult struct {
	Category   string  `json:"category"`
	Confidence float64 `json:"confidence"`
	Reasons    []string `json:"reasons"`
}

// NewFieldClassifier 创建字段分类器
func NewFieldClassifier() *FieldClassifier {
	fc := &FieldClassifier{
		patterns: make(map[string][]*regexp.Regexp),
		weights:  make(map[string]float64),
	}
	fc.initializePatterns()
	fc.initializeWeights()
	return fc
}

// initializePatterns 初始化分类模式
func (fc *FieldClassifier) initializePatterns() {
	// 人口统计学模式
	fc.patterns["demographics"] = []*regexp.Regexp{
		regexp.MustCompile(`(?i)(subject_id|patient_id|person_id)`),
		regexp.MustCompile(`(?i)(gender|sex)`),
		regexp.MustCompile(`(?i)(age|anchor_age|birth|dob)`),
		regexp.MustCompile(`(?i)(race|ethnicity)`),
		regexp.MustCompile(`(?i)(hadm_id|stay_id|visit_id)`),
	}

	// 时间信息模式
	fc.patterns["temporal"] = []*regexp.Regexp{
		regexp.MustCompile(`(?i)(time|date)$`),
		regexp.MustCompile(`(?i)(admit|discharge|death|chart|store)`),
		regexp.MustCompile(`(?i)(start|end|begin|finish)`),
		regexp.MustCompile(`(?i)(year|month|day|hour|minute)`),
		regexp.MustCompile(`(?i)(timestamp|datetime)`),
	}

	// 临床测量模式
	fc.patterns["clinical_measurements"] = []*regexp.Regexp{
		regexp.MustCompile(`(?i)(value|amount|rate|level|count)`),
		regexp.MustCompile(`(?i)(itemid|measurement_id)`),
		regexp.MustCompile(`(?i)(lab|vital|sign)`),
		regexp.MustCompile(`(?i)(blood|heart|pressure|temperature)`),
		regexp.MustCompile(`(?i)(glucose|sodium|potassium|hemoglobin)`),
		regexp.MustCompile(`(?i)(ref_range|reference|normal)`),
		regexp.MustCompile(`(?i)(flag|abnormal|critical)`),
	}

	// 诊断信息模式
	fc.patterns["diagnoses"] = []*regexp.Regexp{
		regexp.MustCompile(`(?i)(icd|diagnosis|disease)`),
		regexp.MustCompile(`(?i)(condition|disorder|syndrome)`),
		regexp.MustCompile(`(?i)(primary|secondary|principal)`),
		regexp.MustCompile(`(?i)(seq_num|sequence)`),
		regexp.MustCompile(`(?i)(version|code_system)`),
	}

	// 药物治疗模式
	fc.patterns["medications"] = []*regexp.Regexp{
		regexp.MustCompile(`(?i)(drug|medication|medicine)`),
		regexp.MustCompile(`(?i)(dose|dosage|amount)`),
		regexp.MustCompile(`(?i)(route|administration)`),
		regexp.MustCompile(`(?i)(form|formulation|strength)`),
		regexp.MustCompile(`(?i)(gsn|ndc|rxnorm)`),
		regexp.MustCompile(`(?i)(pharmacy|prescription)`),
	}

	// 医疗程序模式
	fc.patterns["procedures"] = []*regexp.Regexp{
		regexp.MustCompile(`(?i)(procedure|surgery|operation)`),
		regexp.MustCompile(`(?i)(cpt|icd.*proc)`),
		regexp.MustCompile(`(?i)(intervention|treatment)`),
		regexp.MustCompile(`(?i)(surgical|operative)`),
	}

	// 管理信息模式
	fc.patterns["administrative"] = []*regexp.Regexp{
		regexp.MustCompile(`(?i)(insurance|payer|coverage)`),
		regexp.MustCompile(`(?i)(admission_type|discharge_location)`),
		regexp.MustCompile(`(?i)(marital|language|religion)`),
		regexp.MustCompile(`(?i)(location|ward|unit)`),
		regexp.MustCompile(`(?i)(priority|status|type)`),
		regexp.MustCompile(`(?i)(comments|notes|description)`),
	}
}

// initializeWeights 初始化权重
func (fc *FieldClassifier) initializeWeights() {
	fc.weights = map[string]float64{
		"demographics":          1.0,
		"temporal":             0.9,
		"clinical_measurements": 1.0,
		"diagnoses":            0.95,
		"medications":          0.95,
		"procedures":           0.9,
		"administrative":       0.8,
	}
}

// ClassifyField 对字段进行分类
func (fc *FieldClassifier) ClassifyField(tableName, fieldName, dataType string) ClassificationResult {
	scores := make(map[string]float64)
	reasons := make(map[string][]string)

	// 基于字段名的模式匹配
	for category, patterns := range fc.patterns {
		for _, pattern := range patterns {
			if pattern.MatchString(fieldName) {
				scores[category] += fc.weights[category]
				reasons[category] = append(reasons[category], 
					"字段名匹配模式: "+pattern.String())
			}
		}
	}

	// 基于表名的上下文分析
	fc.analyzeTableContext(tableName, fieldName, scores, reasons)

	// 基于数据类型的分析
	fc.analyzeDataType(dataType, scores, reasons)

	// 找到最高分的分类
	bestCategory := "administrative" // 默认分类
	bestScore := 0.0
	
	for category, score := range scores {
		if score > bestScore {
			bestScore = score
			bestCategory = category
		}
	}

	// 计算置信度
	confidence := fc.calculateConfidence(bestScore, scores)

	return ClassificationResult{
		Category:   bestCategory,
		Confidence: confidence,
		Reasons:    reasons[bestCategory],
	}
}

// analyzeTableContext 分析表名上下文
func (fc *FieldClassifier) analyzeTableContext(tableName, fieldName string, scores map[string]float64, reasons map[string][]string) {
	tableName = strings.ToLower(tableName)
	fieldName = strings.ToLower(fieldName)

	// 患者表上下文
	if strings.Contains(tableName, "patients") {
		if strings.Contains(fieldName, "subject_id") {
			scores["demographics"] += 2.0
			reasons["demographics"] = append(reasons["demographics"], "患者表中的主键字段")
		}
		if strings.Contains(fieldName, "gender") || strings.Contains(fieldName, "age") {
			scores["demographics"] += 1.5
			reasons["demographics"] = append(reasons["demographics"], "患者表中的人口统计学字段")
		}
	}

	// 住院表上下文
	if strings.Contains(tableName, "admissions") {
		if strings.Contains(fieldName, "time") {
			scores["temporal"] += 1.5
			reasons["temporal"] = append(reasons["temporal"], "住院表中的时间字段")
		}
		if strings.Contains(fieldName, "insurance") || strings.Contains(fieldName, "type") {
			scores["administrative"] += 1.5
			reasons["administrative"] = append(reasons["administrative"], "住院表中的管理字段")
		}
	}

	// 检验表上下文
	if strings.Contains(tableName, "lab") {
		if strings.Contains(fieldName, "value") || strings.Contains(fieldName, "result") {
			scores["clinical_measurements"] += 2.0
			reasons["clinical_measurements"] = append(reasons["clinical_measurements"], "检验表中的测量值字段")
		}
	}

	// 诊断表上下文
	if strings.Contains(tableName, "diagnos") {
		if strings.Contains(fieldName, "icd") || strings.Contains(fieldName, "code") {
			scores["diagnoses"] += 2.0
			reasons["diagnoses"] = append(reasons["diagnoses"], "诊断表中的诊断代码字段")
		}
	}

	// 处方表上下文
	if strings.Contains(tableName, "prescription") {
		if strings.Contains(fieldName, "drug") || strings.Contains(fieldName, "dose") {
			scores["medications"] += 2.0
			reasons["medications"] = append(reasons["medications"], "处方表中的药物字段")
		}
	}
}

// analyzeDataType 分析数据类型
func (fc *FieldClassifier) analyzeDataType(dataType string, scores map[string]float64, reasons map[string][]string) {
	dataType = strings.ToUpper(dataType)

	switch dataType {
	case "TIMESTAMP", "DATE", "TIME":
		scores["temporal"] += 1.0
		reasons["temporal"] = append(reasons["temporal"], "时间类型数据")
	case "NUMERIC", "INTEGER", "BIGINT", "REAL", "DOUBLE PRECISION":
		scores["clinical_measurements"] += 0.5
		reasons["clinical_measurements"] = append(reasons["clinical_measurements"], "数值类型数据")
	case "VARCHAR", "TEXT":
		scores["administrative"] += 0.3
		reasons["administrative"] = append(reasons["administrative"], "文本类型数据")
	case "BOOLEAN":
		scores["administrative"] += 0.5
		reasons["administrative"] = append(reasons["administrative"], "布尔类型数据")
	}
}

// calculateConfidence 计算置信度
func (fc *FieldClassifier) calculateConfidence(bestScore float64, allScores map[string]float64) float64 {
	if bestScore == 0 {
		return 0.5 // 默认置信度
	}

	// 计算总分
	totalScore := 0.0
	for _, score := range allScores {
		totalScore += score
	}

	if totalScore == 0 {
		return 0.5
	}

	// 置信度 = 最高分 / 总分
	confidence := bestScore / totalScore

	// 调整置信度范围到 0.5-1.0
	return 0.5 + (confidence * 0.5)
}

// GetCategoryDisplayName 获取分类显示名称
func (fc *FieldClassifier) GetCategoryDisplayName(category string) string {
	displayNames := map[string]string{
		"demographics":          "人口统计学",
		"temporal":             "时间信息",
		"clinical_measurements": "临床测量",
		"diagnoses":            "诊断信息",
		"medications":          "药物治疗",
		"procedures":           "医疗程序",
		"administrative":       "管理信息",
	}

	if displayName, exists := displayNames[category]; exists {
		return displayName
	}
	return category
}

// GetCategoryDescription 获取分类描述
func (fc *FieldClassifier) GetCategoryDescription(category string) string {
	descriptions := map[string]string{
		"demographics":          "患者基本人口学信息和标识符",
		"temporal":             "入院、出院、检查等时间相关信息",
		"clinical_measurements": "生命体征、实验室检查等数值测量",
		"diagnoses":            "疾病诊断、ICD编码等临床诊断",
		"medications":          "处方药物、剂量、给药途径等",
		"procedures":           "手术、操作、医疗程序等",
		"administrative":       "保险、入院类型、出院去向等管理信息",
	}

	if description, exists := descriptions[category]; exists {
		return description
	}
	return "其他医学信息"
}

// GetAllCategories 获取所有支持的分类
func (fc *FieldClassifier) GetAllCategories() []string {
	return []string{
		"demographics",
		"temporal",
		"clinical_measurements",
		"diagnoses",
		"medications",
		"procedures",
		"administrative",
	}
}
