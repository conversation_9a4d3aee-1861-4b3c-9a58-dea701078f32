"use client"

import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Settings, Bell, Shield, Palette, Database } from "lucide-react"
import { toast } from "sonner"

export default function SettingsPage() {
  const handleSave = () => {
    toast.success("设置已保存")
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">设置</h1>
          <p className="text-gray-600">管理您的账户设置和偏好</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 通知设置 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Bell className="h-5 w-5" />
                <span>通知设置</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="email-notifications">邮件通知</Label>
                  <p className="text-sm text-gray-500">接收查询完成和系统更新通知</p>
                </div>
                <Switch id="email-notifications" defaultChecked />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="query-notifications">查询通知</Label>
                  <p className="text-sm text-gray-500">查询执行完成时发送通知</p>
                </div>
                <Switch id="query-notifications" defaultChecked />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="system-notifications">系统通知</Label>
                  <p className="text-sm text-gray-500">系统维护和更新通知</p>
                </div>
                <Switch id="system-notifications" />
              </div>
            </CardContent>
          </Card>

          {/* 安全设置 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Shield className="h-5 w-5" />
                <span>安全设置</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="two-factor">双因素认证</Label>
                  <p className="text-sm text-gray-500">为您的账户添加额外安全层</p>
                </div>
                <Switch id="two-factor" />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="session-timeout">会话超时</Label>
                  <p className="text-sm text-gray-500">自动登出非活跃会话</p>
                </div>
                <Switch id="session-timeout" defaultChecked />
              </div>

              <Button variant="outline" className="w-full">
                更改密码
              </Button>
            </CardContent>
          </Card>

          {/* 界面设置 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Palette className="h-5 w-5" />
                <span>界面设置</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="dark-mode">深色模式</Label>
                  <p className="text-sm text-gray-500">使用深色主题</p>
                </div>
                <Switch id="dark-mode" />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="compact-view">紧凑视图</Label>
                  <p className="text-sm text-gray-500">减少界面间距</p>
                </div>
                <Switch id="compact-view" />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="auto-save">自动保存</Label>
                  <p className="text-sm text-gray-500">自动保存查询草稿</p>
                </div>
                <Switch id="auto-save" defaultChecked />
              </div>
            </CardContent>
          </Card>

          {/* 数据设置 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Database className="h-5 w-5" />
                <span>数据设置</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="cache-results">缓存查询结果</Label>
                  <p className="text-sm text-gray-500">提高重复查询性能</p>
                </div>
                <Switch id="cache-results" defaultChecked />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="export-format">默认导出格式</Label>
                  <p className="text-sm text-gray-500">CSV格式导出</p>
                </div>
                <Switch id="export-format" defaultChecked />
              </div>

              <Button variant="outline" className="w-full">
                清除缓存
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* 开发模式提示 */}
        {process.env.NEXT_PUBLIC_DEV_MODE === 'true' && (
          <Card className="border-yellow-200 bg-yellow-50">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2 text-yellow-800">
                <Settings className="h-5 w-5" />
                <span>开发模式</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-yellow-700">
                当前处于开发模式，所有功能都可用，权限检查已绕过。
                在生产环境中，某些设置可能需要管理员权限。
              </p>
            </CardContent>
          </Card>
        )}

        <div className="flex justify-end space-x-4">
          <Button variant="outline">重置</Button>
          <Button onClick={handleSave}>保存设置</Button>
        </div>
      </div>
    </DashboardLayout>
  )
}
