package model

import (
	"time"

	"github.com/google/uuid"
)

// Permission 权限模型
type Permission struct {
	ID          uuid.UUID `json:"id" db:"id"`
	Name        string    `json:"name" db:"name"`
	Code        string    `json:"code" db:"code"`
	Description string    `json:"description" db:"description"`
	Resource    string    `json:"resource" db:"resource"` // 资源类型：dataset, query, export等
	Action      string    `json:"action" db:"action"`     // 操作类型：read, write, delete等
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

// Role 角色模型
type Role struct {
	ID          uuid.UUID    `json:"id" db:"id"`
	Name        string       `json:"name" db:"name"`
	Code        string       `json:"code" db:"code"`
	Description string       `json:"description" db:"description"`
	Level       int          `json:"level" db:"level"`         // 角色级别，数字越大权限越高
	IsSystem    bool         `json:"is_system" db:"is_system"` // 是否为系统角色
	Permissions []Permission `json:"permissions,omitempty"`
	CreatedAt   time.Time    `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time    `json:"updated_at" db:"updated_at"`
}

// RolePermission 角色权限关联
type RolePermission struct {
	ID           uuid.UUID `json:"id" db:"id"`
	RoleID       uuid.UUID `json:"role_id" db:"role_id"`
	PermissionID uuid.UUID `json:"permission_id" db:"permission_id"`
	CreatedAt    time.Time `json:"created_at" db:"created_at"`
}

// UserRole 用户角色关联
type UserRole struct {
	ID        uuid.UUID `json:"id" db:"id"`
	UserID    uuid.UUID `json:"user_id" db:"user_id"`
	RoleID    uuid.UUID `json:"role_id" db:"role_id"`
	CreatedAt time.Time `json:"created_at" db:"created_at"`
}

// DatasetPermission 数据集权限
type DatasetPermission struct {
	ID        uuid.UUID `json:"id" db:"id"`
	UserID    uuid.UUID `json:"user_id" db:"user_id"`
	DatasetID string    `json:"dataset_id" db:"dataset_id"`
	Access    string    `json:"access" db:"access"` // read, write, admin
	CreatedAt time.Time `json:"created_at" db:"created_at"`
	UpdatedAt time.Time `json:"updated_at" db:"updated_at"`
}

// 预定义的权限代码
const (
	// 数据集权限
	PermissionDatasetRead  = "dataset:read"
	PermissionDatasetWrite = "dataset:write"
	PermissionDatasetAdmin = "dataset:admin"

	// 查询权限
	PermissionQueryExecute = "query:execute"
	PermissionQuerySave    = "query:save"
	PermissionQueryDelete  = "query:delete"
	PermissionQueryShare   = "query:share"

	// 导出权限
	PermissionExportCreate   = "export:create"
	PermissionExportDownload = "export:download"

	// 用户管理权限
	PermissionUserRead   = "user:read"
	PermissionUserWrite  = "user:write"
	PermissionUserDelete = "user:delete"

	// 系统管理权限
	PermissionSystemAdmin  = "system:admin"
	PermissionSystemConfig = "system:config"
)

// 预定义的角色代码
const (
	RoleUser  = "user"
	RoleAdmin = "admin"
)

// PermissionCheck 权限检查请求
type PermissionCheckRequest struct {
	UserID     uuid.UUID `json:"user_id"`
	Permission string    `json:"permission"`
	Resource   string    `json:"resource,omitempty"`
}

// PermissionCheckResponse 权限检查响应
type PermissionCheckResponse struct {
	Allowed bool   `json:"allowed"`
	Reason  string `json:"reason,omitempty"`
}

// UserPermissions 用户权限信息
type UserPermissions struct {
	UserID      uuid.UUID    `json:"user_id"`
	Roles       []Role       `json:"roles"`
	Permissions []Permission `json:"permissions"`
	Datasets    []string     `json:"datasets"` // 可访问的数据集ID列表
}

// GetDefaultRolePermissions 获取默认角色权限映射
func GetDefaultRolePermissions() map[string][]string {
	return map[string][]string{
		RoleUser: {
			PermissionDatasetRead,
			PermissionQueryExecute,
			PermissionQuerySave,
			PermissionExportCreate,
			PermissionExportDownload,
		},
		RoleAdmin: {
			PermissionDatasetRead,
			PermissionDatasetWrite,
			PermissionDatasetAdmin,
			PermissionQueryExecute,
			PermissionQuerySave,
			PermissionQueryDelete,
			PermissionQueryShare,
			PermissionExportCreate,
			PermissionExportDownload,
			PermissionUserRead,
			PermissionUserWrite,
			PermissionUserDelete,
			PermissionSystemAdmin,
			PermissionSystemConfig,
		},
	}
}

// GetRoleLevel 获取角色级别
func GetRoleLevel(roleCode string) int {
	levels := map[string]int{
		RoleUser:  1,
		RoleAdmin: 10,
	}
	if level, exists := levels[roleCode]; exists {
		return level
	}
	return 0
}
