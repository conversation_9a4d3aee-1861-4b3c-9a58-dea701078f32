#!/bin/bash

# Medical Data Platform Server - Build Script
# 医疗数据平台服务器构建脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 配置变量
APP_NAME="medical-data-platform-server"
BIN_DIR="$PROJECT_ROOT/bin"
BUILD_DIR="$PROJECT_ROOT/build"
OUTPUT_DIR="$PROJECT_ROOT/output"
MAIN_FILE="$PROJECT_ROOT/cmd/server/main.go"

# 构建信息
VERSION=${VERSION:-$(git describe --tags --always --dirty 2>/dev/null || echo "dev")}
BUILD_TIME=$(date -u '+%Y-%m-%d_%H:%M:%S')
GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
LDFLAGS="-ldflags \"-X main.Version=$VERSION -X main.BuildTime=$BUILD_TIME -X main.GitCommit=$GIT_COMMIT\""

# 显示帮助信息
show_help() {
    echo "Medical Data Platform Server - Build Script"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help          显示帮助信息"
    echo "  -c, --clean         构建前清理"
    echo "  -r, --release       发布模式构建（优化）"
    echo "  -d, --debug         调试模式构建"
    echo "  -t, --test          运行测试"
    echo "  -l, --lint          运行代码检查"
    echo "  -f, --format        格式化代码"
    echo "  -o, --output DIR    指定输出目录（默认: output）"
    echo "  --package           打包模式：创建完整的部署包"
    echo "  -v, --verbose       详细输出"
    echo "  --docker            构建Docker镜像"
    echo "  --cross-compile     交叉编译（Linux/Windows/macOS）"
    echo "  --no-deps           跳过依赖检查"
    echo ""
    echo "Examples:"
    echo "  $0                  # 标准构建"
    echo "  $0 -c -r            # 清理后发布构建"
    echo "  $0 -t -l            # 运行测试和代码检查"
    echo "  $0 --docker         # 构建Docker镜像"
    echo "  $0 --cross-compile  # 交叉编译多平台"
    echo "  $0 --package        # 创建完整部署包"
}

# 检查Go环境
check_go_env() {
    log_info "检查Go环境..."
    
    if ! command -v go &> /dev/null; then
        log_error "Go未安装，请安装Go 1.19或更高版本"
        exit 1
    fi
    
    GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
    log_info "Go版本: $GO_VERSION"
    
    # 检查Go版本
    REQUIRED_VERSION="1.19"
    if ! printf '%s\n%s\n' "$REQUIRED_VERSION" "$GO_VERSION" | sort -V -C; then
        log_warning "建议使用Go $REQUIRED_VERSION或更高版本"
    fi
}

# 检查项目结构
check_project_structure() {
    log_info "检查项目结构..."
    
    if [[ ! -f "$MAIN_FILE" ]]; then
        log_error "主文件不存在: $MAIN_FILE"
        exit 1
    fi
    
    if [[ ! -f "$PROJECT_ROOT/go.mod" ]]; then
        log_error "go.mod文件不存在"
        exit 1
    fi
    
    log_success "项目结构检查通过"
}

# 创建必要目录
create_directories() {
    log_info "创建构建目录..."

    mkdir -p "$BIN_DIR"
    mkdir -p "$BUILD_DIR"
    mkdir -p "$OUTPUT_DIR"
    mkdir -p "$PROJECT_ROOT/logs"
    mkdir -p "$PROJECT_ROOT/exports"
    mkdir -p "$PROJECT_ROOT/tmp"

    log_success "目录创建完成"
}

# 清理构建产物
clean_build() {
    log_info "清理构建产物..."
    
    rm -rf "$BIN_DIR"/*
    rm -rf "$BUILD_DIR"/*
    rm -f "$PROJECT_ROOT/coverage.out"
    rm -f "$PROJECT_ROOT/coverage.html"
    
    # 清理Go缓存
    go clean -cache -modcache -testcache
    
    log_success "清理完成"
}

# 安装依赖
install_dependencies() {
    local skip_deps=${1:-false}
    
    if [[ "$skip_deps" == "true" ]]; then
        log_info "跳过依赖检查"
        return 0
    fi
    
    log_info "安装依赖..."
    
    cd "$PROJECT_ROOT"
    go mod download
    go mod tidy
    go mod verify
    
    log_success "依赖安装完成"
}

# 格式化代码
format_code() {
    log_info "格式化代码..."
    
    cd "$PROJECT_ROOT"
    go fmt ./...
    
    # 如果安装了goimports，使用它
    if command -v goimports &> /dev/null; then
        find . -name "*.go" -not -path "./vendor/*" -exec goimports -w {} \;
        log_info "使用goimports格式化完成"
    fi
    
    log_success "代码格式化完成"
}

# 代码检查
lint_code() {
    log_info "运行代码检查..."
    
    cd "$PROJECT_ROOT"
    
    # go vet
    log_info "运行go vet..."
    go vet ./...
    
    # golangci-lint
    if command -v golangci-lint &> /dev/null; then
        log_info "运行golangci-lint..."
        golangci-lint run
    else
        log_warning "golangci-lint未安装，跳过高级代码检查"
        log_info "安装命令: go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest"
    fi
    
    log_success "代码检查完成"
}

# 运行测试
run_tests() {
    log_info "运行测试..."
    
    cd "$PROJECT_ROOT"
    
    # 单元测试
    log_info "运行单元测试..."
    go test -v -short ./...
    
    # 生成覆盖率报告
    log_info "生成测试覆盖率报告..."
    go test -v -coverprofile=coverage.out ./...
    
    if [[ -f "coverage.out" ]]; then
        go tool cover -html=coverage.out -o coverage.html
        COVERAGE=$(go tool cover -func=coverage.out | grep total | awk '{print $3}')
        log_info "测试覆盖率: $COVERAGE"
        log_info "覆盖率报告: coverage.html"
    fi
    
    log_success "测试完成"
}

# 标准构建
build_standard() {
    local output_dir=${1:-$BIN_DIR}
    local verbose=${2:-false}

    log_info "开始标准构建..."

    cd "$PROJECT_ROOT"

    local build_cmd="go build"
    if [[ "$verbose" == "true" ]]; then
        build_cmd="$build_cmd -v"
    fi

    eval "$build_cmd $LDFLAGS -o $output_dir/server $MAIN_FILE"

    log_success "标准构建完成: $output_dir/server"
}

# 发布构建（优化）
build_release() {
    local output_dir=${1:-$BIN_DIR}
    local verbose=${2:-false}

    log_info "开始发布构建（优化模式）..."

    cd "$PROJECT_ROOT"

    local build_cmd="go build"
    if [[ "$verbose" == "true" ]]; then
        build_cmd="$build_cmd -v"
    fi

    # 发布模式：禁用调试信息，启用优化
    local release_ldflags="$LDFLAGS -s -w"

    eval "$build_cmd $release_ldflags -o $output_dir/server $MAIN_FILE"

    # 压缩二进制文件（如果安装了upx）
    if command -v upx &> /dev/null; then
        log_info "使用UPX压缩二进制文件..."
        upx --best "$output_dir/server" || log_warning "UPX压缩失败"
    fi

    log_success "发布构建完成: $output_dir/server"
}

# 调试构建
build_debug() {
    local output_dir=${1:-$BIN_DIR}
    local verbose=${2:-false}

    log_info "开始调试构建..."

    cd "$PROJECT_ROOT"

    local build_cmd="go build"
    if [[ "$verbose" == "true" ]]; then
        build_cmd="$build_cmd -v"
    fi

    # 调试模式：保留调试信息，禁用优化
    local debug_ldflags="$LDFLAGS -X main.Debug=true"

    eval "$build_cmd -gcflags=\"-N -l\" $debug_ldflags -o $output_dir/server-debug $MAIN_FILE"

    log_success "调试构建完成: $output_dir/server-debug"
}

# 交叉编译
cross_compile() {
    local output_dir=${1:-$BUILD_DIR}

    log_info "开始交叉编译..."

    cd "$PROJECT_ROOT"

    # 定义目标平台
    local platforms=(
        "linux/amd64"
        "linux/arm64"
        "windows/amd64"
        "darwin/amd64"
        "darwin/arm64"
    )

    for platform in "${platforms[@]}"; do
        local os_arch=(${platform//\// })
        local goos=${os_arch[0]}
        local goarch=${os_arch[1]}

        local output_name="server-$goos-$goarch"
        if [[ $goos == "windows" ]]; then
            output_name="$output_name.exe"
        fi

        log_info "构建 $goos/$goarch..."

        env GOOS=$goos GOARCH=$goarch go build $LDFLAGS -o "$output_dir/$output_name" "$MAIN_FILE"

        if [[ $? -eq 0 ]]; then
            log_success "✓ $goos/$goarch -> $output_dir/$output_name"
        else
            log_error "✗ $goos/$goarch 构建失败"
        fi
    done

    log_success "交叉编译完成"
}

# 构建Docker镜像
build_docker() {
    log_info "构建Docker镜像..."

    cd "$PROJECT_ROOT"

    if [[ ! -f "Dockerfile" ]]; then
        log_error "Dockerfile不存在"
        return 1
    fi

    local image_name="$APP_NAME:$VERSION"
    local latest_name="$APP_NAME:latest"

    log_info "构建镜像: $image_name"
    docker build -t "$image_name" .

    log_info "标记为latest: $latest_name"
    docker tag "$image_name" "$latest_name"

    log_success "Docker镜像构建完成"
    log_info "镜像标签:"
    log_info "  - $image_name"
    log_info "  - $latest_name"
}

# 创建部署包
create_deployment_package() {
    local output_dir=${1:-$OUTPUT_DIR}
    local binary_path=${2:-"$BIN_DIR/server"}

    log_info "创建部署包到: $output_dir"

    # 清理并创建输出目录
    rm -rf "$output_dir"
    mkdir -p "$output_dir"

    # 创建子目录结构
    mkdir -p "$output_dir/bin"
    mkdir -p "$output_dir/configs"
    mkdir -p "$output_dir/logs"
    mkdir -p "$output_dir/exports"
    mkdir -p "$output_dir/tmp"
    mkdir -p "$output_dir/scripts"
    mkdir -p "$output_dir/migrations"

    # 复制二进制文件
    if [[ -f "$binary_path" ]]; then
        cp "$binary_path" "$output_dir/bin/"
        log_info "✓ 复制二进制文件: bin/server"
    else
        log_error "二进制文件不存在: $binary_path"
        return 1
    fi

    # 复制配置文件
    if [[ -d "$PROJECT_ROOT/configs" ]]; then
        cp -r "$PROJECT_ROOT/configs"/* "$output_dir/configs/" 2>/dev/null || true
        log_info "✓ 复制配置文件"
    fi

    # 复制数据库迁移文件
    if [[ -d "$PROJECT_ROOT/migrations" ]]; then
        cp -r "$PROJECT_ROOT/migrations"/* "$output_dir/migrations/" 2>/dev/null || true
        log_info "✓ 复制数据库迁移文件"
    fi

    # 复制现有的操作脚本
    if [[ -d "$PROJECT_ROOT/scripts" ]]; then
        # 复制除了build.sh之外的所有脚本
        for script_file in "$PROJECT_ROOT/scripts"/*.sh; do
            if [[ -f "$script_file" ]]; then
                script_name=$(basename "$script_file")
                # 跳过build.sh，因为它是构建脚本，不需要在部署包中
                if [[ "$script_name" != "build.sh" ]]; then
                    cp "$script_file" "$output_dir/scripts/"
                    log_info "✓ 复制操作脚本: scripts/$script_name"
                fi
            fi
        done
    fi

    # 复制其他有用的脚本文件（如果存在于项目根目录）
    for script_name in "start.sh" "stop.sh" "restart.sh"; do
        if [[ -f "$PROJECT_ROOT/$script_name" ]]; then
            cp "$PROJECT_ROOT/$script_name" "$output_dir/scripts/legacy_$script_name"
            log_info "✓ 复制根目录脚本: scripts/legacy_$script_name"
        fi
    done

    # 创建启动脚本
    create_package_start_script "$output_dir"

    # 创建停止脚本
    create_package_stop_script "$output_dir"

    # 创建重启脚本
    create_package_restart_script "$output_dir"

    # 创建配置脚本
    create_package_config_script "$output_dir"

    # 复制README和文档
    if [[ -f "$PROJECT_ROOT/README.md" ]]; then
        cp "$PROJECT_ROOT/README.md" "$output_dir/"
        log_info "✓ 复制README文件"
    fi

    # 复制文档目录（如果存在）
    if [[ -d "$PROJECT_ROOT/doc" ]]; then
        mkdir -p "$output_dir/doc"
        cp -r "$PROJECT_ROOT/doc"/* "$output_dir/doc/" 2>/dev/null || true
        log_info "✓ 复制文档目录"
    fi

    # 复制Docker相关文件
    for docker_file in "Dockerfile" "docker-compose.yml" "docker-compose.prod.yml"; do
        if [[ -f "$PROJECT_ROOT/$docker_file" ]]; then
            cp "$PROJECT_ROOT/$docker_file" "$output_dir/"
            log_info "✓ 复制Docker文件: $docker_file"
        fi
    done

    # 复制nginx配置（如果存在）
    if [[ -d "$PROJECT_ROOT/nginx" ]]; then
        mkdir -p "$output_dir/nginx"
        cp -r "$PROJECT_ROOT/nginx"/* "$output_dir/nginx/" 2>/dev/null || true
        log_info "✓ 复制nginx配置"
    fi

    # 复制Makefile（如果存在）
    if [[ -f "$PROJECT_ROOT/Makefile" ]]; then
        cp "$PROJECT_ROOT/Makefile" "$output_dir/"
        log_info "✓ 复制Makefile"
    fi

    # 复制环境变量示例文件
    for env_file in ".env.example" "env.example"; do
        if [[ -f "$PROJECT_ROOT/$env_file" ]]; then
            cp "$PROJECT_ROOT/$env_file" "$output_dir/"
            log_info "✓ 复制环境变量示例: $env_file"
        fi
        if [[ -f "$PROJECT_ROOT/configs/$env_file" ]]; then
            cp "$PROJECT_ROOT/configs/$env_file" "$output_dir/configs/"
            log_info "✓ 复制配置目录中的环境变量示例: $env_file"
        fi
    done

    # 创建部署说明
    create_deployment_readme "$output_dir"

    # 设置权限
    chmod +x "$output_dir/bin/server"
    chmod +x "$output_dir/scripts"/*.sh

    log_success "部署包创建完成: $output_dir"
}

# 创建部署包的启动脚本
create_package_start_script() {
    local output_dir=$1
    local script_file="$output_dir/scripts/start.sh"

    cat > "$script_file" << 'EOF'
#!/bin/bash

# Medical Data Platform Server - Start Script (Deployment Package)
# 医疗数据平台服务器启动脚本（部署包）

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
APP_ROOT="$(dirname "$SCRIPT_DIR")"
PID_FILE="$APP_ROOT/medical-platform.pid"
BINARY="$APP_ROOT/bin/server"
CONFIG_FILE="$APP_ROOT/configs/config.yaml"

# 检查二进制文件
if [[ ! -f "$BINARY" ]]; then
    log_error "服务器二进制文件不存在: $BINARY"
    exit 1
fi

# 检查配置文件
if [[ ! -f "$CONFIG_FILE" ]]; then
    log_warning "配置文件不存在: $CONFIG_FILE"
    log_info "将使用默认配置"
fi

# 检查是否已经运行
if [[ -f "$PID_FILE" ]]; then
    PID=$(cat "$PID_FILE")
    if kill -0 "$PID" 2>/dev/null; then
        log_error "服务器已经在运行 (PID: $PID)"
        exit 1
    else
        log_warning "清理过期的PID文件"
        rm -f "$PID_FILE"
    fi
fi

# 设置环境变量
export LOG_LEVEL=${LOG_LEVEL:-info}
export GIN_MODE=${GIN_MODE:-release}
export SERVER_PORT=${SERVER_PORT:-8088}

log_info "启动医疗数据平台服务器..."
log_info "配置:"
log_info "  - 二进制文件: $BINARY"
log_info "  - 配置文件: $CONFIG_FILE"
log_info "  - 日志级别: $LOG_LEVEL"
log_info "  - 服务端口: $SERVER_PORT"
log_info "  - PID文件: $PID_FILE"

# 切换到应用目录
cd "$APP_ROOT"

# 启动服务器
if [[ "$1" == "--foreground" || "$1" == "-f" ]]; then
    # 前台运行
    log_info "前台模式启动..."
    exec "$BINARY"
else
    # 后台运行
    log_info "后台模式启动..."
    nohup "$BINARY" > logs/app.log 2>&1 &
    echo $! > "$PID_FILE"

    # 等待启动
    sleep 2

    if kill -0 $(cat "$PID_FILE") 2>/dev/null; then
        log_success "服务器启动成功 (PID: $(cat "$PID_FILE"))"
        log_info "健康检查: http://localhost:$SERVER_PORT/health"
    else
        log_error "服务器启动失败"
        rm -f "$PID_FILE"
        exit 1
    fi
fi
EOF

    log_info "✓ 创建启动脚本: scripts/start.sh"
}

# 创建部署包的停止脚本
create_package_stop_script() {
    local output_dir=$1
    local script_file="$output_dir/scripts/stop.sh"

    cat > "$script_file" << 'EOF'
#!/bin/bash

# Medical Data Platform Server - Stop Script (Deployment Package)
# 医疗数据平台服务器停止脚本（部署包）

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
APP_ROOT="$(dirname "$SCRIPT_DIR")"
PID_FILE="$APP_ROOT/medical-platform.pid"

# 显示帮助
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo "Options:"
    echo "  -f, --force     强制停止（使用SIGKILL）"
    echo "  -t, --timeout   超时时间（默认30秒）"
    echo "  -h, --help      显示帮助"
}

# 停止服务
stop_service() {
    local force_kill=${1:-false}
    local timeout=${2:-30}

    log_info "停止医疗数据平台服务器..."

    # 检查PID文件
    if [[ ! -f "$PID_FILE" ]]; then
        log_warning "PID文件不存在: $PID_FILE"

        # 尝试通过进程名查找
        local pids=$(pgrep -f "medical-data-platform" 2>/dev/null || true)
        if [[ -z "$pids" ]]; then
            log_warning "未找到运行中的服务进程"
            return 0
        else
            log_info "通过进程名找到运行中的服务: PIDs $pids"
            for pid in $pids; do
                stop_process_by_pid "$pid" "$force_kill" "$timeout"
            done
            return 0
        fi
    fi

    # 读取PID
    local pid=$(cat "$PID_FILE")
    if [[ -z "$pid" ]]; then
        log_error "PID文件为空"
        return 1
    fi

    # 检查进程是否存在
    if ! kill -0 "$pid" 2>/dev/null; then
        log_warning "进程 $pid 不存在，清理PID文件"
        rm -f "$PID_FILE"
        return 0
    fi

    # 停止进程
    stop_process_by_pid "$pid" "$force_kill" "$timeout"

    # 清理PID文件
    rm -f "$PID_FILE"
    log_success "PID文件已清理"
}

# 根据PID停止进程
stop_process_by_pid() {
    local pid=$1
    local force_kill=${2:-false}
    local timeout=${3:-30}

    log_info "正在停止进程 $pid..."

    if [[ "$force_kill" == "true" ]]; then
        log_warning "强制停止进程 $pid"
        kill -KILL "$pid" 2>/dev/null || true
        sleep 1
    else
        log_info "发送SIGTERM信号到进程 $pid"
        kill -TERM "$pid" 2>/dev/null || true

        # 等待进程退出
        local count=0
        while kill -0 "$pid" 2>/dev/null && [[ $count -lt $timeout ]]; do
            sleep 1
            count=$((count + 1))
            if [[ $((count % 5)) -eq 0 ]]; then
                log_info "等待进程退出... ($count/${timeout}s)"
            fi
        done

        # 检查进程是否仍在运行
        if kill -0 "$pid" 2>/dev/null; then
            log_warning "进程未在超时时间内退出，发送SIGKILL信号"
            kill -KILL "$pid" 2>/dev/null || true
            sleep 1
        fi
    fi

    # 最终检查
    if kill -0 "$pid" 2>/dev/null; then
        log_error "无法停止进程 $pid"
        return 1
    else
        log_success "进程 $pid 已停止"
        return 0
    fi
}

# 主函数
main() {
    local force_kill=false
    local timeout=30

    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -f|--force)
                force_kill=true
                shift
                ;;
            -t|--timeout)
                timeout="$2"
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done

    stop_service "$force_kill" "$timeout"
    log_success "医疗数据平台服务已停止"
}

main "$@"
EOF

    log_info "✓ 创建停止脚本: scripts/stop.sh"
}

# 创建部署包的重启脚本
create_package_restart_script() {
    local output_dir=$1
    local script_file="$output_dir/scripts/restart.sh"

    cat > "$script_file" << 'EOF'
#!/bin/bash

# Medical Data Platform Server - Restart Script (Deployment Package)
# 医疗数据平台服务器重启脚本（部署包）

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 显示帮助
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo "Options:"
    echo "  -f, --force     强制停止后重启"
    echo "  -t, --timeout   停止超时时间（默认30秒）"
    echo "  -w, --wait      停止后等待时间（默认5秒）"
    echo "  -h, --help      显示帮助"
}

# 重启服务
restart_service() {
    local force_kill=${1:-false}
    local timeout=${2:-30}
    local wait_time=${3:-5}

    log_info "重启医疗数据平台服务器..."

    # 停止服务
    log_info "步骤 1/3: 停止服务"
    if [[ "$force_kill" == "true" ]]; then
        bash "$SCRIPT_DIR/stop.sh" --force --timeout "$timeout"
    else
        bash "$SCRIPT_DIR/stop.sh" --timeout "$timeout"
    fi

    # 等待
    log_info "步骤 2/3: 等待 ${wait_time} 秒..."
    sleep "$wait_time"

    # 启动服务
    log_info "步骤 3/3: 启动服务"
    bash "$SCRIPT_DIR/start.sh"

    log_success "服务重启完成"
}

# 主函数
main() {
    local force_kill=false
    local timeout=30
    local wait_time=5

    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -f|--force)
                force_kill=true
                shift
                ;;
            -t|--timeout)
                timeout="$2"
                shift 2
                ;;
            -w|--wait)
                wait_time="$2"
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done

    restart_service "$force_kill" "$timeout" "$wait_time"
}

main "$@"
EOF

    log_info "✓ 创建重启脚本: scripts/restart.sh"
}

# 创建配置脚本
create_package_config_script() {
    local output_dir=$1
    local script_file="$output_dir/scripts/config.sh"

    cat > "$script_file" << 'EOF'
#!/bin/bash

# Medical Data Platform Server - Config Script (Deployment Package)
# 医疗数据平台服务器配置脚本（部署包）

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
APP_ROOT="$(dirname "$SCRIPT_DIR")"

# 显示帮助
show_help() {
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  init            初始化配置文件"
    echo "  check           检查配置"
    echo "  backup          备份配置"
    echo "  restore FILE    恢复配置"
    echo ""
    echo "Options:"
    echo "  -h, --help      显示帮助"
}

# 初始化配置
init_config() {
    log_info "初始化配置文件..."

    local config_file="$APP_ROOT/configs/config.yaml"
    local example_file="$APP_ROOT/configs/config.example.yaml"

    if [[ -f "$config_file" ]]; then
        log_warning "配置文件已存在: $config_file"
        read -p "是否覆盖? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "取消初始化"
            return 0
        fi
    fi

    if [[ -f "$example_file" ]]; then
        cp "$example_file" "$config_file"
        log_success "配置文件已创建: $config_file"
        log_info "请编辑配置文件以适应您的环境"
    else
        log_error "示例配置文件不存在: $example_file"
        return 1
    fi
}

# 检查配置
check_config() {
    log_info "检查配置..."

    local config_file="$APP_ROOT/configs/config.yaml"

    if [[ ! -f "$config_file" ]]; then
        log_error "配置文件不存在: $config_file"
        log_info "运行 '$0 init' 来创建配置文件"
        return 1
    fi

    log_success "配置文件存在: $config_file"

    # 这里可以添加更多的配置验证逻辑
    log_info "配置检查完成"
}

# 备份配置
backup_config() {
    log_info "备份配置文件..."

    local config_file="$APP_ROOT/configs/config.yaml"
    local backup_file="$APP_ROOT/configs/config.backup.$(date +%Y%m%d_%H%M%S).yaml"

    if [[ ! -f "$config_file" ]]; then
        log_error "配置文件不存在: $config_file"
        return 1
    fi

    cp "$config_file" "$backup_file"
    log_success "配置已备份到: $backup_file"
}

# 恢复配置
restore_config() {
    local backup_file=$1

    if [[ -z "$backup_file" ]]; then
        log_error "请指定备份文件"
        return 1
    fi

    if [[ ! -f "$backup_file" ]]; then
        log_error "备份文件不存在: $backup_file"
        return 1
    fi

    local config_file="$APP_ROOT/configs/config.yaml"

    log_info "恢复配置文件..."
    cp "$backup_file" "$config_file"
    log_success "配置已恢复: $config_file"
}

# 主函数
main() {
    if [[ $# -eq 0 ]]; then
        show_help
        exit 1
    fi

    case $1 in
        init)
            init_config
            ;;
        check)
            check_config
            ;;
        backup)
            backup_config
            ;;
        restore)
            restore_config "$2"
            ;;
        -h|--help)
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

main "$@"
EOF

    log_info "✓ 创建配置脚本: scripts/config.sh"
}

# 创建部署说明文档
create_deployment_readme() {
    local output_dir=$1
    local readme_file="$output_dir/DEPLOYMENT.md"

    cat > "$readme_file" << 'EOF'
# 医疗数据平台服务器 - 部署包

这是医疗数据平台服务器的完整部署包，包含了运行服务所需的所有文件。

## 目录结构

```
.
├── bin/                    # 二进制文件
│   └── server             # 服务器可执行文件
├── configs/               # 配置文件
│   ├── config.yaml        # 主配置文件
│   └── config.example.yaml # 配置示例
├── scripts/               # 管理脚本
│   ├── start.sh          # 启动脚本
│   ├── stop.sh           # 停止脚本
│   ├── restart.sh        # 重启脚本
│   └── config.sh         # 配置管理脚本
├── migrations/            # 数据库迁移文件
├── logs/                  # 日志目录
├── exports/               # 导出文件目录
├── tmp/                   # 临时文件目录
└── DEPLOYMENT.md          # 本文档
```

## 快速开始

### 1. 初始化配置

```bash
# 创建配置文件
./scripts/config.sh init

# 编辑配置文件
vi configs/config.yaml
```

### 2. 启动服务

```bash
# 后台启动
./scripts/start.sh

# 前台启动（用于调试）
./scripts/start.sh --foreground
```

### 3. 检查服务状态

```bash
# 健康检查
curl http://localhost:8088/health

# 查看日志
tail -f logs/app.log
```

### 4. 停止服务

```bash
# 优雅停止
./scripts/stop.sh

# 强制停止
./scripts/stop.sh --force
```

### 5. 重启服务

```bash
# 重启服务
./scripts/restart.sh

# 强制重启
./scripts/restart.sh --force
```

## 配置说明

### 环境变量

可以通过环境变量覆盖配置：

- `DATABASE_URL`: 数据库连接URL
- `SERVER_PORT`: 服务器端口（默认8088）
- `LOG_LEVEL`: 日志级别（debug/info/warn/error）
- `GIN_MODE`: Gin模式（debug/release）

### 配置文件

主配置文件位于 `configs/config.yaml`，包含：

- 数据库配置
- 服务器配置
- 日志配置
- JWT配置
- 其他业务配置

## 数据库设置

### PostgreSQL

1. 创建数据库：
```sql
CREATE DATABASE medical_platform;
CREATE USER medical_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE medical_platform TO medical_user;
```

2. 运行迁移（如果有迁移工具）：
```bash
# 使用golang-migrate
migrate -path migrations -database "postgres://user:pass@localhost/dbname?sslmode=disable" up
```

## 监控和日志

### 日志文件

- `logs/app.log`: 应用日志
- `logs/gin.log`: HTTP请求日志

### 健康检查

- 端点: `GET /health`
- 返回: JSON格式的健康状态

### API文档

- Swagger UI: `http://localhost:8088/swagger/index.html`

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   lsof -i :8088
   ```

2. **权限问题**
   ```bash
   chmod +x bin/server
   chmod +x scripts/*.sh
   ```

3. **配置文件错误**
   ```bash
   ./scripts/config.sh check
   ```

4. **数据库连接失败**
   - 检查数据库是否运行
   - 验证连接配置
   - 检查网络连接

### 日志分析

```bash
# 查看错误日志
grep ERROR logs/app.log

# 实时监控日志
tail -f logs/app.log | grep ERROR

# 查看最近的日志
tail -100 logs/app.log
```

## 性能优化

### 系统要求

- CPU: 2核心以上
- 内存: 4GB以上
- 磁盘: 10GB可用空间
- 网络: 稳定的网络连接

### 调优建议

1. **数据库连接池**
   - 根据并发需求调整连接池大小

2. **日志级别**
   - 生产环境使用 `info` 或 `warn` 级别

3. **Gin模式**
   - 生产环境设置为 `release` 模式

## 安全建议

1. **配置文件权限**
   ```bash
   chmod 600 configs/config.yaml
   ```

2. **JWT密钥**
   - 使用强随机密钥
   - 定期轮换密钥

3. **数据库安全**
   - 使用专用数据库用户
   - 限制数据库权限
   - 启用SSL连接

4. **网络安全**
   - 使用防火墙限制访问
   - 启用HTTPS
   - 定期更新系统

## 备份和恢复

### 配置备份

```bash
./scripts/config.sh backup
```

### 数据库备份

```bash
pg_dump medical_platform > backup.sql
```

### 恢复配置

```bash
./scripts/config.sh restore backup_file.yaml
```

## 支持

如有问题，请查看：

1. 应用日志: `logs/app.log`
2. 配置文档: `configs/config.example.yaml`
3. 项目文档: 项目仓库的文档目录

---

**注意**: 这是一个部署包，包含预编译的二进制文件。如需修改源代码，请返回开发环境。
EOF

    log_info "✓ 创建部署说明: DEPLOYMENT.md"
}

# 显示构建信息
show_build_info() {
    echo "========================================"
    echo "  医疗数据平台服务器 - 构建脚本"
    echo "========================================"
    echo "项目: $APP_NAME"
    echo "版本: $VERSION"
    echo "构建时间: $BUILD_TIME"
    echo "Git提交: $GIT_COMMIT"
    echo "输出目录: $(if [[ "$package_mode" == "true" ]]; then echo "$OUTPUT_DIR"; else echo "$BIN_DIR"; fi)"
    echo "========================================"
}

# 显示构建结果
show_build_result() {
    local output_dir=${1:-$BIN_DIR}

    log_info "构建结果:"

    if [[ -d "$output_dir" ]]; then
        find "$output_dir" -name "server*" -type f -exec ls -lh {} \; | while read -r line; do
            log_info "  $line"
        done
    fi

    echo ""
    log_success "构建完成！"
    echo ""
    echo "运行服务器:"
    echo "  $output_dir/server"
    echo ""
    echo "查看版本信息:"
    echo "  $output_dir/server --version"
    echo ""
}

# 显示打包结果
show_package_result() {
    local output_dir=${1:-$OUTPUT_DIR}

    log_info "部署包内容:"

    if [[ -d "$output_dir" ]]; then
        echo ""
        echo "目录结构:"
        tree "$output_dir" 2>/dev/null || find "$output_dir" -type f | head -20
        echo ""

        log_info "二进制文件:"
        if [[ -f "$output_dir/bin/server" ]]; then
            ls -lh "$output_dir/bin/server"
        fi

        echo ""
        log_info "管理脚本:"
        ls -la "$output_dir/scripts/"*.sh 2>/dev/null || true

        echo ""
        log_info "配置文件:"
        ls -la "$output_dir/configs/" 2>/dev/null || true
    fi

    echo ""
    log_success "部署包创建完成！"
    echo ""
    echo "部署包位置: $output_dir"
    echo ""
    echo "使用方法:"
    echo "  1. 复制整个 $output_dir 目录到目标服务器"
    echo "  2. 初始化配置: cd $output_dir && ./scripts/config.sh init"
    echo "  3. 编辑配置文件: vi configs/config.yaml"
    echo "  4. 启动服务: ./scripts/start.sh"
    echo "  5. 检查状态: curl http://localhost:8088/health"
    echo ""
    echo "管理命令:"
    echo "  启动: ./scripts/start.sh"
    echo "  停止: ./scripts/stop.sh"
    echo "  重启: ./scripts/restart.sh"
    echo "  配置: ./scripts/config.sh"
    echo ""
}

# 主函数
main() {
    local clean_build=false
    local release_mode=false
    local debug_mode=false
    local run_tests=false
    local run_lint=false
    local format_code_flag=false
    local output_dir="$BIN_DIR"
    local verbose=false
    local docker_build=false
    local cross_compile_flag=false
    local skip_deps=false
    local package_mode=true

    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -c|--clean)
                clean_build=true
                shift
                ;;
            -r|--release)
                release_mode=true
                shift
                ;;
            -d|--debug)
                debug_mode=true
                shift
                ;;
            -t|--test)
                run_tests=true
                shift
                ;;
            -l|--lint)
                run_lint=true
                shift
                ;;
            -f|--format)
                format_code_flag=true
                shift
                ;;
            -o|--output)
                output_dir="$2"
                if [[ -z "$output_dir" ]]; then
                    log_error "输出目录不能为空"
                    exit 1
                fi
                shift 2
                ;;
            -v|--verbose)
                verbose=true
                shift
                ;;
            --docker)
                docker_build=true
                shift
                ;;
            --cross-compile)
                cross_compile_flag=true
                shift
                ;;
            --no-deps)
                skip_deps=true
                shift
                ;;
            --package)
                package_mode=true
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done

    # 显示构建信息
    show_build_info

    # 基础检查
    check_go_env
    check_project_structure

    # 清理（如果需要）
    if [[ "$clean_build" == "true" ]]; then
        clean_build
    fi

    # 创建目录
    create_directories

    # 安装依赖
    install_dependencies "$skip_deps"

    # 格式化代码（如果需要）
    if [[ "$format_code_flag" == "true" ]]; then
        format_code
    fi

    # 代码检查（如果需要）
    if [[ "$run_lint" == "true" ]]; then
        lint_code
    fi

    # 运行测试（如果需要）
    if [[ "$run_tests" == "true" ]]; then
        run_tests
    fi

    # 构建
    if [[ "$docker_build" == "true" ]]; then
        build_docker
    elif [[ "$cross_compile_flag" == "true" ]]; then
        cross_compile "$output_dir"
    elif [[ "$package_mode" == "true" ]]; then
        # 打包模式：先构建，再创建部署包
        if [[ "$release_mode" == "true" ]]; then
            build_release "$BIN_DIR" "$verbose"
        elif [[ "$debug_mode" == "true" ]]; then
            build_debug "$BIN_DIR" "$verbose"
        else
            build_standard "$BIN_DIR" "$verbose"
        fi
        create_deployment_package "$OUTPUT_DIR" "$BIN_DIR/server"
    elif [[ "$release_mode" == "true" ]]; then
        build_release "$output_dir" "$verbose"
    elif [[ "$debug_mode" == "true" ]]; then
        build_debug "$output_dir" "$verbose"
    else
        build_standard "$output_dir" "$verbose"
    fi

    # 显示结果
    if [[ "$docker_build" != "true" ]]; then
        if [[ "$package_mode" == "true" ]]; then
            show_package_result "$OUTPUT_DIR"
        else
            show_build_result "$output_dir"
        fi
    fi
}

# 信号处理
trap 'log_error "构建被中断"; exit 1' INT TERM

# 执行主函数
main "$@"
