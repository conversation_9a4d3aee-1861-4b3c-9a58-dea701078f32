package model

import (
	"encoding/json"
	"time"

	"github.com/google/uuid"
)

// 医学字段分类
type MedicalFieldCategory struct {
	ID          string         `json:"id"`
	Name        string         `json:"name"`
	Description string         `json:"description"`
	Icon        string         `json:"icon"`
	Fields      []MedicalField `json:"fields"`
}

// 医学字段
type MedicalField struct {
	ID          string   `json:"id"`
	Name        string   `json:"name"`
	NameEn      string   `json:"nameEn"`
	Type        string   `json:"type"`
	Description string   `json:"description"`
	Table       string   `json:"table"`
	Category    string   `json:"category"`
	Unit        *string  `json:"unit,omitempty"`
	Examples    []string `json:"examples,omitempty"`
	IsRequired  bool     `json:"isRequired"`
}

// 人群筛选条件
type CohortCriteria struct {
	ID          string      `json:"id"`
	Category    string      `json:"category"`
	Field       string      `json:"field"`
	Operator    string      `json:"operator"`
	Value       interface{} `json:"value"`
	Description string      `json:"description"`
	Logic       *string     `json:"logic,omitempty"` // AND, OR
}

// 数据维度
type DataDimension struct {
	Category   string         `json:"category"`
	Fields     []MedicalField `json:"fields"`
	IsSelected bool           `json:"isSelected"`
}

// 时间范围
type MedicalTimeRange struct {
	Start *string `json:"start,omitempty"`
	End   *string `json:"end,omitempty"`
	Type  string  `json:"type"` // admission, discharge, lab, custom
}

// 医学查询请求
type MedicalQueryRequest struct {
	StudyName      string           `json:"studyName" validate:"required,min=1,max=200"`
	CohortCriteria []CohortCriteria `json:"cohortCriteria"`
	DataDimensions []DataDimension  `json:"dataDimensions" validate:"required,min=1"`
	TimeRange      MedicalTimeRange `json:"timeRange"`
	OutputFormat   string           `json:"outputFormat" validate:"oneof=csv excel json"`
	MaxRecords     int              `json:"maxRecords" validate:"min=1,max=50000"`
}

// 医学查询响应
type MedicalQueryResponse struct {
	QueryID       uuid.UUID                `json:"queryId"`
	StudyName     string                   `json:"studyName"`
	Data          []map[string]interface{} `json:"data"`
	Total         int                      `json:"total"`
	ExecutionTime int                      `json:"executionTime"` // milliseconds
	Status        string                   `json:"status"`
	Metadata      map[string]interface{}   `json:"metadata"`
	DataSource    *DataSourceInfo          `json:"dataSource,omitempty"`
	Error         *QueryError              `json:"error,omitempty"`
}

// DataSourceInfo 数据源信息
type DataSourceInfo struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	IsHealthy   bool   `json:"isHealthy"`
	Description string `json:"description"`
	Version     string `json:"version"`
}

// QueryError 查询错误信息
type QueryError struct {
	Code        string      `json:"code"`
	Message     string      `json:"message"`
	Details     interface{} `json:"details,omitempty"`
	Recoverable bool        `json:"recoverable"`
}

// 人群验证请求
type CohortValidationRequest struct {
	Criteria []CohortCriteria `json:"criteria" validate:"required,min=1"`
}

// 人群验证响应
type CohortValidationResponse struct {
	IsValid        bool     `json:"isValid"`
	EstimatedCount int      `json:"estimatedCount"`
	Warnings       []string `json:"warnings,omitempty"`
	Errors         []string `json:"errors,omitempty"`
	ValidationTime int      `json:"validationTime"` // milliseconds
}

// 结果估算响应
type ResultEstimationResponse struct {
	EstimatedRows   int                    `json:"estimatedRows"`
	EstimatedSize   string                 `json:"estimatedSize"`   // "1.2MB"
	ComplexityScore int                    `json:"complexityScore"` // 1-10
	ExecutionTime   int                    `json:"executionTime"`   // estimated milliseconds
	Warnings        []string               `json:"warnings,omitempty"`
	Recommendations []string               `json:"recommendations,omitempty"`
	Metadata        map[string]interface{} `json:"metadata"`
}

// 查询模板
type MedicalQueryTemplate struct {
	ID          uuid.UUID       `json:"id" db:"id"`
	Name        string          `json:"name" db:"name"`
	Description string          `json:"description" db:"description"`
	Category    string          `json:"category" db:"category"`
	QueryConfig json.RawMessage `json:"queryConfig" db:"query_config"`
	CreatedBy   uuid.UUID       `json:"createdBy" db:"created_by"`
	CreatedAt   time.Time       `json:"createdAt" db:"created_at"`
	UpdatedAt   time.Time       `json:"updatedAt" db:"updated_at"`
	IsPublic    bool            `json:"isPublic" db:"is_public"`
	UsageCount  int             `json:"usageCount" db:"usage_count"`
	Tags        []string        `json:"tags"`
}

// 保存模板请求
type SaveTemplateRequest struct {
	Name        string              `json:"name" validate:"required,min=1,max=100"`
	Description string              `json:"description" validate:"max=500"`
	Category    string              `json:"category" validate:"required"`
	QueryConfig MedicalQueryRequest `json:"queryConfig" validate:"required"`
	IsPublic    bool                `json:"isPublic"`
	Tags        []string            `json:"tags"`
}

// 查询模板响应
type QueryTemplatesResponse struct {
	Templates  []MedicalQueryTemplate `json:"templates"`
	Pagination PaginationInfo         `json:"pagination"`
}

// 医学查询历史
type MedicalQueryHistory struct {
	ID            uuid.UUID       `json:"id" db:"id"`
	UserID        uuid.UUID       `json:"userId" db:"user_id"`
	StudyName     string          `json:"studyName" db:"study_name"`
	QueryConfig   json.RawMessage `json:"queryConfig" db:"query_config"`
	Status        string          `json:"status" db:"status"`
	RecordCount   *int            `json:"recordCount,omitempty" db:"record_count"`
	ExecutionTime *int            `json:"executionTime,omitempty" db:"execution_time"`
	ErrorMessage  *string         `json:"errorMessage,omitempty" db:"error_message"`
	CreatedAt     time.Time       `json:"createdAt" db:"created_at"`
}

// 医学查询历史响应
type MedicalQueryHistoryResponse struct {
	Queries    []MedicalQueryHistory `json:"queries"`
	Pagination PaginationInfo        `json:"pagination"`
}

// 导出结果
type ExportResult struct {
	QueryID     uuid.UUID `json:"queryId"`
	Format      string    `json:"format"`
	FilePath    string    `json:"filePath"`
	FileSize    int64     `json:"fileSize"`
	RecordCount int       `json:"recordCount"`
	ExportedAt  time.Time `json:"exportedAt"`
	DownloadURL string    `json:"downloadUrl"`
	ExpiresAt   time.Time `json:"expiresAt"`
}

// 智能关联建议
type TableJoinSuggestion struct {
	TargetTable   string   `json:"targetTable"`
	JoinType      string   `json:"joinType"`
	JoinCondition string   `json:"joinCondition"`
	Description   string   `json:"description"`
	Confidence    float64  `json:"confidence"` // 0-1
	Benefits      []string `json:"benefits"`
}

// 查询优化建议
type QueryOptimization struct {
	Type        string `json:"type"` // index, limit, filter, etc.
	Description string `json:"description"`
	Impact      string `json:"impact"` // high, medium, low
	Suggestion  string `json:"suggestion"`
}

// 医学查询统计
type MedicalQueryStats struct {
	TotalQueries      int                   `json:"totalQueries"`
	SuccessfulQueries int                   `json:"successfulQueries"`
	FailedQueries     int                   `json:"failedQueries"`
	AvgExecutionTime  float64               `json:"avgExecutionTime"`
	PopularCategories []CategoryUsageStats  `json:"popularCategories"`
	RecentQueries     []MedicalQueryHistory `json:"recentQueries"`
}

// 分类使用统计
type CategoryUsageStats struct {
	Category   string  `json:"category"`
	Count      int     `json:"count"`
	Percentage float64 `json:"percentage"`
}

// 查询复杂度分析
type QueryComplexityAnalysis struct {
	Score           int      `json:"score"` // 1-10
	Factors         []string `json:"factors"`
	Recommendations []string `json:"recommendations"`
	EstimatedTime   int      `json:"estimatedTime"` // milliseconds
}

// 数据质量报告
type DataQualityReport struct {
	TableName        string   `json:"tableName"`
	TotalRecords     int      `json:"totalRecords"`
	CompleteRecords  int      `json:"completeRecords"`
	CompletenessRate float64  `json:"completenessRate"`
	MissingFields    []string `json:"missingFields"`
	DataIssues       []string `json:"dataIssues"`
}

// 医学术语映射
type MedicalTermMapping struct {
	TechnicalTerm string `json:"technicalTerm"`
	MedicalTerm   string `json:"medicalTerm"`
	Description   string `json:"description"`
	Category      string `json:"category"`
}

// 查询执行计划
type QueryExecutionPlan struct {
	Steps         []ExecutionStep `json:"steps"`
	EstimatedTime int             `json:"estimatedTime"`
	ResourceUsage ResourceUsage   `json:"resourceUsage"`
	Optimizations []string        `json:"optimizations"`
}

// 执行步骤
type ExecutionStep struct {
	StepNumber    int    `json:"stepNumber"`
	Description   string `json:"description"`
	Operation     string `json:"operation"`
	EstimatedTime int    `json:"estimatedTime"`
	Status        string `json:"status"` // pending, running, completed, failed
}

// 资源使用情况
type ResourceUsage struct {
	CPUUsage    float64 `json:"cpuUsage"`
	MemoryUsage float64 `json:"memoryUsage"`
	DiskIO      float64 `json:"diskIO"`
	NetworkIO   float64 `json:"networkIO"`
}
