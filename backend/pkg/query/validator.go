package query

import (
	"fmt"
	"strings"
	"time"
)

// QueryValidator 查询验证器
type QueryValidator struct {
	maxFields     int
	maxFilters    int
	maxJoins      int
	allowedTables map[string]bool
	allowedFields map[string]map[string]bool
}

// NewQueryValidator 创建查询验证器
func NewQueryValidator() *QueryValidator {
	validator := &QueryValidator{
		maxFields:     50,
		maxFilters:    20,
		maxJoins:      10,
		allowedTables: make(map[string]bool),
		allowedFields: make(map[string]map[string]bool),
	}
	validator.initializeAllowedTables()
	return validator
}

// initializeAllowedTables 初始化允许的表
func (qv *QueryValidator) initializeAllowedTables() {
	// MIMIC-IV 允许的表 - 所有31张表
	mimicTables := []string{
		// mimiciv_hosp schema - 22张表
		"admissions", "d_hcpcs", "d_icd_diagnoses", "d_icd_procedures", "d_labitems",
		"diagnoses_icd", "drgcodes", "emar", "emar_detail", "hcpcsevents",
		"labevents", "microbiologyevents", "omr", "patients", "pharmacy",
		"poe", "poe_detail", "prescriptions", "procedures_icd", "provider",
		"services", "transfers",

		// mimiciv_icu schema - 9张表
		"caregiver", "chartevents", "d_items", "datetimeevents", "icustays",
		"ingredientevents", "inputevents", "outputevents", "procedureevents",
	}

	for _, table := range mimicTables {
		qv.allowedTables[table] = true
	}

	// 初始化允许的字段（简化版本）
	qv.allowedFields = map[string]map[string]bool{
		"patients": {
			"subject_id": true, "gender": true, "anchor_age": true,
			"anchor_year": true, "anchor_year_group": true, "dod": true,
		},
		"admissions": {
			"subject_id": true, "hadm_id": true, "admittime": true,
			"dischtime": true, "deathtime": true, "admission_type": true,
			"admission_location": true, "discharge_location": true,
			"insurance": true, "language": true, "marital_status": true, "race": true,
		},
		"labevents": {
			"subject_id": true, "hadm_id": true, "specimen_id": true,
			"itemid": true, "charttime": true, "storetime": true,
			"value": true, "valuenum": true, "valueuom": true,
			"ref_range_lower": true, "ref_range_upper": true,
			"flag": true, "priority": true, "comments": true,
		},
		"diagnoses_icd": {
			"subject_id": true, "hadm_id": true, "seq_num": true,
			"icd_code": true, "icd_version": true,
		},
		"prescriptions": {
			"subject_id": true, "hadm_id": true, "pharmacy_id": true,
			"starttime": true, "stoptime": true, "drug_type": true,
			"drug": true, "formulary_drug_cd": true, "gsn": true,
			"ndc": true, "prod_strength": true, "form_rx": true,
			"route": true, "dose_val_rx": true, "dose_unit_rx": true,
		},
	}
}

// ValidateQuery 验证查询
func (qv *QueryValidator) ValidateQuery(dsl *MedicalQueryDSL) QueryValidationResult {
	var errors []QueryValidationError

	// 基础验证
	if dsl.DatasetID == "" {
		errors = append(errors, QueryValidationError{
			Field:   "dataset_id",
			Message: "Dataset ID is required",
			Code:    "MISSING_DATASET_ID",
		})
	}

	if dsl.Cohort.PrimaryTable == "" {
		errors = append(errors, QueryValidationError{
			Field:   "cohort.primary_table",
			Message: "Primary table is required",
			Code:    "MISSING_PRIMARY_TABLE",
		})
	}

	// 验证表
	tableErrors := qv.validateTables(dsl)
	errors = append(errors, tableErrors...)

	// 验证字段
	fieldErrors := qv.validateFields(dsl)
	errors = append(errors, fieldErrors...)

	// 验证过滤条件
	filterErrors := qv.validateFilters(dsl)
	errors = append(errors, filterErrors...)

	// 验证限制
	limitErrors := qv.validateLimits(dsl)
	errors = append(errors, limitErrors...)

	// 验证时间范围
	timeErrors := qv.validateTimeRange(dsl)
	errors = append(errors, timeErrors...)

	// 验证聚合
	aggErrors := qv.validateAggregations(dsl)
	errors = append(errors, aggErrors...)

	return QueryValidationResult{
		Valid:  len(errors) == 0,
		Errors: errors,
	}
}

// validateTables 验证表
func (qv *QueryValidator) validateTables(dsl *MedicalQueryDSL) []QueryValidationError {
	var errors []QueryValidationError

	// 验证主表
	if !qv.allowedTables[dsl.Cohort.PrimaryTable] {
		errors = append(errors, QueryValidationError{
			Field:   "cohort.primary_table",
			Message: fmt.Sprintf("Table '%s' is not allowed", dsl.Cohort.PrimaryTable),
			Code:    "INVALID_TABLE",
		})
	}

	// 收集所有使用的表
	usedTables := make(map[string]bool)
	usedTables[dsl.Cohort.PrimaryTable] = true

	for _, field := range dsl.Fields {
		if field.Table != "" {
			usedTables[field.Table] = true
		}
	}

	for _, filter := range dsl.Filters {
		if filter.Table != "" {
			usedTables[filter.Table] = true
		}
	}

	// 验证所有使用的表
	for table := range usedTables {
		if !qv.allowedTables[table] {
			errors = append(errors, QueryValidationError{
				Field:   "tables",
				Message: fmt.Sprintf("Table '%s' is not allowed", table),
				Code:    "INVALID_TABLE",
			})
		}
	}

	// 检查表数量限制
	if len(usedTables) > qv.maxJoins {
		errors = append(errors, QueryValidationError{
			Field:   "tables",
			Message: fmt.Sprintf("Too many tables used (%d), maximum allowed is %d", len(usedTables), qv.maxJoins),
			Code:    "TOO_MANY_TABLES",
		})
	}

	return errors
}

// validateFields 验证字段
func (qv *QueryValidator) validateFields(dsl *MedicalQueryDSL) []QueryValidationError {
	var errors []QueryValidationError

	// 检查字段数量
	if len(dsl.Fields) > qv.maxFields {
		errors = append(errors, QueryValidationError{
			Field:   "fields",
			Message: fmt.Sprintf("Too many fields (%d), maximum allowed is %d", len(dsl.Fields), qv.maxFields),
			Code:    "TOO_MANY_FIELDS",
		})
	}

	// 验证每个字段
	for i, field := range dsl.Fields {
		if field.Field == "" {
			errors = append(errors, QueryValidationError{
				Field:   fmt.Sprintf("fields[%d].field", i),
				Message: "Field name is required",
				Code:    "MISSING_FIELD_NAME",
			})
			continue
		}

		if field.Table == "" {
			errors = append(errors, QueryValidationError{
				Field:   fmt.Sprintf("fields[%d].table", i),
				Message: "Table name is required",
				Code:    "MISSING_TABLE_NAME",
			})
			continue
		}

		// 验证字段是否允许
		if tableFields, exists := qv.allowedFields[field.Table]; exists {
			if !tableFields[field.Field] {
				errors = append(errors, QueryValidationError{
					Field:   fmt.Sprintf("fields[%d]", i),
					Message: fmt.Sprintf("Field '%s.%s' is not allowed", field.Table, field.Field),
					Code:    "INVALID_FIELD",
				})
			}
		}

		// 验证函数
		if field.Function != "" {
			if !qv.isValidFunction(field.Function) {
				errors = append(errors, QueryValidationError{
					Field:   fmt.Sprintf("fields[%d].function", i),
					Message: fmt.Sprintf("Function '%s' is not allowed", field.Function),
					Code:    "INVALID_FUNCTION",
				})
			}
		}
	}

	return errors
}

// validateFilters 验证过滤条件
func (qv *QueryValidator) validateFilters(dsl *MedicalQueryDSL) []QueryValidationError {
	var errors []QueryValidationError

	// 检查过滤条件数量
	totalFilters := len(dsl.Filters) + len(dsl.Cohort.InclusionCriteria) + len(dsl.Cohort.ExclusionCriteria)
	if totalFilters > qv.maxFilters {
		errors = append(errors, QueryValidationError{
			Field:   "filters",
			Message: fmt.Sprintf("Too many filters (%d), maximum allowed is %d", totalFilters, qv.maxFilters),
			Code:    "TOO_MANY_FILTERS",
		})
	}

	// 验证过滤条件
	for i, filter := range dsl.Filters {
		if filter.Field == "" {
			errors = append(errors, QueryValidationError{
				Field:   fmt.Sprintf("filters[%d].field", i),
				Message: "Filter field is required",
				Code:    "MISSING_FILTER_FIELD",
			})
		}

		if filter.Operator == "" {
			errors = append(errors, QueryValidationError{
				Field:   fmt.Sprintf("filters[%d].operator", i),
				Message: "Filter operator is required",
				Code:    "MISSING_FILTER_OPERATOR",
			})
		}

		if !qv.isValidOperator(filter.Operator) {
			errors = append(errors, QueryValidationError{
				Field:   fmt.Sprintf("filters[%d].operator", i),
				Message: fmt.Sprintf("Operator '%s' is not valid", filter.Operator),
				Code:    "INVALID_OPERATOR",
			})
		}
	}

	return errors
}

// validateLimits 验证限制
func (qv *QueryValidator) validateLimits(dsl *MedicalQueryDSL) []QueryValidationError {
	var errors []QueryValidationError

	if dsl.Limit != nil {
		if *dsl.Limit <= 0 {
			errors = append(errors, QueryValidationError{
				Field:   "limit",
				Message: "Limit must be greater than 0",
				Code:    "INVALID_LIMIT",
			})
		}

		if *dsl.Limit > 10000 {
			errors = append(errors, QueryValidationError{
				Field:   "limit",
				Message: "Limit cannot exceed 10000",
				Code:    "LIMIT_TOO_LARGE",
			})
		}
	}

	if dsl.Offset != nil && *dsl.Offset < 0 {
		errors = append(errors, QueryValidationError{
			Field:   "offset",
			Message: "Offset cannot be negative",
			Code:    "INVALID_OFFSET",
		})
	}

	return errors
}

// validateTimeRange 验证时间范围
func (qv *QueryValidator) validateTimeRange(dsl *MedicalQueryDSL) []QueryValidationError {
	var errors []QueryValidationError

	if dsl.TimeRange != nil {
		if dsl.TimeRange.StartDate != nil && dsl.TimeRange.EndDate != nil {
			if dsl.TimeRange.StartDate.After(*dsl.TimeRange.EndDate) {
				errors = append(errors, QueryValidationError{
					Field:   "time_range",
					Message: "Start date cannot be after end date",
					Code:    "INVALID_TIME_RANGE",
				})
			}
		}

		// 验证时间范围不能太大（例如超过20年）
		if dsl.TimeRange.StartDate != nil && dsl.TimeRange.EndDate != nil {
			duration := dsl.TimeRange.EndDate.Sub(*dsl.TimeRange.StartDate)
			if duration > 20*365*24*time.Hour {
				errors = append(errors, QueryValidationError{
					Field:   "time_range",
					Message: "Time range cannot exceed 20 years",
					Code:    "TIME_RANGE_TOO_LARGE",
				})
			}
		}
	}

	return errors
}

// validateAggregations 验证聚合
func (qv *QueryValidator) validateAggregations(dsl *MedicalQueryDSL) []QueryValidationError {
	var errors []QueryValidationError

	for i, agg := range dsl.Aggregations {
		if agg.Function == "" {
			errors = append(errors, QueryValidationError{
				Field:   fmt.Sprintf("aggregations[%d].function", i),
				Message: "Aggregation function is required",
				Code:    "MISSING_AGGREGATION_FUNCTION",
			})
		}

		if !qv.isValidFunction(agg.Function) {
			errors = append(errors, QueryValidationError{
				Field:   fmt.Sprintf("aggregations[%d].function", i),
				Message: fmt.Sprintf("Aggregation function '%s' is not valid", agg.Function),
				Code:    "INVALID_AGGREGATION_FUNCTION",
			})
		}

		if agg.Field == "" {
			errors = append(errors, QueryValidationError{
				Field:   fmt.Sprintf("aggregations[%d].field", i),
				Message: "Aggregation field is required",
				Code:    "MISSING_AGGREGATION_FIELD",
			})
		}
	}

	return errors
}

// isValidFunction 检查函数是否有效
func (qv *QueryValidator) isValidFunction(function string) bool {
	validFunctions := map[string]bool{
		FuncCount:    true,
		FuncSum:      true,
		FuncAvg:      true,
		FuncMin:      true,
		FuncMax:      true,
		FuncStdDev:   true,
		FuncVariance: true,
	}
	return validFunctions[strings.ToUpper(function)]
}

// isValidOperator 检查操作符是否有效
func (qv *QueryValidator) isValidOperator(operator string) bool {
	validOperators := map[string]bool{
		OpEqual:              true,
		OpNotEqual:           true,
		OpGreaterThan:        true,
		OpGreaterThanOrEqual: true,
		OpLessThan:           true,
		OpLessThanOrEqual:    true,
		OpLike:               true,
		OpNotLike:            true,
		OpIn:                 true,
		OpNotIn:              true,
		OpIsNull:             true,
		OpIsNotNull:          true,
		OpBetween:            true,
		OpNotBetween:         true,
	}
	return validOperators[operator]
}

// SetMaxFields 设置最大字段数
func (qv *QueryValidator) SetMaxFields(maxFields int) {
	qv.maxFields = maxFields
}

// SetMaxFilters 设置最大过滤条件数
func (qv *QueryValidator) SetMaxFilters(maxFilters int) {
	qv.maxFilters = maxFilters
}

// SetMaxJoins 设置最大JOIN数
func (qv *QueryValidator) SetMaxJoins(maxJoins int) {
	qv.maxJoins = maxJoins
}
