package api

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"github.com/chungzy/medical-data-platform/internal/service"
	"github.com/chungzy/medical-data-platform/pkg/logger"
)

type DictionaryController struct {
	dictionaryService *service.DictionaryService
}

func NewDictionaryController(dictionaryService *service.DictionaryService) *DictionaryController {
	return &DictionaryController{
		dictionaryService: dictionaryService,
	}
}

// SearchFields 搜索数据字段
func (dc *DictionaryController) SearchFields(c *gin.Context) {
	c.Set("business_operation", "search_fields")

	logger.InfoWithGinContext(c, "Searching data fields")

	query := c.Query("q")
	dataset := c.Query("dataset")
	category := c.Query("category")

	limitStr := c.Default<PERSON>uery("limit", "50")
	offsetStr := c.<PERSON><PERSON><PERSON>("offset", "0")

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 1000 {
		limit = 50
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		offset = 0
	}

	logger.InfoWithGinContext(c, "Search fields parameters parsed", map[string]interface{}{
		"query":    query,
		"dataset":  dataset,
		"category": category,
		"limit":    limit,
		"offset":   offset,
	})

	response, err := dc.dictionaryService.SearchFields(c.Request.Context(), query, dataset, category, limit, offset)
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to search fields", map[string]interface{}{
			"query":    query,
			"dataset":  dataset,
			"category": category,
			"limit":    limit,
			"offset":   offset,
		})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to search fields", "details": err.Error()})
		return
	}

	logger.InfoWithGinContext(c, "Fields search completed successfully", map[string]interface{}{
		"query":         query,
		"dataset":       dataset,
		"results_count": len(response.Fields),
	})

	c.JSON(http.StatusOK, response)
}

// GetCategories 获取字段分类
func (dc *DictionaryController) GetCategories(c *gin.Context) {
	c.Set("business_operation", "get_categories")

	logger.InfoWithGinContext(c, "Getting field categories")

	dataset := c.Query("dataset")
	if dataset == "" {
		logger.WarnWithGinContext(c, "Dataset parameter is missing")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Dataset parameter is required"})
		return
	}

	logger.InfoWithGinContext(c, "Get categories request parsed", map[string]interface{}{
		"dataset": dataset,
	})

	response, err := dc.dictionaryService.GetCategories(c.Request.Context(), dataset)
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Failed to get categories", map[string]interface{}{
			"dataset": dataset,
		})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get categories", "details": err.Error()})
		return
	}

	logger.InfoWithGinContext(c, "Categories retrieved successfully", map[string]interface{}{
		"dataset":          dataset,
		"categories_count": len(response.Categories),
	})

	c.JSON(http.StatusOK, response)
}

// GetFieldByID 根据ID获取字段详情
func (dc *DictionaryController) GetFieldByID(c *gin.Context) {
	c.Set("business_operation", "get_field_by_id")

	logger.InfoWithGinContext(c, "Getting field by ID")

	id := c.Param("id")
	if id == "" {
		logger.WarnWithGinContext(c, "Field ID parameter is missing")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Field ID is required"})
		return
	}

	logger.InfoWithGinContext(c, "Get field by ID request parsed", map[string]interface{}{
		"field_id": id,
	})

	field, err := dc.dictionaryService.GetFieldByID(c.Request.Context(), id)
	if err != nil {
		logger.ErrorWithGinContext(c, err, "Field not found", map[string]interface{}{
			"field_id": id,
		})
		c.JSON(http.StatusNotFound, gin.H{"error": "Field not found", "details": err.Error()})
		return
	}

	logger.InfoWithGinContext(c, "Field retrieved successfully", map[string]interface{}{
		"field_id":   id,
		"field_name": field.Name,
	})

	c.JSON(http.StatusOK, field)
}
