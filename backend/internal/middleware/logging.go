package middleware

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"

	"github.com/chungzy/medical-data-platform/pkg/logger"
)

// LoggingMiddleware 结构化日志中间件
func LoggingMiddleware() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		// 获取请求的LogID
		logID := param.Request.Header.Get("X-Request-ID")
		if logID == "" {
			logID = "unknown"
		}

		// 使用专门的Gin日志记录器
		logger.GinLogger.WithFields(logrus.Fields{
			"log_id":     logID,
			"timestamp":  param.TimeStamp.Format("2006-01-02 15:04:05"),
			"status":     param.StatusCode,
			"latency":    param.Latency.String(),
			"client_ip":  param.ClientIP,
			"method":     param.Method,
			"path":       param.Path,
			"user_agent": param.Request.UserAgent(),
			"error":      param.ErrorMessage,
			"body_size":  param.BodySize,
		}).Info("HTTP Request")

		// 返回空字符串，因为我们已经通过logrus记录了日志
		return ""
	})
}

// LogIDMiddleware 为每个请求添加唯一LogID和业务上下文
func LogIDMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 生成或获取LogID
		logID := c.GetHeader("X-Request-ID")
		if logID == "" {
			logID = generateLogID()
		}

		// 设置LogID到请求头和上下文
		c.Set("log_id", logID)
		c.Header("X-Request-ID", logID)

		// 创建带LogID的上下文
		ctx := context.WithValue(c.Request.Context(), "log_id", logID)
		c.Request = c.Request.WithContext(ctx)

		// 记录请求开始
		startTime := time.Now()

		// 提取用户信息（如果有的话）
		userID := c.GetString("user_id")
		if userID == "" {
			userID = "anonymous"
		}

		// 记录请求开始日志
		logger.WithBusinessContext(logID, userID, "http_request").
			WithFields(logrus.Fields{
				"method":    c.Request.Method,
				"path":      c.Request.URL.Path,
				"client_ip": c.ClientIP(),
			}).Info("Request started")

		// 执行后续中间件
		c.Next()

		// 记录请求完成日志
		duration := time.Since(startTime)
		status := c.Writer.Status()

		logEntry := logger.WithBusinessContext(logID, userID, "http_request").
			WithFields(logrus.Fields{
				"method":    c.Request.Method,
				"path":      c.Request.URL.Path,
				"status":    status,
				"duration":  duration.String(),
				"client_ip": c.ClientIP(),
				"body_size": c.Writer.Size(),
			})

		// 根据状态码选择日志级别
		if status >= 500 {
			logEntry.Error("Request completed with server error")
		} else if status >= 400 {
			logEntry.Warn("Request completed with client error")
		} else {
			logEntry.Info("Request completed successfully")
		}
	}
}

// 兼容性：保持原有的RequestIDMiddleware名称
func RequestIDMiddleware() gin.HandlerFunc {
	return LogIDMiddleware()
}

// generateLogID 生成LogID（支持分布式追踪）
func generateLogID() string {
	// 使用更安全的随机数生成器
	bytes := make([]byte, 8)
	if _, err := rand.Read(bytes); err != nil {
		// 如果随机数生成失败，使用时间戳作为后备
		return time.Now().Format("20060102150405") + "-" + randomString(8)
	}

	// 生成16字符的十六进制字符串
	return time.Now().Format("20060102150405") + "-" + hex.EncodeToString(bytes)
}

// randomString 生成随机字符串（后备方案）
func randomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(b)
}

// BusinessLogMiddleware 业务操作日志中间件
func BusinessLogMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 提取业务信息
		logID := c.GetString("log_id")
		userID := c.GetString("user_id")

		// 根据路径推断业务操作类型
		operation := inferBusinessOperation(c.Request.Method, c.Request.URL.Path)

		// 设置业务上下文到gin context
		c.Set("business_operation", operation)

		// 记录业务操作开始
		if operation != "unknown" {
			logger.WithBusinessContext(logID, userID, operation).
				WithField("endpoint", c.Request.URL.Path).
				Info("Business operation started")
		}

		c.Next()

		// 记录业务操作结果
		if operation != "unknown" {
			status := c.Writer.Status()
			if status >= 400 {
				logger.WithBusinessContext(logID, userID, operation).
					WithFields(logrus.Fields{
						"endpoint": c.Request.URL.Path,
						"status":   status,
					}).Error("Business operation failed")
			} else {
				logger.WithBusinessContext(logID, userID, operation).
					WithFields(logrus.Fields{
						"endpoint": c.Request.URL.Path,
						"status":   status,
					}).Info("Business operation completed")
			}
		}
	}
}

// inferBusinessOperation 根据请求路径推断业务操作类型
func inferBusinessOperation(method, path string) string {
	switch {
	case method == "POST" && contains(path, "/auth/login"):
		return "user_login"
	case method == "POST" && contains(path, "/auth/register"):
		return "user_register"
	case method == "POST" && contains(path, "/queries/execute"):
		return "query_execute"
	case method == "GET" && contains(path, "/datasets"):
		return "dataset_query"
	case method == "POST" && contains(path, "/export"):
		return "data_export"
	case method == "GET" && contains(path, "/export") && contains(path, "/download"):
		return "data_download"
	case method == "POST" && contains(path, "/templates"):
		return "template_create"
	case method == "PUT" && contains(path, "/templates"):
		return "template_update"
	case method == "DELETE" && contains(path, "/templates"):
		return "template_delete"
	case method == "GET" && contains(path, "/dictionary"):
		return "dictionary_query"
	case method == "POST" && contains(path, "/analytics"):
		return "analytics_query"
	default:
		return "unknown"
	}
}

// contains 辅助函数：检查字符串是否包含子串
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || (len(s) > len(substr) &&
		(s[:len(substr)] == substr || s[len(s)-len(substr):] == substr ||
			indexOfSubstring(s, substr) >= 0)))
}

// indexOfSubstring 查找子串位置
func indexOfSubstring(s, substr string) int {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return i
		}
	}
	return -1
}
