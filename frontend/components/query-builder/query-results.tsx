"use client"

import { useState, use<PERSON>emo } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { 
  Download, 
  Save, 
  Share, 
  BarChart3, 
  Table, 
  Search,
  Filter,
  SortAsc,
  SortDesc,
  Eye,
  FileText,
  Database,
  Clock,
  CheckCircle2
} from "lucide-react"
import { toast } from "sonner"
import { formatNumber } from "@/lib/utils"

interface QueryResult {
  query_id: string
  status: string
  total: number
  executionTime: number
  data: any[]
  metadata?: {
    fields: any[]
    filters: any[]
    dataset: string
  }
}

interface QueryResultsProps {
  results: QueryResult | null
  onExport: (format: string) => void
  onSaveQuery: () => void
}

export function QueryResults({ results, onExport, onSaveQuery }: QueryResultsProps) {
  const [activeTab, setActiveTab] = useState("table")
  const [searchTerm, setSearchTerm] = useState("")
  const [sortField, setSortField] = useState("")
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc")
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)

  // 处理数据搜索和排序
  const processedData = useMemo(() => {
    if (!results?.data) return []

    let filtered = results.data

    // 搜索过滤
    if (searchTerm) {
      filtered = filtered.filter(row => 
        Object.values(row).some(value => 
          String(value).toLowerCase().includes(searchTerm.toLowerCase())
        )
      )
    }

    // 排序
    if (sortField) {
      filtered = [...filtered].sort((a, b) => {
        const aVal = a[sortField]
        const bVal = b[sortField]
        
        if (aVal === bVal) return 0
        
        const comparison = aVal < bVal ? -1 : 1
        return sortDirection === "asc" ? comparison : -comparison
      })
    }

    return filtered
  }, [results?.data, searchTerm, sortField, sortDirection])

  // 分页数据
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize
    return processedData.slice(startIndex, startIndex + pageSize)
  }, [processedData, currentPage, pageSize])

  // 获取列名
  const columns = useMemo(() => {
    if (!results?.data || results.data.length === 0) return []
    return Object.keys(results.data[0])
  }, [results?.data])

  // 总页数
  const totalPages = Math.ceil(processedData.length / pageSize)

  // 处理排序
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortDirection("asc")
    }
  }

  // 导出数据
  const handleExport = (format: string) => {
    if (!results?.data) {
      toast.error("没有可导出的数据")
      return
    }

    let content = ""
    let filename = `query_results_${results.query_id}`

    switch (format) {
      case "csv":
        const headers = columns.join(",")
        const rows = processedData.map(row => 
          columns.map(col => `"${String(row[col] || "")}"`).join(",")
        ).join("\n")
        content = `${headers}\n${rows}`
        filename += ".csv"
        break

      case "json":
        content = JSON.stringify(processedData, null, 2)
        filename += ".json"
        break

      case "txt":
        content = processedData.map(row => 
          columns.map(col => `${col}: ${row[col]}`).join(" | ")
        ).join("\n")
        filename += ".txt"
        break

      default:
        toast.error("不支持的导出格式")
        return
    }

    const blob = new Blob([content], { type: "text/plain" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = filename
    a.click()
    URL.revokeObjectURL(url)

    toast.success(`数据已导出为 ${format.toUpperCase()} 格式`)
    onExport(format)
  }

  // 生成统计信息
  const generateStats = () => {
    if (!results?.data) return null

    const stats: any = {}
    
    columns.forEach(col => {
      const values = results.data.map(row => row[col]).filter(val => val != null)
      const uniqueValues = new Set(values)
      
      stats[col] = {
        total: values.length,
        unique: uniqueValues.size,
        nullCount: results.data.length - values.length,
        type: typeof values[0]
      }

      // 数值统计
      if (stats[col].type === "number") {
        const numbers = values.filter(val => typeof val === "number")
        if (numbers.length > 0) {
          stats[col].min = Math.min(...numbers)
          stats[col].max = Math.max(...numbers)
          stats[col].avg = numbers.reduce((a, b) => a + b, 0) / numbers.length
        }
      }
    })

    return stats
  }

  const stats = generateStats()

  if (!results) {
    return (
      <div className="text-center py-12">
        <Database className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">暂无查询结果</h3>
        <p className="text-gray-600">
          执行查询后，结果将在这里显示
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900">查询结果</h2>
        <p className="text-gray-600 mt-1">
          查看、分析和导出您的查询结果
        </p>
      </div>

      {/* 结果统计 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CheckCircle2 className="h-4 w-4 text-green-600" />
              <div>
                <div className="text-sm font-medium">查询状态</div>
                <div className="text-lg font-bold capitalize">{results.status}</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Table className="h-4 w-4 text-blue-600" />
              <div>
                <div className="text-sm font-medium">结果数量</div>
                <div className="text-lg font-bold">{formatNumber(results.total)}</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-purple-600" />
              <div>
                <div className="text-sm font-medium">执行时间</div>
                <div className="text-lg font-bold">{results.executionTime}ms</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Database className="h-4 w-4 text-orange-600" />
              <div>
                <div className="text-sm font-medium">字段数量</div>
                <div className="text-lg font-bold">{columns.length}</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 操作工具栏 */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex items-center gap-4">
          {/* 搜索 */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="搜索结果..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-64"
            />
          </div>

          {/* 页面大小选择 */}
          <Select value={String(pageSize)} onValueChange={(value) => setPageSize(Number(value))}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">10 条/页</SelectItem>
              <SelectItem value="25">25 条/页</SelectItem>
              <SelectItem value="50">50 条/页</SelectItem>
              <SelectItem value="100">100 条/页</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={() => handleExport("csv")}>
            <Download className="h-4 w-4 mr-1" />
            CSV
          </Button>
          <Button variant="outline" size="sm" onClick={() => handleExport("json")}>
            <Download className="h-4 w-4 mr-1" />
            JSON
          </Button>
          <Button variant="outline" size="sm" onClick={onSaveQuery}>
            <Save className="h-4 w-4 mr-1" />
            保存查询
          </Button>
        </div>
      </div>

      {/* 结果内容 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>数据结果</CardTitle>
            <Badge variant="outline">
              显示 {Math.min((currentPage - 1) * pageSize + 1, processedData.length)}-{Math.min(currentPage * pageSize, processedData.length)} / {processedData.length} 条
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList>
              <TabsTrigger value="table">数据表格</TabsTrigger>
              <TabsTrigger value="stats">统计信息</TabsTrigger>
              <TabsTrigger value="raw">原始数据</TabsTrigger>
            </TabsList>

            <TabsContent value="table" className="mt-4">
              <div className="border rounded-lg overflow-hidden">
                <ScrollArea className="h-96">
                  <table className="w-full">
                    <thead className="bg-gray-50 sticky top-0">
                      <tr>
                        {columns.map((col) => (
                          <th 
                            key={col}
                            className="px-4 py-2 text-left text-sm font-medium text-gray-700 cursor-pointer hover:bg-gray-100"
                            onClick={() => handleSort(col)}
                          >
                            <div className="flex items-center gap-1">
                              {col}
                              {sortField === col && (
                                sortDirection === "asc" ? 
                                <SortAsc className="h-3 w-3" /> : 
                                <SortDesc className="h-3 w-3" />
                              )}
                            </div>
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody>
                      {paginatedData.map((row, index) => (
                        <tr key={index} className="border-t hover:bg-gray-50">
                          {columns.map((col) => (
                            <td key={col} className="px-4 py-2 text-sm">
                              {row[col] != null ? String(row[col]) : "-"}
                            </td>
                          ))}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </ScrollArea>
              </div>

              {/* 分页控件 */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between mt-4">
                  <div className="text-sm text-gray-600">
                    共 {processedData.length} 条记录，{totalPages} 页
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                    >
                      上一页
                    </Button>
                    <span className="text-sm">
                      {currentPage} / {totalPages}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      disabled={currentPage === totalPages}
                    >
                      下一页
                    </Button>
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="stats" className="mt-4">
              {stats && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {Object.entries(stats).map(([field, fieldStats]: [string, any]) => (
                    <Card key={field}>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm">{field}</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-2">
                        <div className="grid grid-cols-2 gap-2 text-xs">
                          <div>总数: {fieldStats.total}</div>
                          <div>唯一值: {fieldStats.unique}</div>
                          <div>空值: {fieldStats.nullCount}</div>
                          <div>类型: {fieldStats.type}</div>
                        </div>
                        {fieldStats.type === "number" && fieldStats.min !== undefined && (
                          <div className="grid grid-cols-2 gap-2 text-xs pt-2 border-t">
                            <div>最小值: {fieldStats.min}</div>
                            <div>最大值: {fieldStats.max}</div>
                            <div className="col-span-2">平均值: {fieldStats.avg.toFixed(2)}</div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="raw" className="mt-4">
              <ScrollArea className="h-96">
                <pre className="text-xs bg-gray-100 p-4 rounded-lg overflow-auto">
                  {JSON.stringify(results, null, 2)}
                </pre>
              </ScrollArea>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
