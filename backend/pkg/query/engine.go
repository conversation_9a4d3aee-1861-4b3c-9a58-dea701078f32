package query

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/chungzy/medical-data-platform/pkg/database"
	"github.com/chungzy/medical-data-platform/pkg/logger"
	"github.com/google/uuid"
)

// QueryEngine 查询执行引擎
type QueryEngine struct {
	dsm          *database.DataSourceManager
	validator    *QueryValidator
	cache        QueryCache
	maxRows      int
	queryTimeout time.Duration
}

// QueryResult 查询结果
type QueryResult struct {
	QueryID       string                   `json:"query_id"`
	StudyName     string                   `json:"study_name"`
	Data          []map[string]interface{} `json:"data"`
	Columns       []ColumnInfo             `json:"columns"`
	Total         int                      `json:"total"`
	ExecutionTime time.Duration            `json:"execution_time"`
	Status        string                   `json:"status"`
	Message       string                   `json:"message,omitempty"`
	SQL           string                   `json:"sql,omitempty"`
	Metadata      QueryMetadata            `json:"metadata"`
}

// ColumnInfo 列信息
type ColumnInfo struct {
	Name        string `json:"name"`
	Type        string `json:"type"`
	Description string `json:"description,omitempty"`
	Unit        string `json:"unit,omitempty"`
}

// QueryMetadata 查询元数据
type QueryMetadata struct {
	DatasetID    string             `json:"dataset_id"`
	TablesUsed   []string           `json:"tables_used"`
	FieldsUsed   []string           `json:"fields_used"`
	RowsScanned  int64              `json:"rows_scanned"`
	RowsReturned int                `json:"rows_returned"`
	CacheHit     bool               `json:"cache_hit"`
	QueryPlan    string             `json:"query_plan,omitempty"`
	Warnings     []string           `json:"warnings,omitempty"`
	Performance  PerformanceMetrics `json:"performance"`
}

// PerformanceMetrics 性能指标
type PerformanceMetrics struct {
	ParseTime     time.Duration `json:"parse_time"`
	PlanTime      time.Duration `json:"plan_time"`
	ExecutionTime time.Duration `json:"execution_time"`
	FetchTime     time.Duration `json:"fetch_time"`
	TotalTime     time.Duration `json:"total_time"`
}

// QueryCache 查询缓存接口
type QueryCache interface {
	Get(key string) (*QueryResult, bool)
	Set(key string, result *QueryResult, ttl time.Duration)
	Delete(key string)
	Clear()
}

// NewQueryEngine 创建查询引擎
func NewQueryEngine(dsm *database.DataSourceManager, cache QueryCache) *QueryEngine {
	return &QueryEngine{
		dsm:          dsm,
		validator:    NewQueryValidator(),
		cache:        cache,
		maxRows:      10000,
		queryTimeout: 5 * time.Minute,
	}
}

// SetMaxRows 设置最大行数
func (qe *QueryEngine) SetMaxRows(maxRows int) {
	qe.maxRows = maxRows
}

// SetQueryTimeout 设置查询超时
func (qe *QueryEngine) SetQueryTimeout(timeout time.Duration) {
	qe.queryTimeout = timeout
}

// ExecuteQuery 执行查询
func (qe *QueryEngine) ExecuteQuery(ctx context.Context, dsl *MedicalQueryDSL) (*QueryResult, error) {
	startTime := time.Now()

	// 生成查询ID
	if dsl.QueryID == "" {
		dsl.QueryID = uuid.New().String()
	}

	logger.Info("Starting query execution", map[string]interface{}{
		"query_id":   dsl.QueryID,
		"study_name": dsl.StudyName,
		"dataset_id": dsl.DatasetID,
	})

	// 验证查询
	parseStart := time.Now()
	validationResult := qe.validator.ValidateQuery(dsl)
	if !validationResult.Valid {
		return &QueryResult{
			QueryID:       dsl.QueryID,
			StudyName:     dsl.StudyName,
			Status:        "error",
			Message:       "Query validation failed",
			ExecutionTime: time.Since(startTime),
			Metadata: QueryMetadata{
				DatasetID: dsl.DatasetID,
				Performance: PerformanceMetrics{
					ParseTime: time.Since(parseStart),
					TotalTime: time.Since(startTime),
				},
			},
		}, fmt.Errorf("query validation failed: %v", validationResult.Errors)
	}

	// 检查缓存
	cacheKey := qe.generateCacheKey(dsl)
	if qe.cache != nil {
		if cachedResult, found := qe.cache.Get(cacheKey); found {
			logger.Info("Query result found in cache", map[string]interface{}{
				"query_id":  dsl.QueryID,
				"cache_key": cacheKey,
			})
			cachedResult.Metadata.CacheHit = true
			return cachedResult, nil
		}
	}

	// 构建SQL
	planStart := time.Now()
	sqlBuilder := NewSQLBuilder(dsl.DatasetID)
	sql, args, err := sqlBuilder.BuildSQL(dsl)
	if err != nil {
		return &QueryResult{
			QueryID:       dsl.QueryID,
			StudyName:     dsl.StudyName,
			Status:        "error",
			Message:       "SQL generation failed",
			ExecutionTime: time.Since(startTime),
			Metadata: QueryMetadata{
				DatasetID: dsl.DatasetID,
				Performance: PerformanceMetrics{
					ParseTime: time.Since(parseStart),
					PlanTime:  time.Since(planStart),
					TotalTime: time.Since(startTime),
				},
			},
		}, fmt.Errorf("failed to build SQL: %w", err)
	}

	// 执行查询
	execStart := time.Now()
	result, err := qe.executeSQL(ctx, dsl, sql, args)
	if err != nil {
		return &QueryResult{
			QueryID:       dsl.QueryID,
			StudyName:     dsl.StudyName,
			Status:        "error",
			Message:       err.Error(),
			SQL:           sql,
			ExecutionTime: time.Since(startTime),
			Metadata: QueryMetadata{
				DatasetID: dsl.DatasetID,
				Performance: PerformanceMetrics{
					ParseTime:     time.Since(parseStart),
					PlanTime:      time.Since(planStart),
					ExecutionTime: time.Since(execStart),
					TotalTime:     time.Since(startTime),
				},
			},
		}, err
	}

	// 设置性能指标
	result.Metadata.Performance = PerformanceMetrics{
		ParseTime:     time.Since(parseStart),
		PlanTime:      planStart.Sub(parseStart),
		ExecutionTime: execStart.Sub(planStart),
		FetchTime:     time.Since(execStart),
		TotalTime:     time.Since(startTime),
	}

	// 缓存结果
	if qe.cache != nil && result.Status == "success" {
		qe.cache.Set(cacheKey, result, 1*time.Hour)
	}

	logger.Info("Query execution completed", map[string]interface{}{
		"query_id":       dsl.QueryID,
		"execution_time": result.ExecutionTime,
		"rows_returned":  result.Total,
		"status":         result.Status,
	})

	return result, nil
}

// executeSQL 执行SQL查询
func (qe *QueryEngine) executeSQL(ctx context.Context, dsl *MedicalQueryDSL, sql string, args []interface{}) (*QueryResult, error) {
	// 获取数据库连接
	db, err := qe.dsm.GetConnection(dsl.DatasetID)
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %w", err)
	}

	// 设置查询超时
	queryCtx, cancel := context.WithTimeout(ctx, qe.queryTimeout)
	defer cancel()

	// 执行查询
	rows, err := db.QueryContext(queryCtx, sql, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to execute query: %w", err)
	}
	defer rows.Close()

	// 获取列信息
	columns, err := rows.Columns()
	if err != nil {
		return nil, fmt.Errorf("failed to get columns: %w", err)
	}

	columnTypes, err := rows.ColumnTypes()
	if err != nil {
		return nil, fmt.Errorf("failed to get column types: %w", err)
	}

	// 构建列信息
	columnInfos := make([]ColumnInfo, len(columns))
	for i, col := range columns {
		columnInfos[i] = ColumnInfo{
			Name: col,
			Type: columnTypes[i].DatabaseTypeName(),
		}
	}

	// 读取数据
	var data []map[string]interface{}
	rowCount := 0

	for rows.Next() {
		if rowCount >= qe.maxRows {
			logger.Warn("Query result truncated due to max rows limit", map[string]interface{}{
				"query_id": dsl.QueryID,
				"max_rows": qe.maxRows,
			})
			break
		}

		// 创建扫描目标
		values := make([]interface{}, len(columns))
		valuePtrs := make([]interface{}, len(columns))
		for i := range values {
			valuePtrs[i] = &values[i]
		}

		// 扫描行
		if err := rows.Scan(valuePtrs...); err != nil {
			return nil, fmt.Errorf("failed to scan row: %w", err)
		}

		// 构建行数据
		row := make(map[string]interface{})
		for i, col := range columns {
			row[col] = qe.convertValue(values[i])
		}

		data = append(data, row)
		rowCount++
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error reading rows: %w", err)
	}

	// 构建结果
	result := &QueryResult{
		QueryID:       dsl.QueryID,
		StudyName:     dsl.StudyName,
		Data:          data,
		Columns:       columnInfos,
		Total:         len(data),
		Status:        "success",
		SQL:           sql,
		ExecutionTime: 0, // 将在调用方设置
		Metadata: QueryMetadata{
			DatasetID:    dsl.DatasetID,
			TablesUsed:   qe.extractTablesUsed(dsl),
			FieldsUsed:   qe.extractFieldsUsed(dsl),
			RowsReturned: len(data),
			CacheHit:     false,
		},
	}

	// 添加警告
	if rowCount >= qe.maxRows {
		result.Metadata.Warnings = append(result.Metadata.Warnings,
			fmt.Sprintf("Result truncated to %d rows", qe.maxRows))
	}

	return result, nil
}

// convertValue 转换数据库值
func (qe *QueryEngine) convertValue(value interface{}) interface{} {
	if value == nil {
		return nil
	}

	switch v := value.(type) {
	case []byte:
		return string(v)
	case time.Time:
		return v.Format(time.RFC3339)
	default:
		return v
	}
}

// extractTablesUsed 提取使用的表
func (qe *QueryEngine) extractTablesUsed(dsl *MedicalQueryDSL) []string {
	tables := make(map[string]bool)

	// 主表
	tables[dsl.Cohort.PrimaryTable] = true

	// 字段表
	for _, field := range dsl.Fields {
		if field.Table != "" {
			tables[field.Table] = true
		}
	}

	// 过滤条件表
	for _, filter := range dsl.Filters {
		if filter.Table != "" {
			tables[filter.Table] = true
		}
	}

	var result []string
	for table := range tables {
		result = append(result, table)
	}
	return result
}

// extractFieldsUsed 提取使用的字段
func (qe *QueryEngine) extractFieldsUsed(dsl *MedicalQueryDSL) []string {
	fields := make(map[string]bool)

	// 选择字段
	for _, field := range dsl.Fields {
		fieldKey := field.Table + "." + field.Field
		fields[fieldKey] = true
	}

	// 过滤字段
	for _, filter := range dsl.Filters {
		fieldKey := filter.Table + "." + filter.Field
		fields[fieldKey] = true
	}

	var result []string
	for field := range fields {
		result = append(result, field)
	}
	return result
}

// generateCacheKey 生成缓存键
func (qe *QueryEngine) generateCacheKey(dsl *MedicalQueryDSL) string {
	// 序列化DSL为JSON并计算哈希
	data, _ := json.Marshal(dsl)
	return fmt.Sprintf("query:%x", data)
}

// GetQueryStatus 获取查询状态
func (qe *QueryEngine) GetQueryStatus(queryID string) (string, error) {
	// 这里可以实现查询状态跟踪
	// 目前返回简单状态
	return "completed", nil
}

// CancelQuery 取消查询
func (qe *QueryEngine) CancelQuery(queryID string) error {
	// 这里可以实现查询取消逻辑
	logger.Info("Query cancellation requested", map[string]interface{}{
		"query_id": queryID,
	})
	return nil
}

// GetDataSourceStatus 获取数据源状态
func (qe *QueryEngine) GetDataSourceStatus(datasetID string) (*database.DataSourceStatus, bool) {
	// 这里应该从数据源管理器获取状态
	// 目前返回模拟状态
	status := &database.DataSourceStatus{
		ID:          datasetID,
		Name:        datasetID,
		IsHealthy:   true,
		Description: fmt.Sprintf("Dataset %s", datasetID),
	}
	return status, true
}

// GetCacheStats 获取缓存统计
func (qe *QueryEngine) GetCacheStats() CacheStats {
	if qe.cache != nil {
		if memCache, ok := qe.cache.(*MemoryCache); ok {
			return memCache.GetStats()
		}
	}
	return CacheStats{}
}

// ClearCache 清除缓存
func (qe *QueryEngine) ClearCache() {
	if qe.cache != nil {
		qe.cache.Clear()
	}
}
