package model

import (
	"encoding/json"
	"time"

	"github.com/google/uuid"
)

type QueryTemplate struct {
	ID             uuid.UUID       `json:"id" db:"id"`
	UserID         uuid.UUID       `json:"user_id" db:"user_id"`
	Name           string          `json:"name" db:"name" validate:"required,min=1,max=200"`
	Description    string          `json:"description" db:"description"`
	Category       string          `json:"category" db:"category"`
	DatasetID      string          `json:"dataset" db:"dataset_id"`
	TemplateConfig json.RawMessage `json:"template_config" db:"template_config"`
	IsPublic       bool            `json:"is_public" db:"is_public"`
	UsageCount     int             `json:"usage" db:"usage_count"`
	Rating         float64         `json:"rating" db:"rating"`
	Author         string          `json:"author" db:"author"`
	CreatedAt      time.Time       `json:"created_at" db:"created_at"`
	UpdatedAt      time.Time       `json:"updated_at" db:"updated_at"`
}

type CreateTemplateRequest struct {
	Name           string          `json:"name" validate:"required,min=1,max=200"`
	Description    string          `json:"description"`
	Category       string          `json:"category"`
	DatasetID      string          `json:"dataset_id" validate:"required"`
	TemplateConfig json.RawMessage `json:"template_config" validate:"required"`
	IsPublic       bool            `json:"is_public"`
	Author         string          `json:"author"`
}

type UpdateTemplateRequest struct {
	Name           string          `json:"name" validate:"required,min=1,max=200"`
	Description    string          `json:"description"`
	Category       string          `json:"category"`
	TemplateConfig json.RawMessage `json:"template_config" validate:"required"`
	IsPublic       bool            `json:"is_public"`
}

type UseTemplateRequest struct {
	Parameters map[string]interface{} `json:"parameters,omitempty"`
}

type TemplateListResponse struct {
	Templates  []QueryTemplateResponse `json:"templates"`
	Pagination PaginationInfo          `json:"pagination,omitempty"`
}

type QueryTemplateResponse struct {
	ID          uuid.UUID `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Category    string    `json:"category"`
	Dataset     string    `json:"dataset"`
	Fields      []string  `json:"fields,omitempty"`
	Author      string    `json:"author"`
	Usage       int       `json:"usage"`
	Rating      float64   `json:"rating"`
	IsPublic    bool      `json:"is_public"`
	CreatedAt   time.Time `json:"created_at"`
}

type UseTemplateResponse struct {
	QueryConfig QueryExecuteRequest `json:"query_config"`
}

type SaveAsTemplateRequest struct {
	Name        string `json:"name" validate:"required,min=1,max=200"`
	Description string `json:"description"`
	IsPublic    bool   `json:"is_public"`
}

// 导出相关模型
type ExportRequest struct {
	QueryID uuid.UUID `json:"query_id" validate:"required"`
	Format  string    `json:"format" validate:"required,oneof=csv excel json"`
	Fields  []string  `json:"fields,omitempty"`
}

type ExportResponse struct {
	DownloadURL string    `json:"download_url"`
	ExpiresAt   time.Time `json:"expires_at"`
}

// 统计分析相关模型
type BasicStatsRequest struct {
	QueryID uuid.UUID `json:"query_id" validate:"required"`
	Fields  []string  `json:"fields" validate:"required,min=1"`
}

type BasicStatsResponse struct {
	Stats map[string]FieldStats `json:"stats"`
}

type FieldStats struct {
	Count   int      `json:"count"`
	Mean    *float64 `json:"mean,omitempty"`
	Median  *float64 `json:"median,omitempty"`
	Std     *float64 `json:"std,omitempty"`
	Min     *float64 `json:"min,omitempty"`
	Max     *float64 `json:"max,omitempty"`
	Missing int      `json:"missing"`
}

type VisualizationRequest struct {
	QueryID   uuid.UUID `json:"query_id" validate:"required"`
	ChartType string    `json:"chart_type" validate:"required,oneof=bar line pie scatter"`
	XField    string    `json:"x_field" validate:"required"`
	YField    *string   `json:"y_field,omitempty"`
	GroupBy   *string   `json:"group_by,omitempty"`
}

type VisualizationResponse struct {
	ChartData []map[string]interface{} `json:"chart_data"`
	Config    ChartConfig              `json:"config"`
}

type ChartConfig struct {
	Type   string `json:"type"`
	XAxis  string `json:"x_axis"`
	YAxis  string `json:"y_axis,omitempty"`
	Title  string `json:"title,omitempty"`
	Legend bool   `json:"legend,omitempty"`
}
