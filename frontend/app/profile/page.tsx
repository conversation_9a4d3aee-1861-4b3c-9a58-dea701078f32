"use client"

import { useAuth } from "@/lib/hooks/use-auth"
import { usePermissions } from "@/lib/hooks/use-permissions"
import { DashboardLayout } from "@/components/dashboard-layout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { User, Mail, Phone, Shield, Calendar } from "lucide-react"

export default function ProfilePage() {
  const { user } = useAuth()
  const { permissions } = usePermissions()

  if (!user) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <p className="text-gray-500">请先登录</p>
        </div>
      </DashboardLayout>
    )
  }

  const getUserRole = () => {
    switch (user.role) {
      case "admin":
        return "管理员"
      case "user":
        return "普通用户"
      default:
        return user.role
    }
  }

  const getPermissionLevel = () => {
    if (!permissions) return "未知"
    switch (permissions.permission) {
      case 1:
        return "管理员权限"
      case 2:
        return "普通用户权限"
      case 0:
        return "无权限"
      default:
        return "未知权限"
    }
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">个人资料</h1>
          <p className="text-gray-600">查看和管理您的账户信息</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 基本信息 */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <User className="h-5 w-5" />
                <span>基本信息</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center space-x-4">
                <Avatar className="h-20 w-20">
                  <AvatarImage src="/placeholder.svg?height=80&width=80" />
                  <AvatarFallback className="bg-blue-100 text-blue-700 text-xl font-semibold">
                    {user.name?.charAt(0).toUpperCase() || "U"}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <h2 className="text-2xl font-semibold text-gray-900">{user.name}</h2>
                  <Badge variant="secondary" className="mt-1">
                    {getUserRole()}
                  </Badge>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center space-x-3">
                  <Mail className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-500">邮箱</p>
                    <p className="text-gray-900">{user.email}</p>
                  </div>
                </div>

                {user.phone && (
                  <div className="flex items-center space-x-3">
                    <Phone className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-500">手机号</p>
                      <p className="text-gray-900">{user.phone}</p>
                    </div>
                  </div>
                )}

                <div className="flex items-center space-x-3">
                  <User className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-500">用户ID</p>
                    <p className="text-gray-900 font-mono text-sm">{user.id}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Calendar className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-500">注册时间</p>
                    <p className="text-gray-900">2024年1月</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 权限信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Shield className="h-5 w-5" />
                <span>权限信息</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm font-medium text-gray-500">权限级别</p>
                <p className="text-gray-900">{getPermissionLevel()}</p>
              </div>

              {permissions && (
                <div>
                  <p className="text-sm font-medium text-gray-500 mb-2">可用权限</p>
                  <div className="space-y-1">
                    {permissions.permissions.slice(0, 5).map((permission, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {permission}
                      </Badge>
                    ))}
                    {permissions.permissions.length > 5 && (
                      <Badge variant="outline" className="text-xs">
                        +{permissions.permissions.length - 5} 更多
                      </Badge>
                    )}
                  </div>
                </div>
              )}

              {process.env.NEXT_PUBLIC_DEV_MODE === 'true' && (
                <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <p className="text-sm text-yellow-800 font-medium">开发模式</p>
                  <p className="text-xs text-yellow-600">当前处于开发模式，拥有所有权限</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  )
}
