import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { AuthProvider } from "@/lib/hooks/use-auth"
import { PermissionProvider } from "@/lib/hooks/use-permissions"
import { Toaster } from "sonner"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "MedData Analytics - 医学数据分析平台",
  description: "专业的医学数据分析平台，支持MIMIC、EICU、NHANES等主流医学数据库",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN">
      <body className={inter.className}>
        <AuthProvider>
          <PermissionProvider>
            {children}
            <Toaster />
          </PermissionProvider>
        </AuthProvider>
      </body>
    </html>
  )
}
