package service

import (
	"context"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"github.com/google/uuid"

	"github.com/chungzy/medical-data-platform/internal/model"
	"github.com/chungzy/medical-data-platform/internal/query"
	"github.com/chungzy/medical-data-platform/internal/repository"
	"github.com/chungzy/medical-data-platform/pkg/logger"
)

// EnhancedExportServiceV2 增强导出服务V2
type EnhancedExportServiceV2 struct {
	queryBuilder   *query.EnhancedQueryBuilder
	exportRepo     repository.ExportRepository
	queryRepo      repository.QueryRepository
	exportDir      string
	maxFileSize    int64 // 最大文件大小（字节）
	supportFormats []string
}

// NewEnhancedExportServiceV2 创建增强导出服务V2
func NewEnhancedExportServiceV2(
	queryBuilder *query.EnhancedQueryBuilder,
	exportRepo repository.ExportRepository,
	queryRepo repository.QueryRepository,
) *EnhancedExportServiceV2 {
	exportDir := "exports"
	os.MkdirAll(exportDir, 0755)

	return &EnhancedExportServiceV2{
		queryBuilder:   queryBuilder,
		exportRepo:     exportRepo,
		queryRepo:      queryRepo,
		exportDir:      exportDir,
		maxFileSize:    100 * 1024 * 1024, // 100MB
		supportFormats: []string{"csv", "json", "excel", "parquet"},
	}
}

// CreateExport 创建导出任务
func (s *EnhancedExportServiceV2) CreateExport(ctx context.Context, userID uuid.UUID, req *ExportRequestV2) (*model.ExportRecord, error) {
	// 验证导出格式
	if !s.isSupportedFormat(req.Format) {
		return nil, fmt.Errorf("unsupported export format: %s", req.Format)
	}

	// 验证查询
	validationResult := s.queryBuilder.ValidateQuery(req.QueryDSL)
	if !validationResult.Valid {
		return nil, fmt.Errorf("invalid query: %v", validationResult.Errors)
	}

	// 估算导出大小
	estimatedSize, err := s.estimateExportSize(req.QueryDSL, req.Format)
	if err != nil {
		logger.Error("Failed to estimate export size", err, map[string]interface{}{
			"user_id": userID,
			"dataset": req.QueryDSL.Dataset,
		})
	}

	// 检查大小限制
	if estimatedSize > s.maxFileSize {
		return nil, fmt.Errorf("estimated export size (%d bytes) exceeds maximum allowed size (%d bytes)", 
			estimatedSize, s.maxFileSize)
	}

	// 创建导出记录
	exportRecord := &model.ExportRecord{
		ID:         uuid.New(),
		UserID:     userID,
		ExportType: req.Format,
		Status:     "pending",
		ExpiresAt:  time.Now().Add(time.Duration(req.ExpirationDays) * 24 * time.Hour),
		CreatedAt:  time.Now(),
	}

	// 保存查询配置
	queryConfig, err := json.Marshal(req.QueryDSL)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal query config: %w", err)
	}

	// 保存导出记录
	if err := s.exportRepo.Create(ctx, exportRecord); err != nil {
		return nil, fmt.Errorf("failed to create export record: %w", err)
	}

	// 异步处理导出
	go s.processExportV2(ctx, exportRecord, req, queryConfig)

	return exportRecord, nil
}

// processExportV2 处理导出任务V2
func (s *EnhancedExportServiceV2) processExportV2(ctx context.Context, record *model.ExportRecord, req *ExportRequestV2, queryConfig []byte) {
	// 更新状态为处理中
	record.Status = "processing"
	s.exportRepo.Update(ctx, record)

	logger.Info("Starting export processing", map[string]interface{}{
		"export_id": record.ID,
		"format":    record.ExportType,
		"dataset":   req.QueryDSL.Dataset,
	})

	// 执行查询
	queryOptions := &query.QueryOptions{
		EnableCache:  false, // 导出时不使用缓存
		WithCount:    true,
		Timeout:      300, // 5分钟超时
		MaxRows:      1000000, // 最大100万行
	}

	result, err := s.queryBuilder.BuildAndExecute(ctx, req.QueryDSL, queryOptions)
	if err != nil {
		s.handleExportError(ctx, record, fmt.Errorf("failed to execute query: %w", err))
		return
	}

	// 检查结果大小
	if len(result.Data) == 0 {
		s.handleExportError(ctx, record, fmt.Errorf("query returned no data"))
		return
	}

	// 根据格式导出数据
	var filePath string
	switch record.ExportType {
	case "csv":
		filePath, err = s.exportToCSVV2(result.Data, req.Fields, record.ID.String(), req.Options)
	case "json":
		filePath, err = s.exportToJSONV2(result.Data, req.Fields, record.ID.String(), req.Options)
	case "excel":
		filePath, err = s.exportToExcel(result.Data, req.Fields, record.ID.String(), req.Options)
	case "parquet":
		filePath, err = s.exportToParquet(result.Data, req.Fields, record.ID.String(), req.Options)
	default:
		err = fmt.Errorf("unsupported export format: %s", record.ExportType)
	}

	if err != nil {
		s.handleExportError(ctx, record, fmt.Errorf("failed to export data: %w", err))
		return
	}

	// 获取文件信息
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		s.handleExportError(ctx, record, fmt.Errorf("failed to get file info: %w", err))
		return
	}

	// 更新导出记录
	record.Status = "completed"
	record.FilePath = &filePath
	fileName := fileInfo.Name()
	record.FileName = &fileName
	fileSize := fileInfo.Size()
	record.FileSize = &fileSize

	if err := s.exportRepo.Update(ctx, record); err != nil {
		logger.Error("Failed to update export record", err, map[string]interface{}{
			"export_id": record.ID,
		})
	}

	logger.Info("Export completed successfully", map[string]interface{}{
		"export_id": record.ID,
		"file_size": fileSize,
		"records":   len(result.Data),
	})
}

// exportToCSVV2 导出为CSV格式V2
func (s *EnhancedExportServiceV2) exportToCSVV2(data []map[string]interface{}, fields []string, exportID string, options *ExportOptions) (string, error) {
	fileName := fmt.Sprintf("export_%s.csv", exportID)
	filePath := filepath.Join(s.exportDir, fileName)

	file, err := os.Create(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	writer := csv.NewWriter(file)
	defer writer.Flush()

	// 确定要导出的字段
	var headers []string
	if len(fields) > 0 {
		headers = fields
	} else {
		// 使用第一行数据的所有字段
		if len(data) > 0 {
			for key := range data[0] {
				headers = append(headers, key)
			}
		}
	}

	// 写入BOM（如果需要）
	if options != nil && options.IncludeBOM {
		file.Write([]byte{0xEF, 0xBB, 0xBF})
	}

	// 写入表头
	if options == nil || options.IncludeHeader {
		if err := writer.Write(headers); err != nil {
			return "", err
		}
	}

	// 写入数据
	for _, row := range data {
		var record []string
		for _, header := range headers {
			value := ""
			if val, exists := row[header]; exists && val != nil {
				value = fmt.Sprintf("%v", val)
			}
			record = append(record, value)
		}
		if err := writer.Write(record); err != nil {
			return "", err
		}
	}

	return filePath, nil
}

// exportToJSONV2 导出为JSON格式V2
func (s *EnhancedExportServiceV2) exportToJSONV2(data []map[string]interface{}, fields []string, exportID string, options *ExportOptions) (string, error) {
	fileName := fmt.Sprintf("export_%s.json", exportID)
	filePath := filepath.Join(s.exportDir, fileName)

	// 过滤字段
	var filteredData []map[string]interface{}
	if len(fields) > 0 {
		for _, row := range data {
			filteredRow := make(map[string]interface{})
			for _, field := range fields {
				if val, exists := row[field]; exists {
					filteredRow[field] = val
				}
			}
			filteredData = append(filteredData, filteredRow)
		}
	} else {
		filteredData = data
	}

	file, err := os.Create(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	encoder := json.NewEncoder(file)
	if options != nil && options.PrettyPrint {
		encoder.SetIndent("", "  ")
	}

	// 根据选项决定输出格式
	if options != nil && options.ArrayFormat {
		if err := encoder.Encode(filteredData); err != nil {
			return "", err
		}
	} else {
		// 逐行JSON格式
		for _, row := range filteredData {
			if err := encoder.Encode(row); err != nil {
				return "", err
			}
		}
	}

	return filePath, nil
}

// exportToExcel 导出为Excel格式
func (s *EnhancedExportServiceV2) exportToExcel(data []map[string]interface{}, fields []string, exportID string, options *ExportOptions) (string, error) {
	// 这里需要使用Excel库，如excelize
	// 为了演示，暂时返回错误
	return "", fmt.Errorf("Excel export not implemented yet")
}

// exportToParquet 导出为Parquet格式
func (s *EnhancedExportServiceV2) exportToParquet(data []map[string]interface{}, fields []string, exportID string, options *ExportOptions) (string, error) {
	// 这里需要使用Parquet库
	// 为了演示，暂时返回错误
	return "", fmt.Errorf("Parquet export not implemented yet")
}

// handleExportError 处理导出错误
func (s *EnhancedExportServiceV2) handleExportError(ctx context.Context, record *model.ExportRecord, err error) {
	record.Status = "failed"
	errorMsg := err.Error()
	record.ErrorMessage = &errorMsg

	if updateErr := s.exportRepo.Update(ctx, record); updateErr != nil {
		logger.Error("Failed to update export record with error", updateErr, map[string]interface{}{
			"export_id": record.ID,
		})
	}

	logger.Error("Export failed", err, map[string]interface{}{
		"export_id": record.ID,
	})
}

// estimateExportSize 估算导出大小
func (s *EnhancedExportServiceV2) estimateExportSize(dsl *model.QueryDSL, format string) (int64, error) {
	// 简化的大小估算
	// 实际应该基于数据类型、行数等进行更精确的估算
	
	// 构建查询计划
	plan, err := s.queryBuilder.BuildQuery(dsl)
	if err != nil {
		return 0, err
	}

	estimatedRows := int64(plan.EstimatedRows)
	estimatedCols := int64(len(dsl.Select))

	var sizePerCell int64
	switch format {
	case "csv":
		sizePerCell = 20 // 平均每个单元格20字节
	case "json":
		sizePerCell = 30 // JSON格式开销更大
	case "excel":
		sizePerCell = 25
	case "parquet":
		sizePerCell = 15 // Parquet压缩效果好
	default:
		sizePerCell = 25
	}

	return estimatedRows * estimatedCols * sizePerCell, nil
}

// isSupportedFormat 检查是否支持的格式
func (s *EnhancedExportServiceV2) isSupportedFormat(format string) bool {
	for _, supported := range s.supportFormats {
		if supported == format {
			return true
		}
	}
	return false
}

// ExportRequestV2 导出请求V2
type ExportRequestV2 struct {
	QueryDSL       *model.QueryDSL `json:"query_dsl" validate:"required"`
	Format         string          `json:"format" validate:"required,oneof=csv json excel parquet"`
	Fields         []string        `json:"fields,omitempty"`
	ExpirationDays int             `json:"expiration_days,omitempty"`
	Options        *ExportOptions  `json:"options,omitempty"`
}

// ExportOptions 导出选项
type ExportOptions struct {
	IncludeHeader bool `json:"include_header"`
	IncludeBOM    bool `json:"include_bom"`
	PrettyPrint   bool `json:"pretty_print"`
	ArrayFormat   bool `json:"array_format"`
	Compression   bool `json:"compression"`
}
