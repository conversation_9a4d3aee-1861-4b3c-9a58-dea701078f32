package query

import (
	"sync"
	"time"

	"github.com/chungzy/medical-data-platform/pkg/logger"
)

// CacheEntry 缓存条目
type CacheEntry struct {
	Result    *QueryResult
	ExpiresAt time.Time
	AccessCount int64
	LastAccess  time.Time
}

// MemoryCache 内存缓存实现
type MemoryCache struct {
	entries    map[string]*CacheEntry
	mutex      sync.RWMutex
	maxSize    int
	defaultTTL time.Duration
	stats      CacheStats
}

// CacheStats 缓存统计
type CacheStats struct {
	Hits        int64 `json:"hits"`
	Misses      int64 `json:"misses"`
	Evictions   int64 `json:"evictions"`
	Size        int   `json:"size"`
	HitRate     float64 `json:"hit_rate"`
}

// NewMemoryCache 创建内存缓存
func NewMemoryCache(maxSize int, defaultTTL time.Duration) *MemoryCache {
	cache := &MemoryCache{
		entries:    make(map[string]*CacheEntry),
		maxSize:    maxSize,
		defaultTTL: defaultTTL,
	}
	
	// 启动清理协程
	go cache.startCleanupRoutine()
	
	return cache
}

// Get 获取缓存项
func (mc *MemoryCache) Get(key string) (*QueryResult, bool) {
	mc.mutex.RLock()
	defer mc.mutex.RUnlock()

	entry, exists := mc.entries[key]
	if !exists {
		mc.stats.Misses++
		mc.updateHitRate()
		return nil, false
	}

	// 检查是否过期
	if time.Now().After(entry.ExpiresAt) {
		mc.mutex.RUnlock()
		mc.mutex.Lock()
		delete(mc.entries, key)
		mc.mutex.Unlock()
		mc.mutex.RLock()
		
		mc.stats.Misses++
		mc.updateHitRate()
		return nil, false
	}

	// 更新访问统计
	entry.AccessCount++
	entry.LastAccess = time.Now()
	
	mc.stats.Hits++
	mc.updateHitRate()

	logger.Debug("Cache hit", map[string]interface{}{
		"key": key,
		"access_count": entry.AccessCount,
	})

	return entry.Result, true
}

// Set 设置缓存项
func (mc *MemoryCache) Set(key string, result *QueryResult, ttl time.Duration) {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	// 如果缓存已满，执行LRU淘汰
	if len(mc.entries) >= mc.maxSize {
		mc.evictLRU()
	}

	// 使用默认TTL如果未指定
	if ttl == 0 {
		ttl = mc.defaultTTL
	}

	entry := &CacheEntry{
		Result:      result,
		ExpiresAt:   time.Now().Add(ttl),
		AccessCount: 1,
		LastAccess:  time.Now(),
	}

	mc.entries[key] = entry
	mc.stats.Size = len(mc.entries)

	logger.Debug("Cache set", map[string]interface{}{
		"key": key,
		"ttl_seconds": ttl.Seconds(),
		"cache_size": mc.stats.Size,
	})
}

// Delete 删除缓存项
func (mc *MemoryCache) Delete(key string) {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	if _, exists := mc.entries[key]; exists {
		delete(mc.entries, key)
		mc.stats.Size = len(mc.entries)
		
		logger.Debug("Cache delete", map[string]interface{}{
			"key": key,
			"cache_size": mc.stats.Size,
		})
	}
}

// Clear 清空缓存
func (mc *MemoryCache) Clear() {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	mc.entries = make(map[string]*CacheEntry)
	mc.stats.Size = 0
	mc.stats.Evictions = 0

	logger.Info("Cache cleared")
}

// evictLRU 淘汰最近最少使用的条目
func (mc *MemoryCache) evictLRU() {
	if len(mc.entries) == 0 {
		return
	}

	var oldestKey string
	var oldestTime time.Time
	
	// 找到最久未访问的条目
	for key, entry := range mc.entries {
		if oldestKey == "" || entry.LastAccess.Before(oldestTime) {
			oldestKey = key
			oldestTime = entry.LastAccess
		}
	}

	if oldestKey != "" {
		delete(mc.entries, oldestKey)
		mc.stats.Evictions++
		mc.stats.Size = len(mc.entries)
		
		logger.Debug("Cache eviction", map[string]interface{}{
			"evicted_key": oldestKey,
			"last_access": oldestTime,
			"cache_size": mc.stats.Size,
		})
	}
}

// startCleanupRoutine 启动清理协程
func (mc *MemoryCache) startCleanupRoutine() {
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		mc.cleanup()
	}
}

// cleanup 清理过期条目
func (mc *MemoryCache) cleanup() {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	now := time.Now()
	expiredKeys := make([]string, 0)

	// 收集过期的键
	for key, entry := range mc.entries {
		if now.After(entry.ExpiresAt) {
			expiredKeys = append(expiredKeys, key)
		}
	}

	// 删除过期条目
	for _, key := range expiredKeys {
		delete(mc.entries, key)
	}

	if len(expiredKeys) > 0 {
		mc.stats.Size = len(mc.entries)
		logger.Info("Cache cleanup completed", map[string]interface{}{
			"expired_count": len(expiredKeys),
			"cache_size": mc.stats.Size,
		})
	}
}

// updateHitRate 更新命中率
func (mc *MemoryCache) updateHitRate() {
	total := mc.stats.Hits + mc.stats.Misses
	if total > 0 {
		mc.stats.HitRate = float64(mc.stats.Hits) / float64(total)
	}
}

// GetStats 获取缓存统计
func (mc *MemoryCache) GetStats() CacheStats {
	mc.mutex.RLock()
	defer mc.mutex.RUnlock()

	stats := mc.stats
	stats.Size = len(mc.entries)
	return stats
}

// GetSize 获取缓存大小
func (mc *MemoryCache) GetSize() int {
	mc.mutex.RLock()
	defer mc.mutex.RUnlock()
	return len(mc.entries)
}

// GetKeys 获取所有缓存键
func (mc *MemoryCache) GetKeys() []string {
	mc.mutex.RLock()
	defer mc.mutex.RUnlock()

	keys := make([]string, 0, len(mc.entries))
	for key := range mc.entries {
		keys = append(keys, key)
	}
	return keys
}

// GetEntryInfo 获取缓存条目信息
func (mc *MemoryCache) GetEntryInfo(key string) (*CacheEntryInfo, bool) {
	mc.mutex.RLock()
	defer mc.mutex.RUnlock()

	entry, exists := mc.entries[key]
	if !exists {
		return nil, false
	}

	info := &CacheEntryInfo{
		Key:         key,
		ExpiresAt:   entry.ExpiresAt,
		AccessCount: entry.AccessCount,
		LastAccess:  entry.LastAccess,
		TTL:         time.Until(entry.ExpiresAt),
		IsExpired:   time.Now().After(entry.ExpiresAt),
	}

	return info, true
}

// CacheEntryInfo 缓存条目信息
type CacheEntryInfo struct {
	Key         string        `json:"key"`
	ExpiresAt   time.Time     `json:"expires_at"`
	AccessCount int64         `json:"access_count"`
	LastAccess  time.Time     `json:"last_access"`
	TTL         time.Duration `json:"ttl"`
	IsExpired   bool          `json:"is_expired"`
}

// SetMaxSize 设置最大缓存大小
func (mc *MemoryCache) SetMaxSize(maxSize int) {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	mc.maxSize = maxSize

	// 如果当前大小超过新的最大值，执行淘汰
	for len(mc.entries) > mc.maxSize {
		mc.evictLRU()
	}
}

// SetDefaultTTL 设置默认TTL
func (mc *MemoryCache) SetDefaultTTL(ttl time.Duration) {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()
	mc.defaultTTL = ttl
}

// Extend 延长缓存项的TTL
func (mc *MemoryCache) Extend(key string, additionalTTL time.Duration) bool {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	entry, exists := mc.entries[key]
	if !exists {
		return false
	}

	entry.ExpiresAt = entry.ExpiresAt.Add(additionalTTL)
	
	logger.Debug("Cache TTL extended", map[string]interface{}{
		"key": key,
		"additional_ttl_seconds": additionalTTL.Seconds(),
		"new_expires_at": entry.ExpiresAt,
	})

	return true
}

// Refresh 刷新缓存项的TTL到默认值
func (mc *MemoryCache) Refresh(key string) bool {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	entry, exists := mc.entries[key]
	if !exists {
		return false
	}

	entry.ExpiresAt = time.Now().Add(mc.defaultTTL)
	entry.LastAccess = time.Now()
	
	logger.Debug("Cache refreshed", map[string]interface{}{
		"key": key,
		"new_expires_at": entry.ExpiresAt,
	})

	return true
}
