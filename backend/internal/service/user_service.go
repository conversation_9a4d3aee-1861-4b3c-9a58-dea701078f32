package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/chungzy/medical-data-platform/internal/model"
	"github.com/chungzy/medical-data-platform/internal/repository"
	"github.com/chungzy/medical-data-platform/pkg/logger"

	"github.com/google/uuid"
)

type UserService struct {
	userRepo        repository.UserRepository
	passwordService *PasswordService
}

func NewUserService(userRepo repository.UserRepository, passwordService *PasswordService) *UserService {
	return &UserService{
		userRepo:        userRepo,
		passwordService: passwordService,
	}
}

// CreateUser 创建用户（增强版）
func (s *UserService) CreateUser(ctx context.Context, req *model.CreateUserRequest) (*model.User, error) {
	// 检查邮箱是否已存在
	existingUser, err := s.userRepo.GetByEmail(ctx, req.Email)
	if err == nil && existingUser != nil {
		return nil, errors.New("邮箱已被注册")
	}

	// 验证并加密密码
	hashedPassword, err := s.passwordService.HashPassword(req.Password)
	if err != nil {
		return nil, fmt.Errorf("密码验证失败: %w", err)
	}

	// 创建用户
	var phone *string
	if req.Phone != "" {
		phone = &req.Phone
	}

	// 设置权限级别
	permission := model.PermissionUser // 默认为普通用户
	if req.Role == "admin" {
		permission = model.PermissionAdmin
	}

	user := &model.User{
		ID:         uuid.New(),
		Name:       req.Name,
		Email:      req.Email,
		Phone:      phone,
		Password:   hashedPassword,
		Role:       req.Role,
		Permission: permission,
		IsActive:   true,
	}

	err = s.userRepo.Create(ctx, user)
	if err != nil {
		return nil, fmt.Errorf("创建用户失败: %w", err)
	}

	// 权限已在创建用户时设置，无需额外操作

	// 清除密码字段
	user.Password = ""
	return user, nil
}

// 简化版本不需要assignDefaultRole方法，权限直接在用户创建时设置

// AuthenticateUser 用户认证
func (s *UserService) AuthenticateUser(ctx context.Context, email, password string) (*model.User, error) {
	user, err := s.userRepo.GetByEmail(ctx, email)
	if err != nil {
		return nil, errors.New("用户不存在")
	}

	// 检查账户是否被锁定
	if user.LockedUntil != nil && user.LockedUntil.After(time.Now()) {
		return nil, errors.New("账户已被锁定，请稍后再试")
	}

	// 验证密码
	err = s.passwordService.VerifyPassword(user.Password, password)
	if err != nil {
		// 增加失败次数
		s.incrementFailedAttempts(ctx, user.ID)
		return nil, errors.New("密码错误")
	}

	// 重置失败次数并更新最后登录时间
	s.resetFailedAttempts(ctx, user.ID)

	// 清除密码字段
	user.Password = ""
	return user, nil
}

// incrementFailedAttempts 增加失败登录次数
func (s *UserService) incrementFailedAttempts(ctx context.Context, userID uuid.UUID) {
	// 这里应该实现失败次数增加和账户锁定逻辑
	// 为了简化，这里只记录日志
	logger.Warn("Failed login attempt", map[string]interface{}{
		"user_id": userID,
	})
}

// resetFailedAttempts 重置失败登录次数
func (s *UserService) resetFailedAttempts(ctx context.Context, userID uuid.UUID) {
	// 这里应该实现重置失败次数的逻辑
	logger.Info("Login successful, reset failed attempts", map[string]interface{}{
		"user_id": userID,
	})
}

// 保持原有的简单方法以兼容现有代码
func (s *UserService) Create(ctx context.Context, user *model.User) error {
	return s.userRepo.Create(ctx, user)
}

func (s *UserService) GetByID(ctx context.Context, id uuid.UUID) (*model.User, error) {
	return s.userRepo.GetByID(ctx, id)
}

func (s *UserService) GetByEmail(ctx context.Context, email string) (*model.User, error) {
	return s.userRepo.GetByEmail(ctx, email)
}

func (s *UserService) Update(ctx context.Context, user *model.User) error {
	return s.userRepo.Update(ctx, user)
}

func (s *UserService) Delete(ctx context.Context, id uuid.UUID) error {
	return s.userRepo.Delete(ctx, id)
}
