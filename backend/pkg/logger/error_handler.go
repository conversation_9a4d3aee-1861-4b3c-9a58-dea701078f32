package logger

import (
	"context"
	"fmt"
	"runtime"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// ErrorWithContext 记录带上下文的错误日志
func ErrorWithContext(ctx context.Context, err error, message string, fields ...logrus.Fields) {
	entry := WithContext(ctx)

	if len(fields) > 0 {
		entry = entry.WithFields(fields[0])
	}

	entry.WithError(err).Error(message)
}

// ErrorWithGinContext 记录带Gin上下文的错误日志
func ErrorWithGinContext(c *gin.Context, err error, message string, extraFields ...logrus.Fields) {
	logID := c.GetString("log_id")
	userID := c.GetString("user_id")
	if userID == "" {
		userID = "anonymous"
	}

	operation := c.GetString("business_operation")
	if operation == "" {
		operation = "unknown"
	}

	// 基础字段
	baseFields := logrus.Fields{
		"log_id":     logID,
		"user_id":    userID,
		"operation":  operation,
		"method":     c.Request.Method,
		"path":       c.Request.URL.Path,
		"client_ip":  c.ClientIP(),
		"user_agent": c.Request.UserAgent(),
	}

	// 合并额外字段
	if len(extraFields) > 0 {
		for k, v := range extraFields[0] {
			baseFields[k] = v
		}
	}

	Logger.WithFields(baseFields).WithError(err).Error(message)
}

// WarnWithContext 记录带上下文的警告日志
func WarnWithContext(ctx context.Context, message string, fields ...logrus.Fields) {
	entry := WithContext(ctx)

	if len(fields) > 0 {
		entry = entry.WithFields(fields[0])
	}

	entry.Warn(message)
}

// WarnWithGinContext 记录带Gin上下文的警告日志
func WarnWithGinContext(c *gin.Context, message string, extraFields ...logrus.Fields) {
	logID := c.GetString("log_id")
	userID := c.GetString("user_id")
	if userID == "" {
		userID = "anonymous"
	}

	operation := c.GetString("business_operation")
	if operation == "" {
		operation = "unknown"
	}

	// 基础字段
	baseFields := logrus.Fields{
		"log_id":    logID,
		"user_id":   userID,
		"operation": operation,
		"method":    c.Request.Method,
		"path":      c.Request.URL.Path,
		"client_ip": c.ClientIP(),
	}

	// 合并额外字段
	if len(extraFields) > 0 {
		for k, v := range extraFields[0] {
			baseFields[k] = v
		}
	}

	Logger.WithFields(baseFields).Warn(message)
}

// InfoWithContext 记录带上下文的信息日志
func InfoWithContext(ctx context.Context, message string, fields ...logrus.Fields) {
	entry := WithContext(ctx)

	if len(fields) > 0 {
		entry = entry.WithFields(fields[0])
	}

	entry.Info(message)
}

// InfoWithGinContext 记录带Gin上下文的信息日志
func InfoWithGinContext(c *gin.Context, message string, extraFields ...logrus.Fields) {
	logID := c.GetString("log_id")
	userID := c.GetString("user_id")
	if userID == "" {
		userID = "anonymous"
	}

	operation := c.GetString("business_operation")
	if operation == "" {
		operation = "unknown"
	}

	// 基础字段
	baseFields := logrus.Fields{
		"log_id":    logID,
		"user_id":   userID,
		"operation": operation,
		"method":    c.Request.Method,
		"path":      c.Request.URL.Path,
		"client_ip": c.ClientIP(),
	}

	// 合并额外字段
	if len(extraFields) > 0 {
		for k, v := range extraFields[0] {
			baseFields[k] = v
		}
	}

	Logger.WithFields(baseFields).Info(message)
}

// HandleDatabaseError 处理数据库错误
func HandleDatabaseError(ctx context.Context, err error, operation string, additionalInfo ...string) error {
	// 获取调用者信息
	_, file, line, ok := runtime.Caller(1)
	caller := "unknown"
	if ok {
		caller = fmt.Sprintf("%s:%d", file, line)
	}

	fields := logrus.Fields{
		"error_type": "database_error",
		"operation":  operation,
		"caller":     caller,
	}

	if len(additionalInfo) > 0 {
		fields["additional_info"] = additionalInfo[0]
	}

	ErrorWithContext(ctx, err, "Database operation failed", fields)

	return fmt.Errorf("database operation failed in %s: %w", operation, err)
}

// HandleServiceError 处理服务层错误
func HandleServiceError(ctx context.Context, err error, service string, method string, additionalInfo ...string) error {
	// 获取调用者信息
	_, file, line, ok := runtime.Caller(1)
	caller := "unknown"
	if ok {
		caller = fmt.Sprintf("%s:%d", file, line)
	}

	fields := logrus.Fields{
		"error_type": "service_error",
		"service":    service,
		"method":     method,
		"caller":     caller,
	}

	if len(additionalInfo) > 0 {
		fields["additional_info"] = additionalInfo[0]
	}

	ErrorWithContext(ctx, err, "Service operation failed", fields)

	return fmt.Errorf("service operation failed in %s.%s: %w", service, method, err)
}

// HandleExternalAPIError 处理外部API错误
func HandleExternalAPIError(ctx context.Context, err error, api string, endpoint string, statusCode int, additionalInfo ...string) error {
	fields := logrus.Fields{
		"error_type":  "external_api_error",
		"api":         api,
		"endpoint":    endpoint,
		"status_code": statusCode,
	}

	if len(additionalInfo) > 0 {
		fields["additional_info"] = additionalInfo[0]
	}

	ErrorWithContext(ctx, err, "External API call failed", fields)

	return fmt.Errorf("external API call failed to %s %s (status: %d): %w", api, endpoint, statusCode, err)
}

// LogBusinessOperation 记录业务操作日志
func LogBusinessOperation(ctx context.Context, operation string, result string, duration int64, additionalInfo ...logrus.Fields) {
	fields := logrus.Fields{
		"operation_type": "business_operation",
		"operation":      operation,
		"result":         result,
		"duration_ms":    duration,
	}

	if len(additionalInfo) > 0 {
		for k, v := range additionalInfo[0] {
			fields[k] = v
		}
	}

	InfoWithContext(ctx, fmt.Sprintf("Business operation completed: %s", operation), fields)
}

// LogBusinessOperationWithGin 记录带Gin上下文的业务操作日志
func LogBusinessOperationWithGin(c *gin.Context, operation string, result string, duration int64, additionalInfo ...logrus.Fields) {
	fields := logrus.Fields{
		"operation_type": "business_operation",
		"operation":      operation,
		"result":         result,
		"duration_ms":    duration,
	}

	if len(additionalInfo) > 0 {
		for k, v := range additionalInfo[0] {
			fields[k] = v
		}
	}

	InfoWithGinContext(c, fmt.Sprintf("Business operation completed: %s", operation), fields)
}
