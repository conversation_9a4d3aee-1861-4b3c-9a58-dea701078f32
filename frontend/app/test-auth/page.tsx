"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useAuth } from "@/lib/hooks/use-auth"
import { usePermissions, PermissionGuard, PERMISSIONS, ROLES, RoleBadge } from "@/lib/hooks/use-permissions"
import { apiClient } from "@/lib/api"
import { toast } from "sonner"

export default function TestAuthPage() {
  const { user, isAuthenticated, login, register, logout } = useAuth()
  const { permissions, hasPermission, hasRole, isAdmin } = usePermissions()
  const [testCredentials, setTestCredentials] = useState({
    email: "<EMAIL>",
    password: "test123456"
  })
  const [testResult, setTestResult] = useState<any>(null)

  const handleQuickLogin = async () => {
    try {
      const result = await login(testCredentials.email, testCredentials.password)
      if (result.success) {
        toast.success("登录成功！")
      } else {
        toast.error(result.error || "登录失败")
      }
    } catch (error) {
      toast.error("网络错误")
    }
  }

  const handleQuickRegister = async () => {
    try {
      const result = await register({
        name: "测试用户",
        email: testCredentials.email,
        password: testCredentials.password,
        role: "user"
      })
      if (result.success) {
        toast.success("注册成功！")
      } else {
        toast.error(result.error || "注册失败")
      }
    } catch (error) {
      toast.error("网络错误")
    }
  }

  const testMedicalQueryAPI = async () => {
    try {
      const queryDSL = {
        dataset_id: "mimic-iv",
        study_name: "测试查询_" + Date.now(),
        description: "通过测试页面创建的查询",
        cohort: {
          criteria: [
            {
              id: Date.now().toString(),
              type: "demographic",
              field: "age",
              operator: "between",
              value: ["18", "65"],
              label: "年龄 18-65 岁"
            }
          ],
          estimatedSize: null
        },
        fields: [
          {
            field: {
              id: "heart_rate",
              name: "心率",
              nameEn: "heart_rate",
              type: "numeric",
              description: "患者心率",
              table: "chartevents",
              category: "clinical_measurements",
              unit: "bpm"
            }
          }
        ],
        filters: [],
        time_range: null,
        group_by: [],
        order_by: [],
        limit: 100
      }

      const response = await apiClient.executeMedicalQuery(queryDSL)
      setTestResult(response)
      
      if (response.data) {
        toast.success("API调用成功！")
      } else {
        toast.error(response.error || "API调用失败")
      }
    } catch (error) {
      toast.error("API调用出错")
      setTestResult({ error: error.message })
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <h1 className="text-3xl font-bold">认证测试页面</h1>
      
      {/* 当前用户状态 */}
      <Card>
        <CardHeader>
          <CardTitle>当前用户状态</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <p><strong>认证状态:</strong> {isAuthenticated ? "已登录" : "未登录"}</p>
            {user && (
              <>
                <p><strong>用户ID:</strong> {user.id}</p>
                <p><strong>姓名:</strong> {user.name}</p>
                <p><strong>邮箱:</strong> {user.email}</p>
                <div className="flex items-center gap-2">
                  <strong>角色:</strong>
                  <RoleBadge role={user.role as any} />
                </div>
                <p><strong>是否管理员:</strong> {isAdmin() ? "是" : "否"}</p>
              </>
            )}
            <p><strong>Token:</strong> {localStorage.getItem("auth_token") ? "已存储" : "未存储"}</p>
          </div>
        </CardContent>
      </Card>

      {/* 权限测试 */}
      {isAuthenticated && permissions && (
        <Card>
          <CardHeader>
            <CardTitle>权限测试</CardTitle>
            <CardDescription>测试当前用户的权限和角色</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">权限信息:</h4>
              <div className="space-y-2">
                <div className="text-sm">
                  <strong>权限级别:</strong> {permissions.permission}
                  ({permissions.permission === 1 ? "管理员" : permissions.permission === 2 ? "普通用户" : "无权限"})
                </div>
                <div>
                  <strong>可用权限:</strong>
                  <div className="grid grid-cols-2 gap-2 mt-1">
                    {permissions.permissions.map((permission, index) => (
                      <div key={index} className="text-sm bg-green-100 text-green-800 px-2 py-1 rounded">
                        {permission}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-2">权限检查测试:</h4>
              <div className="space-y-2">
                <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <span>查询执行权限:</span>
                  <span className={hasPermission(PERMISSIONS.QUERY_EXECUTE) ? "text-green-600" : "text-red-600"}>
                    {hasPermission(PERMISSIONS.QUERY_EXECUTE) ? "✓ 有权限" : "✗ 无权限"}
                  </span>
                </div>
                <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <span>用户管理权限:</span>
                  <span className={hasPermission(PERMISSIONS.USER_READ) ? "text-green-600" : "text-red-600"}>
                    {hasPermission(PERMISSIONS.USER_READ) ? "✓ 有权限" : "✗ 无权限"}
                  </span>
                </div>
                <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <span>系统管理权限:</span>
                  <span className={hasPermission(PERMISSIONS.SYSTEM_ADMIN) ? "text-green-600" : "text-red-600"}>
                    {hasPermission(PERMISSIONS.SYSTEM_ADMIN) ? "✓ 有权限" : "✗ 无权限"}
                  </span>
                </div>
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-2">组件权限测试:</h4>
              <div className="space-y-2">
                <PermissionGuard
                  permission={PERMISSIONS.USER_READ}
                  fallback={<div className="text-red-600">您没有用户管理权限</div>}
                >
                  <div className="text-green-600">✓ 您可以查看用户管理功能</div>
                </PermissionGuard>

                <PermissionGuard
                  role={ROLES.ADMIN}
                  fallback={<div className="text-red-600">您不是管理员</div>}
                >
                  <div className="text-green-600">✓ 您是系统管理员</div>
                </PermissionGuard>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
          </div>
        </CardContent>
      </Card>

      {/* 快速登录/注册 */}
      <Card>
        <CardHeader>
          <CardTitle>快速测试</CardTitle>
          <CardDescription>使用预设的测试账户进行登录/注册</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="email">邮箱</Label>
              <Input
                id="email"
                value={testCredentials.email}
                onChange={(e) => setTestCredentials({...testCredentials, email: e.target.value})}
              />
            </div>
            <div>
              <Label htmlFor="password">密码</Label>
              <Input
                id="password"
                type="password"
                value={testCredentials.password}
                onChange={(e) => setTestCredentials({...testCredentials, password: e.target.value})}
              />
            </div>
          </div>
          
          <div className="flex gap-2">
            <Button onClick={handleQuickRegister}>注册测试账户</Button>
            <Button onClick={handleQuickLogin} variant="outline">登录</Button>
            <Button onClick={logout} variant="destructive">登出</Button>
          </div>
        </CardContent>
      </Card>

      {/* API测试 */}
      {isAuthenticated && (
        <Card>
          <CardHeader>
            <CardTitle>API测试</CardTitle>
            <CardDescription>测试医疗查询API是否正常工作</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button onClick={testMedicalQueryAPI}>测试医疗查询API</Button>
            
            {testResult && (
              <div className="mt-4">
                <h4 className="font-medium mb-2">API响应:</h4>
                <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-64">
                  {JSON.stringify(testResult, null, 2)}
                </pre>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* 调试信息 */}
      <Card>
        <CardHeader>
          <CardTitle>调试信息</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <p><strong>API Base URL:</strong> {process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8088/api"}</p>
            <p><strong>当前页面:</strong> /test-auth</p>
            <p><strong>localStorage Token:</strong> {typeof window !== "undefined" ? localStorage.getItem("auth_token")?.substring(0, 50) + "..." : "N/A"}</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
