"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ScrollArea } from "@/components/ui/scroll-area"
import { 
  Filter, 
  Plus, 
  X,
  Trash2,
  <PERSON><PERSON>,
  Settings,
  AlertCircle,
  CheckCircle2
} from "lucide-react"
import { toast } from "sonner"

interface Dataset {
  id: string
  name: string
  description: string
  version: string
  tableCount: number
  features: string[]
  status: "healthy" | "warning" | "error"
}

interface FilterCondition {
  id: string
  field: string
  table: string
  operator: string
  value: any
  values?: any[]
  logic: "AND" | "OR"
  dataType: string
}

interface FilterGroup {
  id: string
  logic: "AND" | "OR"
  conditions: FilterCondition[]
  groups: FilterGroup[]
}

interface FilterBuilderProps {
  dataset: Dataset | null
  filters: FilterCondition[]
  onFiltersChange: (filters: FilterCondition[]) => void
  availableFields?: any[]
}

export function FilterBuilder({ 
  dataset, 
  filters, 
  onFiltersChange, 
  availableFields = [] 
}: FilterBuilderProps) {
  const [filterGroups, setFilterGroups] = useState<FilterGroup[]>([])
  const [selectedField, setSelectedField] = useState("")
  const [selectedOperator, setSelectedOperator] = useState("")
  const [filterValue, setFilterValue] = useState("")
  const [filterLogic, setFilterLogic] = useState<"AND" | "OR">("AND")

  // 操作符定义
  const operators = {
    string: [
      { value: "=", label: "等于" },
      { value: "!=", label: "不等于" },
      { value: "LIKE", label: "包含" },
      { value: "NOT_LIKE", label: "不包含" },
      { value: "IN", label: "在列表中" },
      { value: "NOT_IN", label: "不在列表中" },
      { value: "IS_NULL", label: "为空" },
      { value: "IS_NOT_NULL", label: "不为空" }
    ],
    number: [
      { value: "=", label: "等于" },
      { value: "!=", label: "不等于" },
      { value: ">", label: "大于" },
      { value: ">=", label: "大于等于" },
      { value: "<", label: "小于" },
      { value: "<=", label: "小于等于" },
      { value: "BETWEEN", label: "在范围内" },
      { value: "NOT_BETWEEN", label: "不在范围内" },
      { value: "IS_NULL", label: "为空" },
      { value: "IS_NOT_NULL", label: "不为空" }
    ],
    date: [
      { value: "=", label: "等于" },
      { value: "!=", label: "不等于" },
      { value: ">", label: "晚于" },
      { value: ">=", label: "不早于" },
      { value: "<", label: "早于" },
      { value: "<=", label: "不晚于" },
      { value: "BETWEEN", label: "在日期范围内" },
      { value: "IS_NULL", label: "为空" },
      { value: "IS_NOT_NULL", label: "不为空" }
    ],
    boolean: [
      { value: "=", label: "等于" },
      { value: "IS_NULL", label: "为空" },
      { value: "IS_NOT_NULL", label: "不为空" }
    ]
  }

  // 获取字段的数据类型
  const getFieldDataType = (fieldName: string): string => {
    const field = availableFields.find(f => f.name === fieldName || f.id === fieldName)
    if (!field) return "string"
    
    const type = field.type || field.dataType || "string"
    if (type.includes("int") || type.includes("float") || type.includes("decimal") || type.includes("numeric")) {
      return "number"
    }
    if (type.includes("date") || type.includes("time") || type.includes("timestamp")) {
      return "date"
    }
    if (type.includes("bool")) {
      return "boolean"
    }
    return "string"
  }

  // 获取可用操作符
  const getAvailableOperators = (fieldName: string) => {
    const dataType = getFieldDataType(fieldName)
    return operators[dataType as keyof typeof operators] || operators.string
  }

  // 添加过滤条件
  const addFilter = () => {
    if (!selectedField || !selectedOperator) {
      toast.error("请选择字段和操作符")
      return
    }

    // 验证值
    if (!["IS_NULL", "IS_NOT_NULL"].includes(selectedOperator) && !filterValue) {
      toast.error("请输入过滤值")
      return
    }

    const newFilter: FilterCondition = {
      id: Date.now().toString(),
      field: selectedField,
      table: getFieldTable(selectedField),
      operator: selectedOperator,
      value: parseFilterValue(filterValue, selectedOperator),
      logic: filterLogic,
      dataType: getFieldDataType(selectedField)
    }

    // 处理特殊操作符
    if (selectedOperator === "IN" || selectedOperator === "NOT_IN") {
      newFilter.values = filterValue.split(",").map(v => v.trim())
      delete newFilter.value
    }

    if (selectedOperator === "BETWEEN" || selectedOperator === "NOT_BETWEEN") {
      const values = filterValue.split(",").map(v => v.trim())
      if (values.length !== 2) {
        toast.error("范围查询需要两个值，用逗号分隔")
        return
      }
      newFilter.values = values
      delete newFilter.value
    }

    onFiltersChange([...filters, newFilter])
    
    // 重置表单
    setSelectedField("")
    setSelectedOperator("")
    setFilterValue("")
    
    toast.success("已添加过滤条件")
  }

  // 解析过滤值
  const parseFilterValue = (value: string, operator: string) => {
    if (["IS_NULL", "IS_NOT_NULL"].includes(operator)) {
      return null
    }
    
    // 尝试解析为数字
    const numValue = Number(value)
    if (!isNaN(numValue) && value.trim() !== "") {
      return numValue
    }
    
    return value
  }

  // 获取字段所属表
  const getFieldTable = (fieldName: string): string => {
    const field = availableFields.find(f => f.name === fieldName || f.id === fieldName)
    return field?.table || "unknown"
  }

  // 移除过滤条件
  const removeFilter = (filterId: string) => {
    const updatedFilters = filters.filter(f => f.id !== filterId)
    onFiltersChange(updatedFilters)
    toast.success("已移除过滤条件")
  }

  // 复制过滤条件
  const duplicateFilter = (filter: FilterCondition) => {
    const newFilter: FilterCondition = {
      ...filter,
      id: Date.now().toString()
    }
    onFiltersChange([...filters, newFilter])
    toast.success("已复制过滤条件")
  }

  // 更新过滤条件逻辑
  const updateFilterLogic = (filterId: string, logic: "AND" | "OR") => {
    const updatedFilters = filters.map(f => 
      f.id === filterId ? { ...f, logic } : f
    )
    onFiltersChange(updatedFilters)
  }

  // 渲染值输入组件
  const renderValueInput = () => {
    if (["IS_NULL", "IS_NOT_NULL"].includes(selectedOperator)) {
      return null
    }

    const dataType = getFieldDataType(selectedField)
    const isRange = ["BETWEEN", "NOT_BETWEEN"].includes(selectedOperator)
    const isList = ["IN", "NOT_IN"].includes(selectedOperator)

    let placeholder = "输入值"
    if (isRange) {
      placeholder = "输入两个值，用逗号分隔"
    } else if (isList) {
      placeholder = "输入多个值，用逗号分隔"
    } else if (dataType === "date") {
      placeholder = "YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS"
    } else if (dataType === "number") {
      placeholder = "输入数字"
    }

    if (dataType === "date" && !isRange && !isList) {
      return (
        <Input
          type="datetime-local"
          value={filterValue}
          onChange={(e) => setFilterValue(e.target.value)}
        />
      )
    }

    if (dataType === "boolean") {
      return (
        <Select value={filterValue} onValueChange={setFilterValue}>
          <SelectTrigger>
            <SelectValue placeholder="选择布尔值" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="true">True</SelectItem>
            <SelectItem value="false">False</SelectItem>
          </SelectContent>
        </Select>
      )
    }

    return (
      <Input
        type={dataType === "number" && !isRange && !isList ? "number" : "text"}
        placeholder={placeholder}
        value={filterValue}
        onChange={(e) => setFilterValue(e.target.value)}
      />
    )
  }

  // 格式化过滤条件显示
  const formatFilterDisplay = (filter: FilterCondition) => {
    const field = availableFields.find(f => f.name === filter.field || f.id === filter.field)
    const fieldName = field?.name || filter.field
    const operatorLabel = getAvailableOperators(filter.field).find(op => op.value === filter.operator)?.label || filter.operator

    let valueDisplay = ""
    if (filter.values) {
      valueDisplay = filter.values.join(", ")
    } else if (filter.value !== null && filter.value !== undefined) {
      valueDisplay = String(filter.value)
    }

    return `${fieldName} ${operatorLabel} ${valueDisplay}`
  }

  if (!dataset) {
    return (
      <div className="text-center py-12">
        <Filter className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">请先选择数据集</h3>
        <p className="text-gray-600">
          选择数据集后才能设置过滤条件
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900">设置过滤条件</h2>
        <p className="text-gray-600 mt-1">
          为 {dataset.name} 添加数据过滤条件，精确控制查询结果
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 左侧：添加过滤条件 */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Plus className="h-5 w-5" />
                添加过滤条件
              </CardTitle>
              <CardDescription>
                选择字段、操作符和值来创建过滤条件
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* 字段选择 */}
              <div>
                <Label htmlFor="field-select">字段</Label>
                <Select value={selectedField} onValueChange={setSelectedField}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择字段" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableFields.map((field) => (
                      <SelectItem key={field.id || field.name} value={field.name || field.id}>
                        {field.name} ({field.table})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* 操作符选择 */}
              {selectedField && (
                <div>
                  <Label htmlFor="operator-select">操作符</Label>
                  <Select value={selectedOperator} onValueChange={setSelectedOperator}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择操作符" />
                    </SelectTrigger>
                    <SelectContent>
                      {getAvailableOperators(selectedField).map((op) => (
                        <SelectItem key={op.value} value={op.value}>
                          {op.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              {/* 值输入 */}
              {selectedField && selectedOperator && (
                <div>
                  <Label htmlFor="value-input">值</Label>
                  {renderValueInput()}
                </div>
              )}

              {/* 逻辑操作符 */}
              {filters.length > 0 && (
                <div>
                  <Label htmlFor="logic-select">与前面条件的关系</Label>
                  <Select value={filterLogic} onValueChange={(value: "AND" | "OR") => setFilterLogic(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="AND">AND (且)</SelectItem>
                      <SelectItem value="OR">OR (或)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}

              <Button 
                onClick={addFilter} 
                className="w-full"
                disabled={!selectedField || !selectedOperator}
              >
                <Plus className="h-4 w-4 mr-2" />
                添加过滤条件
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* 右侧：已设置的过滤条件 */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>过滤条件</span>
                <Badge variant="outline">
                  {filters.length}
                </Badge>
              </CardTitle>
              <CardDescription>
                当前查询的过滤条件列表
              </CardDescription>
            </CardHeader>
            <CardContent>
              {filters.length === 0 ? (
                <p className="text-gray-500 text-sm text-center py-4">
                  暂无过滤条件
                </p>
              ) : (
                <ScrollArea className="h-64">
                  <div className="space-y-3">
                    {filters.map((filter, index) => (
                      <div key={filter.id} className="space-y-2">
                        {/* 逻辑操作符显示 */}
                        {index > 0 && (
                          <div className="flex items-center justify-center">
                            <Select 
                              value={filter.logic} 
                              onValueChange={(value: "AND" | "OR") => updateFilterLogic(filter.id, value)}
                            >
                              <SelectTrigger className="w-20 h-6 text-xs">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="AND">AND</SelectItem>
                                <SelectItem value="OR">OR</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        )}

                        {/* 过滤条件卡片 */}
                        <div className="p-3 bg-gray-50 rounded-lg border">
                          <div className="flex items-start justify-between mb-2">
                            <div className="flex-1">
                              <div className="text-sm font-medium">
                                {formatFilterDisplay(filter)}
                              </div>
                              <div className="text-xs text-gray-600 mt-1">
                                表: {filter.table} • 类型: {filter.dataType}
                              </div>
                            </div>
                            
                            <div className="flex items-center gap-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => duplicateFilter(filter)}
                              >
                                <Copy className="h-3 w-3" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => removeFilter(filter.id)}
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              )}
            </CardContent>
          </Card>

          {/* 过滤条件预览 */}
          {filters.length > 0 && (
            <Card className="mt-4">
              <CardHeader>
                <CardTitle className="text-sm">SQL 预览</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-xs font-mono bg-gray-100 p-2 rounded">
                  WHERE {filters.map((filter, index) => {
                    const prefix = index > 0 ? ` ${filter.logic} ` : ""
                    return `${prefix}${filter.field} ${filter.operator} ${filter.value || filter.values?.join(",") || "NULL"}`
                  }).join("")}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
