package api

import (
	"net/http"
	"strconv"

	"github.com/chungzy/medical-data-platform/internal/service"
	"github.com/gin-gonic/gin"
)

type EnhancedQueryController struct {
	queryService  service.QueryService
	exportService service.ExportService
}

func NewEnhancedQueryController(queryService service.QueryService, exportService service.ExportService) *EnhancedQueryController {
	return &EnhancedQueryController{
		queryService:  queryService,
		exportService: exportService,
	}
}

// 复杂查询配置结构
type EnhancedQueryRequest struct {
	Dataset              string           `json:"dataset" binding:"required"`
	MainTable            string           `json:"mainTable" binding:"required"`
	CohortConditions     []QueryCondition `json:"cohortConditions"`
	Joins                []TableJoin      `json:"joins"`
	SelectedFields       []FieldSelection `json:"selectedFields" binding:"required"`
	AdditionalConditions []QueryCondition `json:"additionalConditions"`
	OrderBy              []OrderByClause  `json:"orderBy"`
	Limit                int              `json:"limit"`
	Offset               int              `json:"offset"`
}

type QueryCondition struct {
	Field    string `json:"field" binding:"required"`
	Operator string `json:"operator" binding:"required"`
	Value    string `json:"value" binding:"required"`
	Logic    string `json:"logic"` // AND, OR
}

type TableJoin struct {
	Table string `json:"table" binding:"required"`
	Type  string `json:"type" binding:"required"` // INNER, LEFT, RIGHT, FULL
	On    string `json:"on" binding:"required"`
	Alias string `json:"alias"`
}

type FieldSelection struct {
	ID     string `json:"id"`
	Name   string `json:"name"`
	NameEn string `json:"nameEn"`
	Table  string `json:"table"`
	Type   string `json:"type"`
	Alias  string `json:"alias"`
}

type OrderByClause struct {
	Field     string `json:"field"`
	Direction string `json:"direction"` // ASC, DESC
}

// SQL生成响应
type SQLGenerationResponse struct {
	SQL           string                 `json:"sql"`
	CohortSQL     string                 `json:"cohortSQL"`
	MainSQL       string                 `json:"mainSQL"`
	EstimatedRows int                    `json:"estimatedRows"`
	Warnings      []string               `json:"warnings"`
	Metadata      map[string]interface{} `json:"metadata"`
}

// 查询执行响应
type EnhancedQueryResponse struct {
	QueryID       string                   `json:"queryId"`
	Data          []map[string]interface{} `json:"data"`
	Total         int                      `json:"total"`
	CohortSize    int                      `json:"cohortSize"`
	ExecutionTime int                      `json:"executionTime"`
	Status        string                   `json:"status"`
	Metadata      map[string]interface{}   `json:"metadata"`
}

// 生成SQL查询
func (c *EnhancedQueryController) GenerateSQL(ctx *gin.Context) {
	var req EnhancedQueryRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 验证查询配置
	if err := c.validateQueryRequest(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 1. 生成人群筛选子查询
	cohortSQL := c.buildCohortQuery(&req)

	// 2. 生成主查询SQL
	mainSQL := c.buildMainQuery(&req, cohortSQL)

	// 3. 估算结果行数
	estimatedRows := c.estimateRowCount(&req)

	// 4. 生成警告信息
	warnings := c.generateWarnings(&req)

	response := SQLGenerationResponse{
		SQL:           mainSQL,
		CohortSQL:     cohortSQL,
		MainSQL:       mainSQL,
		EstimatedRows: estimatedRows,
		Warnings:      warnings,
		Metadata: map[string]interface{}{
			"tables":       c.getInvolvedTables(&req),
			"complexity":   c.calculateQueryComplexity(&req),
			"optimization": c.getOptimizationSuggestions(&req),
		},
	}

	ctx.JSON(http.StatusOK, response)
}

// 执行增强查询
func (c *EnhancedQueryController) ExecuteEnhancedQuery(ctx *gin.Context) {
	var req EnhancedQueryRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	userID := ctx.GetString("user_id")

	// 生成并执行查询
	result, err := c.queryService.ExecuteEnhancedQuery(userID, &req)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, result)
}

// 获取表关系图
func (c *EnhancedQueryController) GetTableRelationships(ctx *gin.Context) {
	dataset := ctx.Param("dataset")

	relationships, err := c.queryService.GetTableRelationships(dataset)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"relationships": relationships})
}

// 获取智能字段推荐
func (c *EnhancedQueryController) GetFieldRecommendations(ctx *gin.Context) {
	dataset := ctx.Query("dataset")
	cohort := ctx.Query("cohort")

	recommendations, err := c.queryService.GetFieldRecommendations(dataset, cohort)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"recommendations": recommendations})
}

// 导出增强查询结果
func (c *EnhancedQueryController) ExportEnhancedResults(ctx *gin.Context) {
	var req struct {
		QueryID string                 `json:"queryId" binding:"required"`
		Format  string                 `json:"format" binding:"required"` // excel, csv, json
		Options map[string]interface{} `json:"options"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	userID := ctx.GetString("user_id")

	// 创建导出任务
	exportRecord, err := c.exportService.CreateEnhancedExport(userID, req.QueryID, req.Format, req.Options)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusCreated, exportRecord)
}

// 私有方法：构建人群筛选查询
func (c *EnhancedQueryController) buildCohortQuery(req *EnhancedQueryRequest) string {
	if len(req.CohortConditions) == 0 {
		return ""
	}

	sql := "WITH cohort AS (\n"
	sql += "  SELECT DISTINCT subject_id\n"
	sql += "  FROM " + req.MainTable + "\n"

	if len(req.CohortConditions) > 0 {
		sql += "  WHERE "
		for i, condition := range req.CohortConditions {
			if i > 0 && condition.Logic != "" {
				sql += " " + condition.Logic + " "
			}
			sql += condition.Field + " " + condition.Operator + " '" + condition.Value + "'"
		}
	}

	sql += "\n)\n"
	return sql
}

// 私有方法：构建主查询
func (c *EnhancedQueryController) buildMainQuery(req *EnhancedQueryRequest, cohortSQL string) string {
	sql := cohortSQL

	// SELECT 子句
	sql += "SELECT "
	fields := make([]string, 0, len(req.SelectedFields))
	for _, field := range req.SelectedFields {
		fieldExpr := field.Table + "." + field.NameEn
		if field.Alias != "" {
			fieldExpr += " AS " + field.Alias
		}
		fields = append(fields, fieldExpr)
	}
	sql += join(fields, ", ") + "\n"

	// FROM 子句
	if cohortSQL != "" {
		sql += "FROM cohort c\n"
		sql += "JOIN " + req.MainTable + " ON c.subject_id = " + req.MainTable + ".subject_id\n"
	} else {
		sql += "FROM " + req.MainTable + "\n"
	}

	// JOIN 子句
	for _, joinClause := range req.Joins {
		sql += joinClause.Type + " JOIN " + joinClause.Table
		if joinClause.Alias != "" {
			sql += " " + joinClause.Alias
		}
		sql += " ON " + joinClause.On + "\n"
	}

	// WHERE 子句（附加条件）
	if len(req.AdditionalConditions) > 0 {
		sql += "WHERE "
		for i, condition := range req.AdditionalConditions {
			if i > 0 && condition.Logic != "" {
				sql += " " + condition.Logic + " "
			}
			sql += condition.Field + " " + condition.Operator + " '" + condition.Value + "'"
		}
		sql += "\n"
	}

	// ORDER BY 子句
	if len(req.OrderBy) > 0 {
		sql += "ORDER BY "
		orderFields := make([]string, 0, len(req.OrderBy))
		for _, order := range req.OrderBy {
			orderFields = append(orderFields, order.Field+" "+order.Direction)
		}
		sql += join(orderFields, ", ") + "\n"
	}

	// LIMIT 子句
	if req.Limit > 0 {
		sql += "LIMIT " + strconv.Itoa(req.Limit)
		if req.Offset > 0 {
			sql += " OFFSET " + strconv.Itoa(req.Offset)
		}
		sql += "\n"
	}

	return sql
}

// 私有方法：验证查询请求
func (c *EnhancedQueryController) validateQueryRequest(req *EnhancedQueryRequest) error {
	// 验证数据集存在
	// 验证表存在
	// 验证字段存在
	// 验证关联条件
	return nil
}

// 私有方法：估算行数
func (c *EnhancedQueryController) estimateRowCount(req *EnhancedQueryRequest) int {
	// 基于统计信息估算结果行数
	return 1000 // 示例
}

// 私有方法：生成警告
func (c *EnhancedQueryController) generateWarnings(req *EnhancedQueryRequest) []string {
	warnings := []string{}

	if len(req.Joins) > 5 {
		warnings = append(warnings, "查询涉及过多表关联，可能影响性能")
	}

	if req.Limit == 0 || req.Limit > 10000 {
		warnings = append(warnings, "建议设置合理的查询限制以避免返回过多数据")
	}

	return warnings
}

// 私有方法：获取涉及的表
func (c *EnhancedQueryController) getInvolvedTables(req *EnhancedQueryRequest) []string {
	tables := []string{req.MainTable}
	for _, join := range req.Joins {
		tables = append(tables, join.Table)
	}
	return tables
}

// 私有方法：计算查询复杂度
func (c *EnhancedQueryController) calculateQueryComplexity(req *EnhancedQueryRequest) string {
	score := 0
	score += len(req.CohortConditions)
	score += len(req.Joins) * 2
	score += len(req.SelectedFields)
	score += len(req.AdditionalConditions)

	if score <= 5 {
		return "简单"
	} else if score <= 15 {
		return "中等"
	} else {
		return "复杂"
	}
}

// 私有方法：获取优化建议
func (c *EnhancedQueryController) getOptimizationSuggestions(req *EnhancedQueryRequest) []string {
	suggestions := []string{}

	if len(req.Joins) > 3 {
		suggestions = append(suggestions, "考虑减少表关联或使用索引优化")
	}

	if req.Limit == 0 {
		suggestions = append(suggestions, "建议添加LIMIT子句限制结果数量")
	}

	return suggestions
}

// 工具函数：字符串数组连接
func join(elements []string, separator string) string {
	if len(elements) == 0 {
		return ""
	}

	result := elements[0]
	for i := 1; i < len(elements); i++ {
		result += separator + elements[i]
	}
	return result
}

// 增强查询构建器
type EnhancedQueryBuilder struct {
	dataset   string
	mainTable string
}

// 注册路由
func (c *EnhancedQueryController) RegisterRoutes(router *gin.RouterGroup) {
	enhanced := router.Group("/enhanced-queries")
	{
		enhanced.POST("/generate-sql", c.GenerateSQL)
		enhanced.POST("/execute", c.ExecuteEnhancedQuery)
		enhanced.GET("/table-relationships/:dataset", c.GetTableRelationships)
		enhanced.GET("/field-recommendations", c.GetFieldRecommendations)
		enhanced.POST("/export", c.ExportEnhancedResults)
	}
}
